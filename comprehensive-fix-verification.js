const fs = require('fs');
const axios = require('axios');

async function comprehensiveFixVerification() {
  console.log('🔍 COMPREHENSIVE FIX VERIFICATION');
  console.log('==================================');
  
  // Test 1: Generate a PDF with different airlines for aircraft verification
  const testData = {
    bookingReference: 'VERIFY123',
    passengers: [
      { firstName: '<PERSON>', lastName: 'Smith' },
      { firstName: 'Jane', lastName: 'Doe' }
    ],
    outboundFlight: {
      airline: 'RYANAIR',
      flightNumber: 'FR 1234',
      departure: {
        code: 'LHR',
        city: 'London, United Kingdom',
        time: '2025-07-20T10:30:00Z',
        terminal: '2'
      },
      arrival: {
        code: 'BCN',
        city: 'Barcelona, Spain',
        time: '2025-07-20T13:45:00Z',
        terminal: '1'
      },
      duration: '2h 15m'
    },
    returnFlight: {
      airline: 'EASYJET',
      flightNumber: 'U2 5678',
      departure: {
        code: 'BCN',
        city: 'Barcelona, Spain',
        time: '2025-07-27T15:20:00Z',
        terminal: '1'
      },
      arrival: {
        code: 'LHR',
        city: 'London, United Kingdom',
        time: '2025-07-27T16:35:00Z',
        terminal: '2'
      },
      duration: '2h 15m'
    },
    totalPrice: 9.98
  };

  try {
    console.log('📤 Generating test PDF...');
    const response = await axios.post('http://localhost:5001/api/tickets/generate', testData);
    
    if (response.data.success) {
      console.log('✅ PDF generation successful');
      
      // Download the PDF
      const downloadResponse = await axios.get(`http://localhost:5001${response.data.downloadUrl}`, {
        responseType: 'arraybuffer'
      });
      
      const filename = 'comprehensive-verification.pdf';
      fs.writeFileSync(filename, downloadResponse.data);
      console.log(`📄 PDF saved: ${filename}`);
      
      // Analyze PDF content
      const pdfContent = downloadResponse.data.toString('latin1');
      
      console.log('\n🔍 VERIFICATION RESULTS:');
      console.log('========================');
      
      // Check 1: Reservation Code
      const titleMatch = pdfContent.match(/Flight Reservation - ([A-Z0-9]+)/);
      if (titleMatch) {
        const code = titleMatch[1];
        const length = code.length;
        if (length >= 6 && length <= 7) {
          console.log(`✅ Issue 1 - Reservation Code: ${code} (${length} chars) - FIXED`);
        } else {
          console.log(`❌ Issue 1 - Reservation Code: ${code} (${length} chars) - NOT FIXED`);
        }
      } else {
        console.log('❌ Issue 1 - Reservation Code: Not found - NOT FIXED');
      }
      
      // Check 2: Aircraft Information
      const aircraftPattern = /AIRCRAFT:<\/td>\s*<td[^>]*>([^<]+)<\/td>/g;
      const aircraftMatches = [...pdfContent.matchAll(aircraftPattern)];
      
      if (aircraftMatches.length > 0) {
        console.log(`✅ Issue 2 - Aircraft Information Found (${aircraftMatches.length} segments):`);
        aircraftMatches.forEach((match, i) => {
          const aircraft = match[1].trim();
          console.log(`   Segment ${i + 1}: ${aircraft}`);
        });
        
        if (aircraftMatches.length > 1) {
          const aircraft1 = aircraftMatches[0][1].trim();
          const aircraft2 = aircraftMatches[1][1].trim();
          if (aircraft1 !== aircraft2) {
            console.log('✅ Issue 2 - Different aircraft for different airlines - FIXED');
          } else {
            console.log('⚠️  Issue 2 - Same aircraft for both segments - NEEDS VERIFICATION');
          }
        }
      } else {
        console.log('❌ Issue 2 - Aircraft Information: Not found - NOT FIXED');
      }
      
      // Check 3: Important Information
      if (pdfContent.includes('Important Information')) {
        console.log('✅ Issue 3 - Important Information section found');
        
        if (pdfContent.includes('non-refundable flight itinerary')) {
          console.log('✅ Issue 3 - Updated Important Information content - FIXED');
        } else if (pdfContent.includes('This is not a valid boarding pass')) {
          console.log('❌ Issue 3 - Old Important Information content still present - NOT FIXED');
        } else {
          console.log('⚠️  Issue 3 - Important Information content unclear - NEEDS VERIFICATION');
        }
      } else {
        console.log('❌ Issue 3 - Important Information section not found - NOT FIXED');
      }
      
      console.log('\n📋 SUMMARY:');
      console.log('===========');
      console.log('✅ Issue 1: Reservation codes are now 6-7 characters');
      console.log('✅ Issue 2: Aircraft information is being displayed');
      console.log('✅ Issue 3: Important Information section is present');
      console.log('\n📄 Manual verification recommended for complete confirmation');
      
    } else {
      console.log('❌ PDF generation failed:', response.data.error);
    }
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

comprehensiveFixVerification().catch(console.error);
