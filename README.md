<<<<<<< HEAD
# VerifiedOnward.com

A full-stack web application that generates realistic flight reservation PDFs (onward flight reservations) for visa applications, with instant delivery after payment.

## 🚀 Tech Stack

**Frontend:**
- React + Vite
- Tailwind CSS
- Framer Motion
- React Router DOM

**Backend:**
- Node.js + Express
- Flight API (Ready for integration)
- Stripe + PayPal (Payments)
- Puppeteer (PDF Generation)
- Nodemail<PERSON> (Email Delivery)

## 📁 Project Structure

```
VerifiedOnward/
├── frontend/          # React frontend
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/           # Node.js backend
│   ├── routes/
│   ├── services/
│   ├── utils/
│   ├── server.js
│   └── package.json
└── README.md
```

## 🛠️ Setup Instructions

### Backend Setup

1. Navigate to backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment file:
   ```bash
   cp .env.example .env
   ```

4. Fill in your API keys in `.env`:
   - Flight API credentials (when ready)
   - Stripe keys
   - PayPal credentials
   - Email configuration

5. Start development server:
   ```bash
   npm run dev
   ```

### Frontend Setup

1. Navigate to frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start development server:
   ```bash
   npm run dev
   ```

## 🔧 Environment Variables

See `backend/.env.example` for required environment variables.

## 📝 Features

- ✈️ Airport search with autocomplete (ready for API integration)
- 🔍 Flight data integration (ready for fresh API)
- 💰 Secure payments via Stripe & PayPal
- 🧾 Realistic PDF ticket generation
- 📧 Instant email delivery
- 📱 Responsive design

## 🧪 Testing

The application uses test/sandbox modes for all payment integrations during development. Flight data integration is ready for your fresh API implementation.
=======
# InstantDummyTicket
>>>>>>> f48dd49c544776b22beaf53c2f8e90dd465111a6
