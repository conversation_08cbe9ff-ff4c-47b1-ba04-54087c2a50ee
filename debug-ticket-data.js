const axios = require('axios');

async function debugTicketData() {
  console.log('🔍 Debugging ticket data generation...');
  
  // Test data with different airlines
  const bookingData = {
    bookingReference: 'DEBUG123',
    passengers: [
      { firstName: '<PERSON>', lastName: '<PERSON>' },
      { firstName: '<PERSON>', lastName: 'Doe' }
    ],
    outboundFlight: {
      airline: 'RYANAIR',
      flightNumber: 'FR 1234',
      departure: {
        code: 'LHR',
        city: 'London, United Kingdom',
        time: '2025-07-20T10:30:00Z',
        terminal: '2'
      },
      arrival: {
        code: 'BCN',
        city: 'Barcelona, Spain',
        time: '2025-07-20T13:45:00Z',
        terminal: '1'
      },
      duration: '2h 15m'
    },
    returnFlight: {
      airline: 'EASYJET',
      flightNumber: 'U2 5678',
      departure: {
        code: 'BCN',
        city: 'Barcelona, Spain',
        time: '2025-07-27T15:20:00Z',
        terminal: '1'
      },
      arrival: {
        code: 'LHR',
        city: 'London, United Kingdom',
        time: '2025-07-27T16:35:00Z',
        terminal: '2'
      },
      duration: '2h 15m'
    },
    totalPrice: 9.98
  };

  try {
    console.log('📤 Sending booking data to debug ticket generation...');
    console.log('📋 Outbound Airline:', bookingData.outboundFlight.airline);
    console.log('📋 Return Airline:', bookingData.returnFlight.airline);
    
    // Add debug logging to the backend temporarily
    const response = await axios.post('http://localhost:5001/api/tickets/generate', bookingData);
    
    if (response.data.success) {
      console.log('✅ Booking generated successfully');
      console.log(`📋 Booking Reference: ${response.data.bookingReference}`);
      console.log(`📋 Reservation Code: ${response.data.reservationCode}`);
      
      // Now let's check the backend logs to see what ticket data was generated
      console.log('\n🔍 Check the backend console logs to see the ticket data structure');
      console.log('Look for aircraft assignments and customNotice content');
      
    } else {
      console.log(`❌ Booking generation failed: ${response.data.error}`);
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    if (error.response) {
      console.log(`❌ Response status: ${error.response.status}`);
      console.log(`❌ Response data:`, error.response.data);
    }
  }
}

// Run the debug test
debugTicketData().catch(console.error);
