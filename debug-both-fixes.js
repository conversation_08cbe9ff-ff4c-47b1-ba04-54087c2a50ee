const axios = require('axios');
const fs = require('fs');

async function debugBothFixes() {
  console.log('🔍 DEBUGGING BOTH FIXES AFTER SERVER RESTART');
  console.log('==============================================');
  console.log('Testing with fresh server instance to ensure changes are applied\n');
  
  const testData = {
    bookingReference: 'DEBUGFIX',
    passengers: [
      { firstName: 'HUDIFA', lastName: 'MISRATI' },
      { firstName: 'ATEDAL', lastName: 'ZEMIT' }
    ],
    outboundFlight: {
      airline: 'RYANAIR',
      flightNumber: 'FR 5209',
      departure: {
        code: 'MAN',
        city: 'Manchester Airport',
        time: '2025-07-21T17:10:00Z',
        terminal: '1'
      },
      arrival: {
        code: 'MLA',
        city: 'Malta International Airport',
        time: '2025-07-21T21:35:00Z',
        terminal: '1'
      },
      duration: '3h 25m'
    },
    returnFlight: {
      airline: 'EASYJET',
      flightNumber: 'U2 2274',
      departure: {
        code: 'MLA',
        city: 'Malta International Airport',
        time: '2025-07-28T11:15:00Z',
        terminal: '1'
      },
      arrival: {
        code: 'MAN',
        city: 'Manchester Airport',
        time: '2025-07-28T13:50:00Z',
        terminal: '1'
      },
      duration: '3h 35m'
    },
    totalPrice: 4.99
  };

  try {
    console.log('🔄 Testing with fresh server instance...');
    
    // First check server health
    const healthResponse = await axios.get('http://localhost:5001/api/health');
    console.log('✅ Server is healthy:', healthResponse.data);
    
    console.log('\n📤 Generating test PDF...');
    const response = await axios.post('http://localhost:5001/api/tickets/generate', testData);
    
    if (response.data.success) {
      console.log('✅ Booking generated successfully');
      console.log(`📋 Reservation Code: ${response.data.reservationCode}`);
      
      // Download the PDF
      const downloadResponse = await axios.get(`http://localhost:5001${response.data.downloadUrl}`, {
        responseType: 'arraybuffer'
      });
      
      const filename = 'DEBUG-BOTH-FIXES.pdf';
      fs.writeFileSync(filename, downloadResponse.data);
      console.log(`📄 PDF saved: ${filename}`);
      
      console.log(`\n🔍 CRITICAL VERIFICATION POINTS`);
      console.log(`===============================`);
      
      console.log(`\n✅ FIX 1 - Verification Text Position:`);
      console.log(`   📍 LOOK FOR: "Please verify flight times prior to departure"`);
      console.log(`   📍 EXPECTED POSITION: Far right side of grey departure headers`);
      console.log(`   📍 SHOULD BE ALIGNED WITH: "DEPARTURE:" and "RETURN:" text`);
      console.log(`   📍 COLOR: Light grey (#888888)`);
      console.log(`   📍 INSTANCES: Should appear in BOTH departure and return headers`);
      
      console.log(`\n✅ FIX 2 - Important Information Removal:`);
      console.log(`   📍 LOOK FOR: NO "Important Information" section at bottom`);
      console.log(`   📍 SHOULD NOT CONTAIN: Bullet points about refunds, extras, etc.`);
      console.log(`   📍 PDF SHOULD END: Cleanly after flight details table`);
      console.log(`   📍 NO DISCLAIMER TEXT: About non-refundable itinerary, etc.`);
      
      console.log(`\n🎯 VISUAL LAYOUT EXPECTED:`);
      console.log(`=========================`);
      console.log(`┌─────────────────────────────────────────────────────────────────────────────────────┐`);
      console.log(`│ ✈ DEPARTURE: MONDAY, JUL 21                    Please verify flight times prior to │`);
      console.log(`│                                                 departure                           │`);
      console.log(`├─────────────────────────────────────────────────────────────────────────────────────┤`);
      console.log(`│ [Flight Details Table]                                                             │`);
      console.log(`├─────────────────────────────────────────────────────────────────────────────────────┤`);
      console.log(`│ ✈ RETURN: MONDAY, JUL 28                       Please verify flight times prior to │`);
      console.log(`│                                                 departure                           │`);
      console.log(`├─────────────────────────────────────────────────────────────────────────────────────┤`);
      console.log(`│ [Flight Details Table]                                                             │`);
      console.log(`└─────────────────────────────────────────────────────────────────────────────────────┘`);
      console.log(`  [END OF PDF - NO Important Information section below]`);
      
      console.log(`\n🚨 CRITICAL CHECKS:`);
      console.log(`==================`);
      console.log(`1. Open ${filename} in a PDF viewer`);
      console.log(`2. Verify verification text is on the RIGHT side of headers`);
      console.log(`3. Confirm NO "Important Information" section exists`);
      console.log(`4. Check that PDF ends cleanly after flight details`);
      
      console.log(`\n📊 If both fixes are working correctly:`);
      console.log(`   ✅ Text positioning should match authentic airline reservations`);
      console.log(`   ✅ PDF should have clean, professional ending`);
      console.log(`   ✅ No unnecessary disclaimer content`);
      
    } else {
      console.log(`❌ Booking generation failed: ${response.data.error}`);
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    if (error.response) {
      console.log(`❌ Response status: ${error.response.status}`);
      console.log(`❌ Response data:`, error.response.data);
    }
  }
}

debugBothFixes().catch(console.error);
