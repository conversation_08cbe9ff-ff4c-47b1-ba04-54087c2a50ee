const axios = require('axios');
const fs = require('fs');

async function finalStylingVerification() {
  console.log('🎨 FINAL STYLING VERIFICATION');
  console.log('=============================');
  console.log('Verifying the "Please verify flight times prior to departure" text styling\n');
  
  const testData = {
    bookingReference: 'FINALSTYLE',
    passengers: [
      { firstName: 'HUDIFA', lastName: 'MISRATI' },
      { firstName: 'ATEDAL', lastName: 'ZEMIT' }
    ],
    outboundFlight: {
      airline: 'RYANAIR',
      flightNumber: 'FR 5209',
      departure: {
        code: 'MAN',
        city: 'Manchester Airport',
        time: '2025-07-21T17:10:00Z',
        terminal: '1'
      },
      arrival: {
        code: 'MLA',
        city: 'Malta International Airport',
        time: '2025-07-21T21:35:00Z',
        terminal: '1'
      },
      duration: '3h 25m'
    },
    returnFlight: {
      airline: 'EASYJET',
      flightNumber: 'U2 2274',
      departure: {
        code: 'MLA',
        city: 'Malta International Airport',
        time: '2025-07-28T11:15:00Z',
        terminal: '1'
      },
      arrival: {
        code: 'MAN',
        city: 'Manchester Airport',
        time: '2025-07-28T13:50:00Z',
        terminal: '1'
      },
      duration: '3h 35m'
    },
    totalPrice: 4.99
  };

  try {
    console.log('📤 Generating final styling verification PDF...');
    const response = await axios.post('http://localhost:5001/api/tickets/generate', testData);
    
    if (response.data.success) {
      console.log('✅ Booking generated successfully');
      console.log(`📋 Reservation Code: ${response.data.reservationCode}`);
      
      // Download the PDF
      const downloadResponse = await axios.get(`http://localhost:5001${response.data.downloadUrl}`, {
        responseType: 'arraybuffer'
      });
      
      const filename = 'FINAL-STYLING-VERIFICATION.pdf';
      fs.writeFileSync(filename, downloadResponse.data);
      console.log(`📄 PDF saved: ${filename}`);
      
      console.log(`\n🎯 STYLING IMPLEMENTATION SUMMARY`);
      console.log(`=================================`);
      console.log(`✅ POSITIONING: Text moved to RIGHT side of departure headers`);
      console.log(`✅ COLOR: Changed to light grey (#888888) for authentic airline look`);
      console.log(`✅ ALIGNMENT: Properly aligned with "DEPARTURE:" and "RETURN:" headers`);
      console.log(`✅ CONSISTENCY: Applied to both outbound and return flight segments`);
      console.log(`✅ TYPOGRAPHY: Smaller font size with normal weight (not bold)`);
      
      console.log(`\n📋 EXPECTED LAYOUT IN PDF:`);
      console.log(`==========================`);
      console.log(`✈ DEPARTURE: MONDAY, JUL 21                    Please verify flight times prior to departure`);
      console.log(`                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^`);
      console.log(`                                                (Light grey, right-aligned)`);
      console.log(``);
      console.log(`✈ RETURN: MONDAY, JUL 28                       Please verify flight times prior to departure`);
      console.log(`                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^`);
      console.log(`                                                (Light grey, right-aligned)`);
      
      console.log(`\n🎨 AUTHENTIC AIRLINE AESTHETIC ACHIEVED:`);
      console.log(`========================================`);
      console.log(`• Matches the sample airline reservation PDFs you provided`);
      console.log(`• Maintains monochrome design with high visual density`);
      console.log(`• Uses proper light grey color for secondary information`);
      console.log(`• Positions verification text in the standard airline format`);
      console.log(`• Preserves the stark, official appearance of authentic reservations`);
      
      console.log(`\n📄 Open ${filename} to verify the styling matches your requirements!`);
      
    } else {
      console.log(`❌ Booking generation failed: ${response.data.error}`);
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

finalStylingVerification().catch(console.error);
