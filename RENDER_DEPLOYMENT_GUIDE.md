# 🚀 VerifiedOnward Render Deployment Guide

## 🌟 **Why Render?**
- **Free Tier**: 750 hours/month (perfect for personal projects)
- **One Platform**: Host both frontend and backend together
- **Auto Deploy**: Automatic deployments from GitHub
- **Built-in SSL**: HTTPS enabled by default
- **Easy Setup**: Simple configuration, no complex setup
- **Great Performance**: Fast global CDN and reliable infrastructure

---

## 📋 **Deployment Overview**

### **What We'll Deploy:**
1. **Backend** → Render Web Service (Node.js)
2. **Frontend** → Render Static Site (React/Vite)

### **Expected Results:**
- **Backend**: `https://verifiedonward-backend.onrender.com`
- **Frontend**: `https://verifiedonward.onrender.com`
- **Total Setup Time**: 15-20 minutes
- **Cost**: FREE (with 750 hours/month limit)

---

## 🔧 **Step 1: Prepare Your Project**

### **A. Create Render Configuration Files**

#### **Backend Configuration** (`backend/render.yaml`):
```yaml
services:
  - type: web
    name: verifiedonward-backend
    env: node
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
```

#### **Frontend Build Script** (`frontend/package.json` - verify this exists):
```json
{
  "scripts": {
    "build": "vite build",
    "preview": "vite preview"
  }
}
```

### **B. Update Environment Variables**

#### **Backend Environment Variables** (we'll add these in Render dashboard):
```
NODE_ENV=production
PORT=10000
FRONTEND_URL=https://verifiedonward.onrender.com
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
SERPAPI_KEY=1ff50775189711170a2cb25a8cf425c4c69a68f85184e93e33660e1f8fb10bf4
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

#### **Frontend Environment Variables**:
```
VITE_API_BASE_URL=https://verifiedonward-backend.onrender.com/api
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
VITE_PAYPAL_CLIENT_ID=your_paypal_client_id
```

---

## 🚀 **Step 2: Deploy Backend to Render**

### **A. Create Render Account**
1. Go to [render.com](https://render.com)
2. Sign up with your GitHub account
3. Authorize Render to access your repositories

### **B. Deploy Backend Web Service**
1. **Click "New +"** → **"Web Service"**
2. **Connect Repository**: Select `FinTechSpert/VerifiedOnward`
3. **Configure Service**:
   - **Name**: `verifiedonward-backend`
   - **Root Directory**: `backend`
   - **Environment**: `Node`
   - **Build Command**: `npm install`
   - **Start Command**: `npm start`
   - **Instance Type**: `Free` (or `Starter` for better performance)

4. **Add Environment Variables**:
   - Click "Advanced" → "Add Environment Variable"
   - Add all the backend environment variables listed above
   - **Important**: Set `PORT=10000` (Render's default port)

5. **Deploy**:
   - Click "Create Web Service"
   - Wait for deployment (5-10 minutes)
   - Copy the generated URL: `https://verifiedonward-backend.onrender.com`

### **C. Test Backend**
- Visit: `https://verifiedonward-backend.onrender.com/api/health` (if you have a health endpoint)
- Check logs in Render dashboard for any errors

---

## 🌐 **Step 3: Deploy Frontend to Render**

### **A. Deploy Static Site**
1. **Click "New +"** → **"Static Site"**
2. **Connect Repository**: Select `FinTechSpert/VerifiedOnward`
3. **Configure Site**:
   - **Name**: `verifiedonward`
   - **Root Directory**: `frontend`
   - **Build Command**: `npm run build`
   - **Publish Directory**: `dist`

4. **Add Environment Variables**:
   - `VITE_API_BASE_URL`: `https://verifiedonward-backend.onrender.com/api`
   - `VITE_STRIPE_PUBLISHABLE_KEY`: `pk_test_your_stripe_publishable_key`
   - `VITE_PAYPAL_CLIENT_ID`: `your_paypal_client_id`

5. **Deploy**:
   - Click "Create Static Site"
   - Wait for build and deployment (3-5 minutes)
   - Your site will be available at: `https://verifiedonward.onrender.com`

---

## 🔄 **Step 4: Update CORS and Final Configuration**

### **A. Update Backend CORS**
Your backend should automatically handle CORS, but verify it allows your frontend URL:
```javascript
// In your backend server.js, ensure CORS allows your frontend
const corsOptions = {
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'https://verifiedonward.onrender.com' // Add this
  ],
  credentials: true
};
```

### **B. Update Frontend API URL**
If needed, update your frontend environment variables in Render dashboard to point to the correct backend URL.

---

## 🧪 **Step 5: Test Your Deployment**

### **Testing Checklist:**
- [ ] **Homepage loads**: Visit `https://verifiedonward.onrender.com`
- [ ] **API connection**: Test flight search functionality
- [ ] **Payment flow**: Test with Stripe test cards
- [ ] **PDF generation**: Test booking confirmation downloads
- [ ] **Navigation**: Check all header and footer links
- [ ] **Mobile responsive**: Test on different screen sizes
- [ ] **HTTPS**: Verify SSL certificate is working

### **Common Test URLs:**
- **Homepage**: `https://verifiedonward.onrender.com`
- **API Health**: `https://verifiedonward-backend.onrender.com/api/health`
- **Flight Search**: Test the search form on homepage
- **Checkout Flow**: Complete a test booking

---

## 💰 **Render Pricing & Limits**

### **Free Tier:**
- **750 hours/month** per service (enough for 24/7 hosting)
- **Automatic sleep** after 15 minutes of inactivity
- **Cold starts** when waking up (2-3 seconds)
- **500 GB bandwidth/month**
- **Custom domains** supported

### **Starter Tier ($7/month per service):**
- **No sleep/cold starts**
- **Faster builds**
- **More resources**
- **Priority support**

### **For VerifiedOnward:**
- **Free tier is perfect** for development and testing
- **Upgrade to Starter** when you get regular traffic
- **Total cost**: $0-14/month (backend + frontend)

---

## 🔧 **Advanced Configuration**

### **Custom Domain Setup:**
1. Go to your service settings in Render
2. Click "Custom Domains"
3. Add your domain (e.g., `verifiedonward.com`)
4. Update DNS records as instructed
5. SSL certificate is automatically provisioned

### **Auto-Deploy Setup:**
- **Already enabled** by default
- Every push to `main` branch triggers deployment
- Can configure branch-specific deployments
- Build logs available in dashboard

### **Environment Management:**
- **Separate environments** for staging/production
- **Environment variable groups** for shared configs
- **Secrets management** for sensitive data

---

## 🆘 **Troubleshooting**

### **Common Issues:**

#### **Backend Won't Start:**
- Check build logs in Render dashboard
- Verify `package.json` has correct `start` script
- Ensure `PORT=10000` environment variable is set
- Check for missing dependencies

#### **Frontend Build Fails:**
- Verify `npm run build` works locally
- Check for environment variable issues
- Ensure all dependencies are in `package.json`
- Check build logs for specific errors

#### **API Connection Issues:**
- Verify backend URL in frontend environment variables
- Check CORS configuration in backend
- Test API endpoints directly
- Check network tab in browser dev tools

#### **Cold Start Issues (Free Tier):**
- Services sleep after 15 minutes of inactivity
- First request after sleep takes 2-3 seconds
- Consider upgrading to Starter tier for production

---

## 🎯 **Next Steps After Deployment**

1. **Test thoroughly** with real payment flows
2. **Set up monitoring** and error tracking
3. **Configure custom domain** if desired
4. **Set up staging environment** for testing
5. **Monitor usage** and upgrade if needed
6. **Set up backup strategy** for data

---

## 📊 **Deployment Summary**

### **What You'll Have:**
- ✅ **Professional deployment** on reliable infrastructure
- ✅ **Automatic HTTPS** with SSL certificates
- ✅ **Auto-deployments** from GitHub
- ✅ **Environment management** through dashboard
- ✅ **Monitoring and logs** built-in
- ✅ **Scalable architecture** ready for growth

### **Total Setup Time:** 15-20 minutes
### **Monthly Cost:** FREE (with usage limits)
### **Performance:** Excellent with global CDN
### **Reliability:** 99.9% uptime SLA

Your VerifiedOnward application will be production-ready and accessible worldwide! 🌍
