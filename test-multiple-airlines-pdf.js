#!/usr/bin/env node

/**
 * Multiple Airlines PDF Test
 * Tests logo display consistency across different airlines
 */

const airlineLogoService = require('./backend/services/airlineLogoService');
const pdfService = require('./backend/services/pdfService');
const fs = require('fs');

async function testMultipleAirlines() {
  console.log('✈️  MULTIPLE AIRLINES PDF TEST');
  console.log('=' .repeat(60));

  const testAirlines = [
    { name: 'AIR FRANCE', code: 'AF', flight: 'AF 1669' },
    { name: 'ETHIOPIAN AIRLINES', code: 'ET', flight: 'ET 900' },
    { name: 'BRITISH AIRWAYS', code: 'BA', flight: 'BA 123' },
    { name: '<PERSON>Y<PERSON><PERSON><PERSON>', code: 'FR', flight: 'FR 456' },
    { name: 'EMIRATES', code: 'EK', flight: 'EK 789' },
    { name: 'L<PERSON>FTHANSA', code: 'LH', flight: '<PERSON>H 321' },
    { name: '<PERSON>INGAPORE AIRLINES', code: 'SQ', flight: 'SQ 654' },
    { name: 'QATAR AIRWAYS', code: 'QR', flight: 'QR 987' }
  ];

  const results = [];

  for (let i = 0; i < testAirlines.length; i++) {
    const airline = testAirlines[i];
    console.log(`\n🔍 Testing ${airline.name} (${i + 1}/${testAirlines.length})`);
    
    try {
      // Test logo retrieval
      console.log('  📥 Fetching logo...');
      const logo = await airlineLogoService.getLogoForPDF(airline.code, airline.name);
      
      const isBase64PNG = logo.startsWith('data:image/png;base64,');
      const isSVG = logo.startsWith('data:image/svg');
      const logoType = isBase64PNG ? 'Authentic PNG' : isSVG ? 'Generated SVG' : 'Unknown';
      
      console.log(`  🎨 Logo type: ${logoType}`);
      console.log(`  📏 Logo size: ${logo.length} characters`);
      
      // Create test ticket data for this airline
      const testTicketData = {
        reservationCode: `TEST${airline.code}123`,
        airlineReservationCode: `${airline.code}123VW`,
        tripDates: '21 JUL 2025 • 28 JUL 2025',
        destination: 'TRIP TO INTERNATIONAL DESTINATION',
        passengers: [
          { name: 'JOHN/DOE' }
        ],
        segments: [
          {
            airline: airline.name,
            flightNo: airline.flight,
            from: { code: 'LHR', name: 'London Heathrow' },
            to: { code: 'JFK', name: 'New York JFK' },
            departureTime: '11:50',
            arrivalTime: '14:40',
            departureDay: 'MONDAY, JUL 21',
            duration: '8H 50M',
            flightClass: 'Economy Class (M)',
            stops: 0
          }
        ]
      };

      // Generate PDF
      const outputPath = `./test-${airline.code.toLowerCase()}-logo.pdf`;
      console.log(`  📄 Generating PDF: ${outputPath}`);
      
      await pdfService.generatePDF(testTicketData, outputPath);
      
      // Check file size
      const stats = fs.statSync(outputPath);
      const fileSizeKB = (stats.size / 1024).toFixed(2);
      
      console.log(`  ✅ PDF generated successfully (${fileSizeKB} KB)`);
      
      results.push({
        airline: airline.name,
        code: airline.code,
        logoType,
        logoSize: logo.length,
        pdfPath: outputPath,
        pdfSizeKB: fileSizeKB,
        success: true
      });
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      results.push({
        airline: airline.name,
        code: airline.code,
        error: error.message,
        success: false
      });
    }
  }

  // Summary report
  console.log('\n📊 SUMMARY REPORT');
  console.log('=' .repeat(60));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n✅ SUCCESSFUL TESTS:');
    successful.forEach(result => {
      console.log(`  ${result.code} (${result.airline}): ${result.logoType}, PDF: ${result.pdfSizeKB} KB`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\n❌ FAILED TESTS:');
    failed.forEach(result => {
      console.log(`  ${result.code} (${result.airline}): ${result.error}`);
    });
  }

  // Logo type analysis
  console.log('\n🎨 LOGO TYPE ANALYSIS:');
  const logoTypes = {};
  successful.forEach(result => {
    logoTypes[result.logoType] = (logoTypes[result.logoType] || 0) + 1;
  });
  
  Object.entries(logoTypes).forEach(([type, count]) => {
    console.log(`  ${type}: ${count} airlines`);
  });

  console.log('\n🏁 MULTIPLE AIRLINES TEST COMPLETE');
  console.log(`📁 Generated ${successful.length} PDF files`);
}

// Run the test
testMultipleAirlines().catch(console.error);
