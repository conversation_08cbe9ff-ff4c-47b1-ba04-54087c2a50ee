const fs = require('fs');
const path = require('path');

function verifyPDFContent() {
  console.log('🔍 Verifying PDF content for all three fixes...');
  
  // Check the latest generated PDF
  const pdfFiles = [
    'live-system-test-1752707681639.pdf',
    'test-outputs/refined-ticket-ryanair-round-trip.pdf',
    'test-outputs/refined-ticket-easyjet-one-way.pdf'
  ];
  
  pdfFiles.forEach((filename, index) => {
    if (fs.existsSync(filename)) {
      console.log(`\n📄 Checking ${filename}...`);
      
      try {
        const pdfBuffer = fs.readFileSync(filename);
        const pdfContent = pdfBuffer.toString('latin1'); // Use latin1 to preserve binary data as text
        
        // Check 1: Reservation Code Length
        const titleMatch = pdfContent.match(/Flight Reservation - ([A-Z0-9]+)/);
        if (titleMatch) {
          const reservationCode = titleMatch[1];
          const codeLength = reservationCode.length;
          if (codeLength >= 6 && codeLength <= 7) {
            console.log(`✅ Issue 1 - Reservation Code: ${reservationCode} (${codeLength} chars) ✓`);
          } else {
            console.log(`❌ Issue 1 - Reservation Code: ${reservationCode} (${codeLength} chars) ✗`);
          }
        } else {
          console.log('❌ Issue 1 - Could not find reservation code in PDF');
        }
        
        // Check 2: Aircraft Information (look for different aircraft types)
        const aircraftMatches = pdfContent.match(/AIRCRAFT:<\/td>\s*<td[^>]*>([^<]+)/g);
        if (aircraftMatches && aircraftMatches.length > 0) {
          console.log('✅ Issue 2 - Aircraft Information Found:');
          aircraftMatches.forEach((match, i) => {
            const aircraft = match.match(/>([^<]+)$/)[1];
            console.log(`   Segment ${i + 1}: ${aircraft}`);
          });
          
          // Check if we have different aircraft for different segments
          if (aircraftMatches.length > 1) {
            const aircraft1 = aircraftMatches[0].match(/>([^<]+)$/)[1];
            const aircraft2 = aircraftMatches[1].match(/>([^<]+)$/)[1];
            if (aircraft1 !== aircraft2) {
              console.log('✅ Different aircraft for different segments ✓');
            } else {
              console.log('⚠️  Same aircraft for both segments (may be intentional)');
            }
          }
        } else {
          console.log('❌ Issue 2 - Could not find aircraft information');
        }
        
        // Check 3: Important Information Section
        if (pdfContent.includes('Important Information')) {
          console.log('✅ Issue 3 - Important Information section found ✓');
          
          if (pdfContent.includes('non-refundable flight itinerary')) {
            console.log('✅ Updated Important Information content confirmed ✓');
          } else if (pdfContent.includes('This is not a valid boarding pass')) {
            console.log('❌ Old Important Information content still present ✗');
          } else {
            console.log('⚠️  Important Information content unclear');
          }
        } else {
          console.log('❌ Issue 3 - Important Information section not found');
        }
        
      } catch (error) {
        console.log(`❌ Error reading PDF: ${error.message}`);
      }
    } else {
      console.log(`⚠️  PDF file not found: ${filename}`);
    }
  });
  
  console.log('\n🎉 PDF content verification completed!');
}

// Run the verification
verifyPDFContent();
