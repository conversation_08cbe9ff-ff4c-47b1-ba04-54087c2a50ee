// Test script to verify PayPal payment flow
const testPayPalFlow = async () => {
  console.log('🧪 Testing PayPal payment flow...');
  
  try {
    // Test PayPal order creation
    const response = await fetch('http://localhost:5001/api/payments/paypal/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: 4.99,
        currency: 'USD',
        description: 'Test dummy flight ticket'
      })
    });
    
    const data = await response.json();
    console.log('✅ PayPal order creation response:', data);
    
    if (data.success && data.orderId) {
      console.log('✅ PayPal order created successfully');
      
      // Test PayPal order capture (simulate approval)
      const captureResponse = await fetch(`http://localhost:5001/api/payments/paypal/capture/${data.orderId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      const captureData = await captureResponse.json();
      console.log('✅ PayPal capture response:', captureData);
      
      if (captureData.success) {
        console.log('🎉 PayPal flow test PASSED!');
        return true;
      } else {
        console.error('❌ PayPal capture failed:', captureData);
        return false;
      }
      
    } else {
      console.error('❌ PayPal order creation failed:', data);
      return false;
    }
    
  } catch (error) {
    console.error('❌ PayPal test failed with error:', error);
    return false;
  }
};

// Run the test
testPayPalFlow().then(success => {
  if (success) {
    console.log('🎉 PAYPAL TEST PASSED - PayPal flow is working!');
  } else {
    console.log('❌ PAYPAL TEST FAILED - PayPal flow has issues');
  }
});
