#!/usr/bin/env node

/**
 * Simple Checkout Flow Test
 * Tests the key components of the checkout system
 */

console.log('🧪 Testing checkout flow...\n');

// Simple test using curl commands
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const runTest = async () => {
  let allPassed = true;

  // Test 1: Frontend server
  console.log('📡 Testing frontend server...');
  try {
    const { stdout } = await execAsync('curl -s -o /dev/null -w "%{http_code}" http://localhost:5173');
    if (stdout.trim() === '200') {
      console.log('✅ Frontend server: Running');
    } else {
      console.log('❌ Frontend server: Not responding');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ Frontend server: Error -', error.message);
    allPassed = false;
  }

  // Test 2: Backend server
  console.log('\n📡 Testing backend server...');
  try {
    const { stdout } = await execAsync('curl -s http://localhost:5001/api/health');
    const data = JSON.parse(stdout);
    if (data.status === 'OK') {
      console.log('✅ Backend server: Running');
    } else {
      console.log('❌ Backend server: Not healthy');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ Backend server: Error -', error.message);
    allPassed = false;
  }

  // Test 3: Checkout page
  console.log('\n🌐 Testing checkout page...');
  try {
    const { stdout } = await execAsync('curl -s -o /dev/null -w "%{http_code}" http://localhost:5173/checkout');
    if (stdout.trim() === '200') {
      console.log('✅ Checkout page: Accessible');
    } else {
      console.log('❌ Checkout page: Not accessible');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ Checkout page: Error -', error.message);
    allPassed = false;
  }

  // Test 4: Flight search API
  console.log('\n✈️ Testing flight search API...');
  try {
    const { stdout } = await execAsync(`curl -s -X POST http://localhost:5001/api/flights/search -H "Content-Type: application/json" -d '{"origin":"LHR","destination":"JFK","date":"2025-08-15","tripType":"oneWay"}'`);
    const data = JSON.parse(stdout);
    if (data.success && data.data.flights.length > 0) {
      console.log(`✅ Flight search: Found ${data.data.flights.length} flights`);
    } else {
      console.log('❌ Flight search: No flights found');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ Flight search: Error -', error.message);
    allPassed = false;
  }

  // Test 5: Payment API
  console.log('\n💳 Testing payment API...');
  try {
    const { stdout } = await execAsync(`curl -s -X POST http://localhost:5001/api/payments/stripe/create-intent -H "Content-Type: application/json" -d '{"amount":4.99,"currency":"usd","metadata":{"description":"Test payment"}}'`);
    const data = JSON.parse(stdout);
    if (data.success && data.clientSecret) {
      console.log('✅ Payment API: Working');
    } else {
      console.log('❌ Payment API: Failed');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ Payment API: Error -', error.message);
    allPassed = false;
  }

  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('📊 CHECKOUT FLOW TEST SUMMARY');
  console.log('='.repeat(50));

  if (allPassed) {
    console.log('🎉 ALL TESTS PASSED!');
    console.log('\n🚀 Checkout system is ready:');
    console.log('   • Frontend: http://localhost:5173');
    console.log('   • Backend: http://localhost:5001');
    console.log('   • Checkout: http://localhost:5173/checkout');
    console.log('   • Debug: http://localhost:5173/checkout-debug');
    
    console.log('\n🎯 Test the complete flow:');
    console.log('   1. Go to: http://localhost:5173/search');
    console.log('   2. Search for flights (e.g., LHR to JFK)');
    console.log('   3. Select a flight');
    console.log('   4. Fill passenger details');
    console.log('   5. Click "Continue to Payment"');
    console.log('   6. Complete payment');
    console.log('   7. Success! 🎉');
    
  } else {
    console.log('❌ Some tests failed.');
    console.log('\n🔧 Troubleshooting:');
    console.log('   • Make sure both servers are running');
    console.log('   • Frontend: npm run dev (in frontend folder)');
    console.log('   • Backend: npm start (in backend folder)');
  }

  return allPassed;
};

// Run the test
runTest().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('🚨 Test error:', error);
  process.exit(1);
});
