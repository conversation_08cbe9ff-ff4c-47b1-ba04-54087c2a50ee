
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Flight Reservation</title>
        <style>
          @page {
            margin: 15mm;
            size: A4;
          }

          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #000;
            background: white;
            font-size: 11px;
            line-height: 1.4;
          }

          .container {
            width: 100%;
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 20px;
          }

          /* Trip Header - Exact Format */
          .trip-header {
            font-size: 14px;
            font-weight: bold;
            text-align: left;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 5px;
          }

          /* Prepared For Section */
          .prepared-section {
            margin-bottom: 20px;
          }

          .prepared-label {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 5px;
          }

          .prepared-names {
            font-size: 11px;
            font-weight: bold;
            line-height: 1.2;
          }

          /* Reservation Codes Section */
          .reservation-codes {
            margin-bottom: 20px;
          }

          .reservation-codes div {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 3px;
          }

          /* Flight Section - Exact Format */
          .flight-section {
            margin-bottom: 20px;
          }

          .departure-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 12px;
            font-weight: bold;
            border-bottom: 1px solid #000;
            padding-bottom: 5px;
          }

          .plane-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            fill: currentColor;
          }

          .verification-text {
            font-size: 10px;
            color: #666;
            font-weight: normal;
            margin-left: auto;
          }

          /* Flight Details Box - Exact Format */
          .flight-box {
            border: 1px solid #000;
            margin-bottom: 15px;
          }

          .flight-content {
            display: flex;
          }

          .airline-section {
            width: 200px;
            padding: 15px;
            border-right: 1px solid #000;
            background: #f8f8f8;
          }

          .airline-name {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 5px;
          }

          .flight-number {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
          }

          .flight-details {
            font-size: 10px;
            line-height: 1.3;
          }

          .flight-details div {
            margin-bottom: 3px;
          }

          /* Route Section */
          .route-section {
            flex: 1;
            padding: 15px;
          }

          .route-grid {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
          }

          .departure-info, .arrival-info {
            text-align: center;
          }

          .airport-code {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 2px;
          }

          .city-country {
            font-size: 9px;
            margin-bottom: 8px;
          }

          .time-large {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 2px;
          }

          .terminal {
            font-size: 9px;
          }

          /* Aircraft Info Section */
          .aircraft-section {
            width: 150px;
            padding: 15px;
            border-left: 1px solid #000;
            font-size: 10px;
          }

          .aircraft-section div {
            margin-bottom: 8px;
          }

          .aircraft-label {
            font-weight: bold;
            margin-bottom: 3px;
          }

          /* Passenger Table - Exact Format */
          .passenger-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            border: 1px solid #000;
          }

          .passenger-table th {
            background-color: #e8e8e8;
            padding: 8px 12px;
            text-align: left;
            font-weight: bold;
            font-size: 10px;
            border-right: 1px solid #000;
            border-bottom: 1px solid #000;
          }

          .passenger-table th:last-child {
            border-right: none;
          }

          .passenger-table td {
            padding: 8px 12px;
            border-right: 1px solid #000;
            font-size: 10px;
            background: white;
          }

          .passenger-table td:last-child {
            border-right: none;
          }

          /* Important Information */
          .important-section {
            margin-top: 30px;
            padding-top: 15px;
          }

          .important-title {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 10px;
          }

          .important-list {
            font-size: 10px;
            line-height: 1.4;
          }

          .important-item {
            margin-bottom: 5px;
          }

          /* Airline Logo */
          .airline-logo {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            vertical-align: middle;
          }

          /* Utility Classes */
          .text-center {
            text-align: center;
          }

          .text-bold {
            font-weight: bold;
          }

          .mb-10 {
            margin-bottom: 10px;
          }

          .mb-15 {
            margin-bottom: 15px;
          }

          .mb-20 {
            margin-bottom: 20px;
          }

          .main-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 30px;
            text-transform: uppercase;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
          }

          .prepared-section {
            margin-bottom: 20px;
          }

          .prepared-label {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 5px;
          }

          .prepared-names {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 20px;
          }

          .status-section {
            margin-bottom: 30px;
          }

          .status-line {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 5px;
          }

          .reservation-line {
            font-size: 11px;
            font-weight: bold;
          }

          /* Passenger Table */
          .passenger-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
            margin-bottom: 30px;
          }

          .passenger-header {
            background: #f0f0f0;
            border-bottom: 1px solid #000;
          }

          .passenger-header td {
            padding: 8px 12px;
            font-size: 11px;
            font-weight: bold;
            border-right: 1px solid #000;
          }

          .passenger-header td:last-child {
            border-right: none;
          }

          .passenger-row td {
            padding: 8px 12px;
            font-size: 11px;
            border-right: 1px solid #000;
            border-top: 1px solid #000;
          }

          .passenger-row td:last-child {
            border-right: none;
          }

          /* Important Information */
          .important-section {
            margin-top: 30px;
          }

          .important-title {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 10px;
          }

          .important-list {
            font-size: 10px;
            line-height: 1.4;
          }

          .important-item {
            margin-bottom: 8px;
          }

          .header-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
          }

          .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .trip-destination {
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: bold;
          }

          /* Prepared For Section */
          .prepared-section {
            margin-bottom: 20px;
          }

          .prepared-label {
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 5px;
            color: #000;
          }

          .prepared-names {
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            color: #000;
            margin-bottom: 15px;
          }

          /* Status and Reservation Section */
          .status-section {
            margin-bottom: 20px;
          }

          .status-item {
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            color: #000;
            margin-bottom: 5px;
          }

          .reservation-code {
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            color: #000;
          }

          /* Route Section */
          .route-section {
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
          }

          .route-info {
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            color: #000;
          }

          /* Departure Section */
          .departure-section {
            margin-bottom: 15px;
            border-bottom: 1px solid #000;
            padding-bottom: 8px;
          }

          .departure-title {
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            color: #000;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;
          }

          .departure-icon {
            font-size: 14px;
          }

          .verification-note {
            font-size: 9px;
            color: #666;
            font-style: italic;
          }

          /* Flight Block - New Sample Format */
          .flight-block {
            border: 1px solid #000;
            margin-bottom: 20px;
            background: white;
          }

          .flight-content {
            display: flex;
            background: white;
          }

          /* Airline Logo Section */
          .airline-logo-section {
            width: 250px;
            padding: 20px;
            background: #f5f5f5;
            border-right: 1px solid #000;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }

          .airline-logo-large {
            max-width: 180px;
            max-height: 80px;
            object-fit: contain;
            margin-bottom: 15px;
          }

          .airline-info {
            text-align: center;
          }

          .airline-name-large {
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
            color: #000;
            margin-bottom: 5px;
          }

          .flight-number-large {
            font-size: 16px;
            font-weight: bold;
            color: #000;
            margin-bottom: 10px;
          }

          .flight-class, .flight-status {
            font-size: 10px;
            color: #000;
            margin-bottom: 3px;
          }

          /* Flight Details Grid */
          .flight-details-grid {
            flex: 1;
            display: flex;
            background: white;
          }

          .flight-route-cell {
            flex: 1;
            padding: 15px;
            border-right: 1px solid #000;
            display: flex;
            justify-content: space-between;
          }

          .airport-section {
            text-align: center;
          }

          .airport-code {
            font-size: 14px;
            font-weight: bold;
            color: #000;
          }

          .airport-city {
            font-size: 10px;
            color: #000;
            margin-top: 2px;
          }

          .flight-info-cell {
            flex: 1;
            padding: 15px;
            border-right: 1px solid #000;
          }

          .additional-info-cell {
            flex: 1;
            padding: 15px;
          }

          .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 10px;
          }

          .info-label {
            font-weight: bold;
            color: #000;
          }

          .info-value {
            color: #000;
          }

          .duration-text {
            font-size: 10px;
            color: #000;
            margin-bottom: 5px;
          }

          .stops-text {
            font-size: 10px;
            color: #000;
          }

          /* Flight Details Grid */
          .flight-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            border-top: 1px solid #000;
            background: white;
          }

          .detail-cell {
            padding: 8px 12px;
            border-right: 1px solid #000;
            border-bottom: 1px solid #000;
            font-size: 10px;
          }

          .detail-cell:last-child {
            border-right: none;
          }

          .detail-label {
            font-weight: bold;
            margin-bottom: 3px;
            color: #000;
          }

          .detail-value {
            color: #000;
            font-size: 10px;
          }

          /* Passenger Table Section */
          .passenger-table-section {
            background: #f0f0f0;
            border-top: 1px solid #000;
          }

          .passenger-table {
            width: 100%;
            border-collapse: collapse;
          }

          .passenger-header-row {
            background: #f0f0f0;
          }

          .passenger-header-cell {
            padding: 8px 12px;
            border-right: 1px solid #000;
            font-size: 10px;
            font-weight: bold;
            text-align: left;
            color: #000;
          }

          .passenger-header-cell:last-child {
            border-right: none;
          }

          .passenger-cell {
            padding: 8px 12px;
            border-right: 1px solid #000;
            border-top: 1px solid #000;
            font-size: 10px;
            background: white;
            color: #000;
          }

          .passenger-cell:last-child {
            border-right: none;
          }

          /* Important Information Section */
          .important-section {
            margin-top: 25px;
            padding-top: 15px;
            border-top: 1px solid #000;
          }

          .important-title {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #000;
          }

          .important-list {
            list-style: none;
            padding: 0;
            margin: 0;
          }

          .important-item {
            margin-bottom: 10px;
            font-size: 10px;
            line-height: 1.4;
            padding-left: 12px;
            position: relative;
            text-align: justify;
            color: #000;
          }

          .important-item:before {
            content: "•";
            position: absolute;
            left: 0;
            font-weight: bold;
          }
            position: absolute;
            left: 0;
            font-weight: bold;
            color: #000;
          }

          .important-item strong {
            font-weight: bold;
            color: #000;
          }


        </style>
      </head>
      <body>
        <div class="container">
          <!-- Trip Header - Exact Format -->
          <div class="trip-header">
            18 JUL 2021 • 19 JUL 2021 TRIP TO NEW YORK
          </div>

          <!-- Prepared For Section -->
          <div class="prepared-section">
            <div class="prepared-label">PREPARED FOR</div>
            <div class="prepared-names">
              COOPER/JANE MR.<br>WILSON/JENNY MR.
            </div>
          </div>

          <!-- Reservation Codes -->
          <div class="reservation-codes">
            <div>RESERVATION CODE: NHG8IQ</div>
            <div>AIRLINE RESERVATION CODE: NHG8IQ</div>
          </div>

          
        <!-- Flight Section for Trip 1 -->
        <div class="flight-section">
          <div class="departure-header">
            <svg class="plane-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
            </svg>
            DEPARTURE: SUNDAY 18 JUL
            <span class="verification-text">Please verify flight times prior to departure</span>
          </div>
          
          <div class="flight-box">
            <div class="flight-content">
              <!-- Airline Section -->
              <div class="airline-section">
                <div class="airline-name">CATHAY PACIFIC</div>
                <div class="flight-number">CX 784</div>
                <div class="flight-details">
                  <div>Duration: 05hr(s) 00min(s)</div>
                  <div>Class: Economy Class (M)</div>
                  <div>Status: Confirmed</div>
                </div>
              </div>

              <!-- Route Section -->
              <div class="route-section">
                <div class="route-grid">
                  <div class="departure-info">
                    <div class="airport-code">DPS</div>
                    <div class="city-country">Denpasar-Bali, Indonesia</div>
                    <div class="time-large">16:05</div>
                    <div class="terminal">Terminal: 1</div>
                  </div>
                  <div class="arrival-info">
                    <div class="airport-code">HKG</div>
                    <div class="city-country">Hong Kong, Hong Kong</div>
                    <div class="time-large">21:05</div>
                    <div class="terminal">Terminal: 1</div>
                  </div>
                </div>
              </div>

              <!-- Aircraft Section -->
              <div class="aircraft-section">
                <div>
                  <div class="aircraft-label">Aircraft:</div>
                  <div>AIRBUS INDUSTRIE A330-300</div>
                </div>
                <div>
                  <div class="aircraft-label">Distance (in miles):</div>
                  <div>Not Available</div>
                </div>
                <div>
                  <div class="aircraft-label">Stop(s):</div>
                  <div>0</div>
                </div>
                <div>
                  <div class="aircraft-label">Meals:</div>
                  <div>Not Available</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Passenger Table for this leg -->
          <table class="passenger-table">
            <tr>
              <th>Passenger Name:</th>
              <th>Seats:</th>
              <th>Booking:</th>
            </tr>
            
              <tr>
                <td>COOPER/JANE MR.</td>
                <td>Check-in required</td>
                <td>CONFIRMED</td>
              </tr>
            
              <tr>
                <td>WILSON/JENNY MR.</td>
                <td>Check-in required</td>
                <td>CONFIRMED</td>
              </tr>
            
          </table>
        
        </div>
      
        <!-- Flight Section for Trip 2 -->
        <div class="flight-section">
          <div class="departure-header">
            <svg class="plane-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
            </svg>
            DEPARTURE: MONDAY 19 JUL
            <span class="verification-text">Please verify flight times prior to departure</span>
          </div>
          
          <div class="flight-box">
            <div class="flight-content">
              <!-- Airline Section -->
              <div class="airline-section">
                <div class="airline-name">CATHAY PACIFIC</div>
                <div class="flight-number">CX 844</div>
                <div class="flight-details">
                  <div>Duration: 15hr(s) 55min(s)</div>
                  <div>Class: Economy Class (M)</div>
                  <div>Status: Confirmed</div>
                </div>
              </div>

              <!-- Route Section -->
              <div class="route-section">
                <div class="route-grid">
                  <div class="departure-info">
                    <div class="airport-code">HKG</div>
                    <div class="city-country">Hong Kong, Hong Kong</div>
                    <div class="time-large">02:05</div>
                    <div class="terminal">Terminal: 1</div>
                  </div>
                  <div class="arrival-info">
                    <div class="airport-code">JFK</div>
                    <div class="city-country">New York, United States Of America</div>
                    <div class="time-large">06:00</div>
                    <div class="terminal">Terminal: 8</div>
                  </div>
                </div>
              </div>

              <!-- Aircraft Section -->
              <div class="aircraft-section">
                <div>
                  <div class="aircraft-label">Aircraft:</div>
                  <div>BOEING 777-300ER</div>
                </div>
                <div>
                  <div class="aircraft-label">Distance (in miles):</div>
                  <div>Not Available</div>
                </div>
                <div>
                  <div class="aircraft-label">Stop(s):</div>
                  <div>0</div>
                </div>
                <div>
                  <div class="aircraft-label">Meals:</div>
                  <div>Not Available</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Passenger Table for this leg -->
          <table class="passenger-table">
            <tr>
              <th>Passenger Name:</th>
              <th>Seats:</th>
              <th>Booking:</th>
            </tr>
            
              <tr>
                <td>COOPER/JANE MR.</td>
                <td>Check-in required</td>
                <td>CONFIRMED</td>
              </tr>
            
              <tr>
                <td>WILSON/JENNY MR.</td>
                <td>Check-in required</td>
                <td>CONFIRMED</td>
              </tr>
            
          </table>
        
        </div>
      
        </div>
      </body>
      </html>
    