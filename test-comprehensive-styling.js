const axios = require('axios');
const fs = require('fs');

async function testComprehensiveStyling() {
  console.log('🎨 COMPREHENSIVE STYLING TEST');
  console.log('=============================');
  console.log('Testing verification text styling across different scenarios\n');
  
  const testCases = [
    {
      name: 'Round Trip - Different Airlines',
      filename: 'round-trip-styling-test.pdf',
      data: {
        bookingReference: 'ROUNDTRIP',
        passengers: [{ firstName: 'John', lastName: 'Smith' }],
        outboundFlight: {
          airline: 'BRITISH AIRWAYS',
          flightNumber: 'BA 456',
          departure: { code: 'LHR', city: 'London', time: '2025-07-21T08:00:00Z', terminal: '5' },
          arrival: { code: 'JFK', city: 'New York', time: '2025-07-21T16:30:00Z', terminal: '7' },
          duration: '8h 30m'
        },
        returnFlight: {
          airline: 'AMERICAN AIRLINES',
          flightNumber: 'AA 789',
          departure: { code: 'JFK', city: 'New York', time: '2025-07-28T18:45:00Z', terminal: '8' },
          arrival: { code: 'LHR', city: 'London', time: '2025-07-29T06:15:00Z', terminal: '3' },
          duration: '7h 30m'
        },
        totalPrice: 599.99
      }
    },
    {
      name: 'One Way - Single Flight',
      filename: 'one-way-styling-test.pdf',
      data: {
        bookingReference: 'ONEWAY',
        passengers: [{ firstName: 'Jane', lastName: 'Doe' }],
        outboundFlight: {
          airline: 'EMIRATES',
          flightNumber: 'EK 123',
          departure: { code: 'DXB', city: 'Dubai', time: '2025-07-22T14:20:00Z', terminal: '3' },
          arrival: { code: 'LHR', city: 'London', time: '2025-07-22T18:45:00Z', terminal: '2' },
          duration: '7h 25m'
        },
        totalPrice: 899.99
      }
    },
    {
      name: 'Budget Airlines Round Trip',
      filename: 'budget-airlines-styling-test.pdf',
      data: {
        bookingReference: 'BUDGET',
        passengers: [{ firstName: 'Mike', lastName: 'Johnson' }],
        outboundFlight: {
          airline: 'RYANAIR',
          flightNumber: 'FR 1234',
          departure: { code: 'STN', city: 'London Stansted', time: '2025-07-23T06:30:00Z', terminal: '1' },
          arrival: { code: 'BCN', city: 'Barcelona', time: '2025-07-23T09:45:00Z', terminal: '2' },
          duration: '2h 15m'
        },
        returnFlight: {
          airline: 'EASYJET',
          flightNumber: 'U2 5678',
          departure: { code: 'BCN', city: 'Barcelona', time: '2025-07-30T20:10:00Z', terminal: '1' },
          arrival: { code: 'LGW', city: 'London Gatwick', time: '2025-07-30T21:25:00Z', terminal: 'S' },
          duration: '2h 15m'
        },
        totalPrice: 89.98
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n✈️  Testing: ${testCase.name}`);
    
    try {
      const response = await axios.post('http://localhost:5001/api/tickets/generate', testCase.data);
      
      if (response.data.success) {
        console.log('✅ Booking generated successfully');
        
        // Download the PDF
        const downloadResponse = await axios.get(`http://localhost:5001${response.data.downloadUrl}`, {
          responseType: 'arraybuffer'
        });
        
        fs.writeFileSync(testCase.filename, downloadResponse.data);
        console.log(`📄 PDF saved: ${testCase.filename}`);
        
        // Check if it's round trip or one way
        const hasReturn = testCase.data.returnFlight !== undefined;
        console.log(`📋 Flight type: ${hasReturn ? 'Round Trip (2 segments)' : 'One Way (1 segment)'}`);
        console.log(`📋 Expected verification text instances: ${hasReturn ? '2' : '1'}`);
        
      } else {
        console.log(`❌ Failed: ${response.data.error}`);
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }

  console.log(`\n🎯 STYLING VERIFICATION SUMMARY`);
  console.log(`===============================`);
  console.log(`📋 Generated ${testCases.length} test PDFs with different flight configurations`);
  console.log(`📋 Each PDF should show the verification text with:`);
  console.log(`   • RIGHT-ALIGNED positioning next to departure headers`);
  console.log(`   • LIGHT GREY color (#888888)`);
  console.log(`   • Smaller font size than main headers`);
  console.log(`   • Consistent styling across all flight segments`);
  
  console.log(`\n📄 Files to review:`);
  testCases.forEach(testCase => {
    console.log(`   • ${testCase.filename} - ${testCase.name}`);
  });
  
  console.log(`\n🎨 This styling should match the authentic airline reservation aesthetic`);
  console.log(`   from your sample images with proper right-aligned verification text.`);
}

testComprehensiveStyling().catch(console.error);
