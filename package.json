{"name": "<PERSON><PERSON><PERSON>-dev", "version": "1.0.0", "description": "VerifiedOnward Development Environment", "scripts": {"dev": "./start-dev.sh", "start": "./start-dev.sh", "stop": "./stop-dev.sh", "health": "./health-check.sh", "install-all": "cd backend && npm install && cd ../frontend && npm install", "clean": "./stop-dev.sh && rm -f *.log *.pid", "logs": "echo '=== Backend Log ===' && cat backend.log 2>/dev/null || echo 'No backend log' && echo '' && echo '=== Frontend Log ===' && cat frontend.log 2>/dev/null || echo 'No frontend log'"}, "keywords": ["<PERSON><PERSON><PERSON>", "development", "flight-reservation"], "author": "VerifiedOnward Team", "license": "MIT", "dependencies": {"axios": "^1.10.0", "node-fetch": "^3.3.2", "puppeteer": "^24.14.0", "react-toastify": "^11.0.5"}}