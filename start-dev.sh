#!/bin/bash

# VerifiedOnward Development Environment Startup Script
# This script starts both backend and frontend servers

set -e  # Exit on any error

echo "🚀 Starting VerifiedOnward Development Environment..."
echo "📁 Working directory: $(pwd)"
echo "⏰ Started at: $(date)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to kill processes on a port
kill_port() {
    local port=$1
    print_warning "Killing processes on port $port..."
    lsof -ti:$port | xargs kill -9 2>/dev/null || true
    sleep 2
}

# Check Node.js installation
print_status "Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed or not in PATH"
    print_error "Please install Node.js from https://nodejs.org/"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed or not in PATH"
    print_error "Please install npm (usually comes with Node.js)"
    exit 1
fi

NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)
print_success "Node.js version: $NODE_VERSION"
print_success "npm version: $NPM_VERSION"

# Check for required directories
if [ ! -d "backend" ]; then
    print_error "Backend directory not found!"
    exit 1
fi

if [ ! -d "frontend" ]; then
    print_error "Frontend directory not found!"
    exit 1
fi

# Kill any existing processes on our ports
print_status "Checking for existing processes..."
if check_port 5001; then
    print_warning "Port 5001 is in use"
    kill_port 5001
fi

if check_port 5173; then
    print_warning "Port 5173 is in use"
    kill_port 5173
fi

# Install dependencies if needed
print_status "Checking backend dependencies..."
if [ ! -d "backend/node_modules" ]; then
    print_status "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    print_success "Backend dependencies installed"
else
    print_success "Backend dependencies already installed"
fi

print_status "Checking frontend dependencies..."
if [ ! -d "frontend/node_modules" ]; then
    print_status "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    print_success "Frontend dependencies installed"
else
    print_success "Frontend dependencies already installed"
fi

# Create log directory if it doesn't exist
mkdir -p logs

# Start backend server
print_status "Starting backend server on port 5001..."
cd backend
nohup node server.js > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../logs/backend.pid
cd ..
print_success "Backend server started (PID: $BACKEND_PID)"

# Wait a moment for backend to start
sleep 3

# Start frontend server
print_status "Starting frontend server on port 5173..."
cd frontend
nohup npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > ../logs/frontend.pid
cd ..
print_success "Frontend server started (PID: $FRONTEND_PID)"

# Wait for servers to fully start
print_status "Waiting for servers to start..."
sleep 5

# Health check
print_status "Performing health checks..."

# Check backend health
if curl -s http://localhost:5001/api/health > /dev/null 2>&1; then
    print_success "Backend server is healthy (http://localhost:5001)"
else
    print_warning "Backend server health check failed"
fi

# Check frontend
if curl -s http://localhost:5173 > /dev/null 2>&1; then
    print_success "Frontend server is healthy (http://localhost:5173)"
else
    print_warning "Frontend server health check failed"
fi

echo ""
echo "🎉 VerifiedOnward Development Environment Started!"
echo ""
echo "📱 Access Points:"
echo "   🌐 Homepage: http://localhost:5173"
echo "   🔧 Backend API: http://localhost:5001/api/"
echo "   ❤️  Health Check: http://localhost:5001/api/health"
echo ""
echo "📊 Process Information:"
echo "   🖥️  Backend PID: $BACKEND_PID"
echo "   🎨 Frontend PID: $FRONTEND_PID"
echo ""
echo "📝 Useful Commands:"
echo "   ./stop-dev.sh          # Stop both servers"
echo "   ./health-check.sh      # Check server health"
echo "   npm run logs           # View server logs"
echo "   tail -f logs/backend.log   # Watch backend logs"
echo "   tail -f logs/frontend.log  # Watch frontend logs"
echo ""
echo "🚀 Happy coding!"
