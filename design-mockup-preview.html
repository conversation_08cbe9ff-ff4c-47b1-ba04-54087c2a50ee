<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VerifiedOnward - Design Mockup Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .animate-blob {
            animation: blob 7s infinite;
        }
        
        .animation-delay-2000 {
            animation-delay: 2s;
        }
        
        .animation-delay-4000 {
            animation-delay: 4s;
        }
        
        @keyframes blob {
            0% { transform: translate(0px, 0px) scale(1); }
            33% { transform: translate(30px, -50px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
            100% { transform: translate(0px, 0px) scale(1); }
        }
        
        .glassmorphism {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Comparison Container -->
    <div class="min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b p-4 text-center">
            <h1 class="text-2xl font-bold text-gray-800">VerifiedOnward Design Transformation</h1>
            <p class="text-gray-600">Current vs. Proposed Professional Design</p>
        </div>
        
        <!-- Split View -->
        <div class="grid grid-cols-1 lg:grid-cols-2 min-h-screen">
            
            <!-- CURRENT DESIGN (LEFT SIDE) -->
            <div class="bg-white border-r border-gray-200">
                <div class="p-6 bg-red-50 border-b border-red-200">
                    <h2 class="text-xl font-semibold text-red-800">❌ CURRENT DESIGN</h2>
                    <p class="text-red-600">Functional but plain appearance</p>
                </div>
                
                <!-- Current Header -->
                <header class="bg-white shadow-sm border-b border-gray-200 p-4">
                    <div class="flex justify-between items-center">
                        <div class="text-xl font-bold text-blue-600">VerifiedOnward</div>
                        <nav class="hidden md:flex space-x-6">
                            <a href="#" class="text-sm text-gray-700">Search Flights</a>
                            <a href="#" class="text-sm text-gray-700">How It Works</a>
                            <a href="#" class="text-sm text-gray-700">FAQ</a>
                        </nav>
                    </div>
                </header>
                
                <!-- Current Hero -->
                <section class="py-16 px-4">
                    <div class="max-w-4xl mx-auto text-center">
                        <h1 class="text-3xl font-semibold text-gray-700 mb-6">
                            Get Your Verified Flight Reservation — Embassy Approved in 60 Seconds
                        </h1>
                        <p class="text-lg text-gray-600 mb-8">
                            Real airline reservations with your name and a verifiable PNR — trusted by embassies and perfect for visas.
                        </p>
                        
                        <!-- Current Form -->
                        <div class="bg-white rounded-2xl shadow-xl p-6 max-w-3xl mx-auto">
                            <div class="flex justify-center mb-6">
                                <div class="flex bg-gray-100 rounded-lg p-1">
                                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md">One Way</button>
                                    <button class="px-6 py-2 text-gray-600">Return</button>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">✈️ From</label>
                                    <input type="text" placeholder="Type to search airports" class="w-full px-4 py-3 border border-gray-300 rounded-lg">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">🛬 To</label>
                                    <input type="text" placeholder="Type to search airports" class="w-full px-4 py-3 border border-gray-300 rounded-lg">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">📅 Departure Date</label>
                                    <input type="date" class="w-full px-4 py-3 border border-gray-300 rounded-lg">
                                </div>
                            </div>
                            
                            <button class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-8 rounded-lg font-semibold text-lg">
                                Search Flights
                            </button>
                        </div>
                    </div>
                </section>
            </div>
            
            <!-- PROPOSED DESIGN (RIGHT SIDE) -->
            <div class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
                <div class="p-6 bg-green-50 border-b border-green-200">
                    <h2 class="text-xl font-semibold text-green-800">✅ PROPOSED DESIGN</h2>
                    <p class="text-green-600">Professional, trustworthy, conversion-optimized</p>
                </div>
                
                <!-- Animated Background Blobs -->
                <div class="absolute inset-0">
                    <div class="absolute top-20 left-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
                    <div class="absolute top-40 right-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
                    <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
                </div>
                
                <!-- Professional Header -->
                <header class="relative z-10 bg-white/80 backdrop-blur-sm shadow-lg border-b border-white/20 p-4">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">🛫</span>
                            <span class="text-xl font-bold gradient-text">VerifiedOnward</span>
                        </div>
                        <nav class="hidden md:flex space-x-6 items-center">
                            <a href="#" class="text-sm font-medium text-gray-700 hover:text-blue-600">Search Flights</a>
                            <a href="#" class="text-sm font-medium text-gray-700 hover:text-blue-600">How It Works</a>
                            <a href="#" class="text-sm font-medium text-gray-700 hover:text-blue-600">FAQ</a>
                            <div class="flex items-center space-x-1 bg-green-100 px-3 py-1 rounded-full">
                                <span class="text-green-600">🔒</span>
                                <span class="text-xs font-medium text-green-700">SSL</span>
                            </div>
                        </nav>
                    </div>
                </header>
                
                <!-- Professional Hero -->
                <section class="relative z-10 py-12 px-4">
                    <!-- Trust Badges -->
                    <div class="flex justify-center items-center space-x-4 mb-8">
                        <div class="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg">
                            <span class="text-green-500">✅</span>
                            <span class="text-sm font-medium">Embassy Approved</span>
                        </div>
                        <div class="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg">
                            <span class="text-blue-500">🔒</span>
                            <span class="text-sm font-medium">SSL Secured</span>
                        </div>
                        <div class="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg">
                            <span class="text-yellow-500">⭐</span>
                            <span class="text-sm font-medium">4.9/5</span>
                        </div>
                    </div>
                    
                    <!-- Main Headline -->
                    <div class="text-center mb-8">
                        <h1 class="text-4xl md:text-5xl font-bold mb-4">
                            <span class="gradient-text">Embassy-Approved</span><br>
                            <span class="text-gray-900">Flight Reservations</span><br>
                            <span class="text-2xl font-medium text-gray-600 flex items-center justify-center">
                                in 60 seconds <span class="ml-2 text-yellow-500">⚡</span>
                            </span>
                        </h1>
                        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                            Real airline reservations with verifiable PNR codes — trusted by embassies worldwide
                        </p>
                    </div>
                    
                    <!-- Statistics -->
                    <div class="grid grid-cols-3 gap-4 mb-8">
                        <div class="text-center glassmorphism rounded-2xl p-4 shadow-xl">
                            <div class="text-2xl font-bold text-blue-600">50K+</div>
                            <div class="text-xs text-gray-600">Bookings</div>
                        </div>
                        <div class="text-center glassmorphism rounded-2xl p-4 shadow-xl">
                            <div class="text-2xl font-bold text-green-600">150+</div>
                            <div class="text-xs text-gray-600">Embassies</div>
                        </div>
                        <div class="text-center glassmorphism rounded-2xl p-4 shadow-xl">
                            <div class="text-2xl font-bold text-purple-600">24/7</div>
                            <div class="text-xs text-gray-600">Support</div>
                        </div>
                    </div>
                    
                    <!-- Enhanced Form -->
                    <div class="glassmorphism rounded-3xl p-6 shadow-2xl max-w-2xl mx-auto">
                        <!-- Progress Indicator -->
                        <div class="flex items-center justify-center space-x-2 mb-6">
                            <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">1</div>
                            <div class="w-12 h-0.5 bg-gray-300"></div>
                            <div class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 text-xs">2</div>
                            <div class="w-12 h-0.5 bg-gray-300"></div>
                            <div class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 text-xs">3</div>
                        </div>
                        
                        <!-- Trip Type -->
                        <div class="flex justify-center mb-6">
                            <div class="bg-gray-100 rounded-2xl p-2">
                                <button class="px-6 py-3 rounded-xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg">One Way</button>
                                <button class="px-6 py-3 rounded-xl font-semibold text-gray-600">Return</button>
                            </div>
                        </div>
                        
                        <!-- Form Fields -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                                    <span class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">🛫</span>
                                    Flying From
                                </label>
                                <input type="text" placeholder="London (LHR)" class="w-full h-12 px-4 border-2 border-gray-200 rounded-2xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all">
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                                    <span class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2">🛬</span>
                                    Flying To
                                </label>
                                <input type="text" placeholder="New York (JFK)" class="w-full h-12 px-4 border-2 border-gray-200 rounded-2xl focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all">
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                                <span class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-2">📅</span>
                                Departure Date
                            </label>
                            <input type="date" class="w-full h-12 px-4 border-2 border-gray-200 rounded-2xl focus:border-purple-500 focus:ring-4 focus:ring-purple-100 transition-all">
                        </div>
                        
                        <!-- CTA Button -->
                        <button class="w-full h-14 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white text-lg font-bold rounded-2xl shadow-2xl hover:shadow-3xl transform hover:scale-[1.02] transition-all duration-300 flex items-center justify-center space-x-3">
                            <span>🚀</span>
                            <span>Find My Perfect Flight</span>
                            <span class="bg-white/20 px-3 py-1 rounded-full text-sm">$9.98</span>
                        </button>
                        
                        <!-- Enhanced Trust Indicators -->
                        <div class="space-y-4 mt-6">
                            <div class="flex items-center justify-center space-x-4 text-sm text-gray-600">
                                <div class="flex items-center space-x-1">
                                    <span class="text-green-500">🔒</span>
                                    <span>256-bit SSL</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <span class="text-blue-500">⚡</span>
                                    <span>Instant</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <span class="text-purple-500">💳</span>
                                    <span>Secure</span>
                                </div>
                            </div>

                            <!-- Customer Testimonial -->
                            <div class="glassmorphism rounded-2xl p-4 max-w-md mx-auto">
                                <div class="flex items-center space-x-3 mb-2">
                                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                                        SM
                                    </div>
                                    <div>
                                        <div class="font-semibold text-gray-900">Sarah M.</div>
                                        <div class="text-sm text-gray-600">Verified Customer</div>
                                    </div>
                                    <div class="ml-auto text-yellow-500">⭐⭐⭐⭐⭐</div>
                                </div>
                                <p class="text-sm text-gray-700 italic">
                                    "Got my visa approved! The reservation looked completely authentic and the embassy accepted it without question."
                                </p>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
</body>
</html>
