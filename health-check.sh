#!/bin/bash

# VerifiedOnward Development Environment Health Check Script
# This script checks the health of both backend and frontend servers

echo "🏥 VerifiedOnward Health Check"
echo "⏰ $(date)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to check HTTP endpoint
check_http() {
    local url=$1
    local timeout=${2:-5}
    
    if curl -s --max-time $timeout "$url" > /dev/null 2>&1; then
        return 0  # Success
    else
        return 1  # Failed
    fi
}

# Function to get process info for a port
get_port_process() {
    local port=$1
    lsof -Pi :$port -sTCP:LISTEN 2>/dev/null | tail -n +2 | awk '{print $2, $1}' | head -1
}

echo "🔍 System Information:"
echo "   Node.js: $(node --version 2>/dev/null || echo 'Not found')"
echo "   npm: $(npm --version 2>/dev/null || echo 'Not found')"
echo "   Working Directory: $(pwd)"
echo ""

echo "🖥️  Backend Server (Port 5001):"
if check_port 5001; then
    process_info=$(get_port_process 5001)
    print_success "Port 5001 is in use (PID: $process_info)"
    
    # Check API health endpoint
    if check_http "http://localhost:5001/api/health" 3; then
        print_success "API health endpoint responding"
        
        # Get actual health response
        health_response=$(curl -s http://localhost:5001/api/health 2>/dev/null || echo "Failed to get response")
        echo "   Response: $health_response"
    else
        print_error "API health endpoint not responding"
    fi
else
    print_error "Backend server not running (port 5001 free)"
fi

echo ""

echo "🎨 Frontend Server (Port 5173):"
if check_port 5173; then
    process_info=$(get_port_process 5173)
    print_success "Port 5173 is in use (PID: $process_info)"
    
    # Check frontend accessibility
    if check_http "http://localhost:5173" 3; then
        print_success "Frontend server responding"
    else
        print_error "Frontend server not responding"
    fi
else
    print_error "Frontend server not running (port 5173 free)"
fi

echo ""

echo "📁 Process Files:"
if [ -f "logs/backend.pid" ]; then
    backend_pid=$(cat logs/backend.pid)
    if kill -0 "$backend_pid" 2>/dev/null; then
        print_success "Backend PID file exists and process is running ($backend_pid)"
    else
        print_warning "Backend PID file exists but process is not running ($backend_pid)"
    fi
else
    print_warning "Backend PID file not found"
fi

if [ -f "logs/frontend.pid" ]; then
    frontend_pid=$(cat logs/frontend.pid)
    if kill -0 "$frontend_pid" 2>/dev/null; then
        print_success "Frontend PID file exists and process is running ($frontend_pid)"
    else
        print_warning "Frontend PID file exists but process is not running ($frontend_pid)"
    fi
else
    print_warning "Frontend PID file not found"
fi

echo ""

echo "📊 Log Files:"
if [ -f "logs/backend.log" ]; then
    backend_log_size=$(wc -l < logs/backend.log)
    print_success "Backend log exists ($backend_log_size lines)"
    echo "   Last 3 lines:"
    tail -3 logs/backend.log | sed 's/^/   /'
else
    print_warning "Backend log file not found"
fi

if [ -f "logs/frontend.log" ]; then
    frontend_log_size=$(wc -l < logs/frontend.log)
    print_success "Frontend log exists ($frontend_log_size lines)"
    echo "   Last 3 lines:"
    tail -3 logs/frontend.log | sed 's/^/   /'
else
    print_warning "Frontend log file not found"
fi

echo ""

# Overall health assessment
backend_healthy=false
frontend_healthy=false

if check_port 5001 && check_http "http://localhost:5001/api/health" 3; then
    backend_healthy=true
fi

if check_port 5173 && check_http "http://localhost:5173" 3; then
    frontend_healthy=true
fi

echo "🎯 Overall Status:"
if $backend_healthy && $frontend_healthy; then
    print_success "All services are healthy!"
    echo ""
    echo "🌐 Access Points:"
    echo "   Homepage: http://localhost:5173"
    echo "   Backend API: http://localhost:5001/api/"
    echo "   Health Check: http://localhost:5001/api/health"
elif $backend_healthy; then
    print_warning "Backend is healthy, but frontend has issues"
    echo "   Try: cd frontend && npm run dev"
elif $frontend_healthy; then
    print_warning "Frontend is healthy, but backend has issues"
    echo "   Try: cd backend && node server.js"
else
    print_error "Both services have issues"
    echo "   Try: ./start-dev.sh"
fi

echo ""
echo "📝 Useful Commands:"
echo "   ./start-dev.sh     # Start both servers"
echo "   ./stop-dev.sh      # Stop both servers"
echo "   npm run logs       # View all logs"
echo "   tail -f logs/backend.log   # Watch backend logs"
echo "   tail -f logs/frontend.log  # Watch frontend logs"
