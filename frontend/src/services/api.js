import axios from 'axios';

// Create axios instance with base configuration
// Use proxy in development, full URL in production
const baseURL = import.meta.env.DEV
  ? '/api' // Use Vite proxy in development
  : (import.meta.env.VITE_API_BASE_URL || 'http://localhost:5001/api');
console.log('🔗 API Base URL:', baseURL);
console.log('🔗 Environment:', import.meta.env.DEV ? 'development' : 'production');

const api = axios.create({
  baseURL,
  timeout: 20000, // 20 seconds timeout for API requests
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('Response error:', error.response?.data || error.message);

    // Handle specific error cases
    if (error.response?.status === 404) {
      console.error('API endpoint not found');
    } else if (error.response?.status >= 500) {
      console.error('Server error occurred');
    }

    return Promise.reject(error);
  }
);

// Flight API methods - SerpAPI Google Flights integration
export const flightAPI = {
  // Search airports using SerpAPI
  searchAirports: async (query) => {
    const response = await api.get('/flights/airports', {
      params: { query }
    });
    return response.data;
  },

  // Search flights using SerpAPI
  searchFlights: async (searchData) => {
    console.log('🔍 Searching flights with data:', searchData);
    try {
      const response = await api.post('/flights/search', searchData);
      console.log('✅ Flight search response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Flight search error:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        config: error.config
      });
      throw error;
    }
  },

  // Get flight details by ID
  getFlightDetails: async (flightId) => {
    const response = await api.get(`/flights/${flightId}`);
    return response.data;
  }
};

export const paymentAPI = {
  // Create Stripe payment intent
  createStripeIntent: async (amount, metadata = {}) => {
    try {
      const response = await api.post('/payments/stripe/create-intent', {
        amount,
        currency: 'usd',
        metadata
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to create payment intent');
    }
  },

  // Confirm Stripe payment
  confirmStripePayment: async (paymentIntentId) => {
    try {
      const response = await api.post('/payments/stripe/confirm', {
        paymentIntentId
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to confirm payment');
    }
  },

  // Create PayPal order
  createPayPalOrder: async (amount) => {
    try {
      const response = await api.post('/payments/paypal/create-order', {
        amount,
        currency: 'USD'
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to create PayPal order');
    }
  },

  // Capture PayPal payment
  capturePayPalPayment: async (orderId) => {
    try {
      const response = await api.post(`/payments/paypal/capture/${orderId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to capture PayPal payment');
    }
  }
};

export const ticketAPI = {
  // Generate and send ticket
  generateTicket: async (ticketData) => {
    try {
      const response = await api.post('/tickets/generate', ticketData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to generate ticket');
    }
  }
};

// Health check
export const healthCheck = async () => {
  try {
    const response = await api.get('/health');
    return response.data;
  } catch (error) {
    throw new Error('API health check failed');
  }
};

export default api;
