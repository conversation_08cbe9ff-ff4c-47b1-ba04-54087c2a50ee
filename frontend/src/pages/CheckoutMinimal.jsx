import React from 'react';

const CheckoutMinimal = () => {
  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f0f0f0', 
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ color: '#333', fontSize: '24px' }}>
        🛒 MINIMAL CHECKOUT PAGE
      </h1>
      <p style={{ color: '#666' }}>
        This is a minimal checkout page with inline styles.
      </p>
      <p style={{ color: '#666' }}>
        URL: {window.location.href}
      </p>
      <p style={{ color: '#666' }}>
        Time: {new Date().toISOString()}
      </p>
      <div style={{ 
        marginTop: '20px', 
        padding: '15px', 
        backgroundColor: 'white', 
        borderRadius: '5px',
        border: '1px solid #ddd'
      }}>
        <h2 style={{ margin: '0 0 10px 0' }}>Status Check:</h2>
        <ul style={{ margin: 0, paddingLeft: '20px' }}>
          <li>✅ React component rendering</li>
          <li>✅ JavaScript working</li>
          <li>✅ Inline styles working</li>
          <li>✅ No external dependencies</li>
        </ul>
      </div>
    </div>
  );
};

export default CheckoutMinimal;
