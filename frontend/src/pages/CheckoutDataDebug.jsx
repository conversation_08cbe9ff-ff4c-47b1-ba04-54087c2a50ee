import React from 'react';
import { useLocation } from 'react-router-dom';
import { useBooking } from '../context/BookingContext';
import { getCheckoutData } from '../utils/sessionStorageHelper';

const CheckoutDataDebug = () => {
  const location = useLocation();
  const {
    selectedFlight,
    selectedOutboundFlight,
    selectedReturnFlight,
    passengers,
    email,
    tripType
  } = useBooking();

  const sessionData = getCheckoutData();

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Checkout Data Debug
        </h1>

        <div className="space-y-6">
          {/* React Router location.state */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-blue-600 mb-4">
              React Router location.state
            </h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(location.state, null, 2)}
            </pre>
          </div>

          {/* BookingContext data */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-green-600 mb-4">
              BookingContext data
            </h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify({
                selectedFlight,
                selectedOutboundFlight,
                selectedReturnFlight,
                passengers,
                email,
                tripType
              }, null, 2)}
            </pre>
          </div>

          {/* SessionStorage data */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-purple-600 mb-4">
              SessionStorage data
            </h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(sessionData, null, 2)}
            </pre>
          </div>

          {/* localStorage data */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-orange-600 mb-4">
              localStorage data
            </h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify({
                bookingSelectedFlight: localStorage.getItem('bookingSelectedFlight'),
                bookingSelectedOutboundFlight: localStorage.getItem('bookingSelectedOutboundFlight'),
                bookingSelectedReturnFlight: localStorage.getItem('bookingSelectedReturnFlight'),
                bookingPassengers: localStorage.getItem('bookingPassengers'),
                bookingEmail: localStorage.getItem('bookingEmail'),
                checkoutBackupData: localStorage.getItem('checkoutBackupData')
              }, null, 2)}
            </pre>
          </div>

          {/* Navigation buttons */}
          <div className="flex gap-4">
            <button
              onClick={() => window.history.back()}
              className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
            >
              ← Back
            </button>
            <button
              onClick={() => window.location.href = '/checkout'}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Go to Checkout
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutDataDebug;
