import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useBooking } from '../context/BookingContext';

const CheckoutDemo = () => {
  const navigate = useNavigate();
  const { 
    setSelectedFlight, 
    setSelectedOutboundFlight, 
    setSelectedReturnFlight,
    setPassengers, 
    setEmail, 
    setTripType 
  } = useBooking();

  useEffect(() => {
    // Set up demo data
    const demoOutboundFlight = {
      airline: {
        name: 'British Airways',
        logo: 'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png'
      },
      flightNumber: 'BA 117',
      from: 'London Heathrow (LHR)',
      to: 'New York JFK (JFK)',
      departure: {
        airport: 'London Heathrow (LHR)',
        time: '10:30 AM'
      },
      arrival: {
        airport: 'New York JFK (JFK)',
        time: '2:15 PM'
      },
      duration: '8h 45m',
      price: '4.99'
    };

    const demoReturnFlight = {
      airline: {
        name: 'British Airways',
        logo: 'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png'
      },
      flightNumber: 'BA 118',
      from: 'New York JFK (JFK)',
      to: 'London Heathrow (LHR)',
      departure: {
        airport: 'New York JFK (JFK)',
        time: '9:00 PM'
      },
      arrival: {
        airport: 'London Heathrow (LHR)',
        time: '8:30 AM+1'
      },
      duration: '7h 30m',
      price: '4.99'
    };

    const demoPassengers = [
      { id: 1, firstName: 'John', lastName: 'Smith' },
      { id: 2, firstName: 'Jane', lastName: 'Doe' }
    ];

    const demoEmail = '<EMAIL>';

    // Set the demo data in context
    setSelectedOutboundFlight(demoOutboundFlight);
    setSelectedReturnFlight(demoReturnFlight);
    setPassengers(demoPassengers);
    setEmail(demoEmail);
    setTripType('return');

    // Navigate to checkout after a short delay
    setTimeout(() => {
      navigate('/checkout');
    }, 500);
  }, [navigate, setSelectedOutboundFlight, setSelectedReturnFlight, setPassengers, setEmail, setTripType]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Setting up demo checkout data...</p>
      </div>
    </div>
  );
};

export default CheckoutDemo;
