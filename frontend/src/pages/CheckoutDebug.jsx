import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const CheckoutDebug = () => {
  const navigate = useNavigate();
  const [testResults, setTestResults] = useState([]);

  const addResult = (test, status, message) => {
    setTestResults(prev => [...prev, { test, status, message, timestamp: Date.now() }]);
  };

  const runTests = async () => {
    setTestResults([]);
    
    // Test 1: Check if checkout page loads
    addResult('Page Load', 'running', 'Testing checkout page load...');
    try {
      const response = await fetch('/checkout');
      if (response.ok) {
        addResult('Page Load', 'success', 'Checkout page loads successfully');
      } else {
        addResult('Page Load', 'error', `HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      addResult('Page Load', 'error', error.message);
    }

    // Test 2: Check API connectivity
    addResult('API Test', 'running', 'Testing API connectivity...');
    try {
      const response = await fetch('/api/health');
      const data = await response.json();
      if (data.status === 'OK') {
        addResult('API Test', 'success', 'API is responding correctly');
      } else {
        addResult('API Test', 'error', 'API returned unexpected response');
      }
    } catch (error) {
      addResult('API Test', 'error', error.message);
    }

    // Test 3: Check localStorage
    addResult('LocalStorage', 'running', 'Testing localStorage access...');
    try {
      localStorage.setItem('test', 'value');
      const value = localStorage.getItem('test');
      localStorage.removeItem('test');
      if (value === 'value') {
        addResult('LocalStorage', 'success', 'localStorage is working');
      } else {
        addResult('LocalStorage', 'error', 'localStorage read/write failed');
      }
    } catch (error) {
      addResult('LocalStorage', 'error', error.message);
    }

    // Test 4: Set up test data
    addResult('Test Data', 'running', 'Setting up test booking data...');
    try {
      const testData = {
        bookingSelectedFlight: JSON.stringify({
          id: 'test-flight-1',
          flight: {
            number: 'BA 123',
            departure: { airport: 'LHR', iataCode: 'LHR', city: 'London Heathrow', time: '2025-07-15 10:00' },
            arrival: { airport: 'JFK', iataCode: 'JFK', city: 'New York JFK', time: '2025-07-15 18:00' },
            duration: '8h 00m'
          },
          airline: { name: 'British Airways', code: 'BA', logo: 'https://www.gstatic.com/flights/airline_logos/70px/BA.png' },
          price: { total: 4.99, currency: 'USD', displayPrice: 4.99, originalPrice: 650 }
        }),
        bookingPassengers: JSON.stringify([{ id: 1, firstName: 'John', lastName: 'Doe', dateOfBirth: '1990-01-01' }]),
        bookingEmail: '<EMAIL>',
        bookingTripType: 'oneWay'
      };

      Object.keys(testData).forEach(key => {
        localStorage.setItem(key, testData[key]);
      });

      addResult('Test Data', 'success', 'Test booking data set successfully');
    } catch (error) {
      addResult('Test Data', 'error', error.message);
    }
  };

  const clearData = () => {
    const keys = ['bookingSelectedFlight', 'bookingPassengers', 'bookingEmail', 'bookingTripType', 'bookingSelectedOutboundFlight', 'bookingSelectedReturnFlight'];
    keys.forEach(key => localStorage.removeItem(key));
    addResult('Clear Data', 'success', 'All booking data cleared');
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'running': return '⏳';
      default: return '⚪';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50';
      case 'error': return 'text-red-600 bg-red-50';
      case 'running': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            🔧 Checkout Debug Console
          </h1>
          
          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-800">Quick Actions</h2>
              
              <button
                onClick={runTests}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
              >
                🧪 Run All Tests
              </button>
              
              <button
                onClick={clearData}
                className="w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors font-semibold"
              >
                🗑️ Clear All Data
              </button>
              
              <button
                onClick={() => navigate('/checkout')}
                className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-semibold"
              >
                🚀 Go to Checkout
              </button>
              
              <button
                onClick={() => navigate('/checkout?bypass=true')}
                className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors font-semibold"
              >
                🎭 Go to Checkout (Demo Mode)
              </button>
            </div>

            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-800">Navigation</h2>
              
              <button
                onClick={() => navigate('/search')}
                className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
              >
                🔍 Search Flights
              </button>
              
              <button
                onClick={() => navigate('/success')}
                className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
              >
                ✅ Success Page
              </button>
              
              <button
                onClick={() => navigate('/')}
                className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
              >
                🏠 Home Page
              </button>
            </div>
          </div>

          {testResults.length > 0 && (
            <div className="border-t pt-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Test Results</h2>
              <div className="space-y-2">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg border ${getStatusColor(result.status)}`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{getStatusIcon(result.status)}</span>
                        <span className="font-medium">{result.test}</span>
                      </div>
                      <span className="text-xs text-gray-500">
                        {new Date(result.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <p className="text-sm mt-1 ml-6">{result.message}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CheckoutDebug;
