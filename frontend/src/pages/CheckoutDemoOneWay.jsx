import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useBooking } from '../context/BookingContext';

const CheckoutDemoOneWay = () => {
  const navigate = useNavigate();
  const { 
    setSelectedFlight, 
    setSelectedOutboundFlight, 
    setSelectedReturnFlight,
    setPassengers, 
    setEmail, 
    setTripType 
  } = useBooking();

  useEffect(() => {
    // Set up demo data for one-way flight
    const demoFlight = {
      airline: {
        name: 'British Airways',
        logo: 'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png'
      },
      flightNumber: 'BA 117',
      from: 'London Heathrow (LHR)',
      to: 'New York JFK (JFK)',
      departure: {
        airport: 'London Heathrow (LHR)',
        time: '10:30 AM'
      },
      arrival: {
        airport: 'New York JFK (JFK)',
        time: '2:15 PM'
      },
      duration: '8h 45m',
      price: '4.99'
    };

    const demoPassengers = [
      { id: 1, firstName: '<PERSON>', lastName: '<PERSON>' }
    ];

    const demoEmail = '<EMAIL>';

    // Set the demo data in context
    setSelectedFlight(demoFlight);
    setSelectedOutboundFlight(null); // Clear return flight data
    setSelectedReturnFlight(null);
    setPassengers(demoPassengers);
    setEmail(demoEmail);
    setTripType('oneWay');

    // Navigate to checkout after a short delay
    setTimeout(() => {
      navigate('/checkout');
    }, 500);
  }, [navigate, setSelectedFlight, setSelectedOutboundFlight, setSelectedReturnFlight, setPassengers, setEmail, setTripType]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Setting up one-way flight demo...</p>
      </div>
    </div>
  );
};

export default CheckoutDemoOneWay;
