import React from 'react';
import { useNavigate } from 'react-router-dom';

const CheckoutTestPage = () => {
  const navigate = useNavigate();

  const testOneWayCheckout = () => {
    const checkoutData = {
      tripType: 'oneWay',
      passengers: [
        {
          id: 1,
          firstName: '<PERSON>',
          lastName: 'Doe'
        }
      ],
      email: '<EMAIL>',
      totalPrice: 4.99,
      flight: {
        id: 'test-flight-1',
        airline: {
          name: 'Test Airlines',
          code: 'TA'
        },
        flightNumber: 'TA 123',
        flight: {
          departure: {
            time: '2024-01-01T10:00:00Z',
            airport: 'JFK'
          },
          arrival: {
            time: '2024-01-01T14:00:00Z',
            airport: 'LAX'
          }
        },
        price: {
          total: 4.99,
          displayPrice: '$4.99'
        }
      }
    };

    console.log('🧪 Navigating to checkout with test data:', checkoutData);
    
    navigate('/checkout', {
      state: checkoutData
    });
  };

  const testReturnCheckout = () => {
    const checkoutData = {
      tripType: 'return',
      passengers: [
        {
          id: 1,
          firstName: '<PERSON>',
          lastName: '<PERSON>'
        }
      ],
      email: '<EMAIL>',
      totalPrice: 9.98,
      outboundFlight: {
        id: 'test-outbound-1',
        airline: {
          name: 'Test Airlines',
          code: 'TA'
        },
        flightNumber: 'TA 456',
        flight: {
          departure: {
            time: '2024-01-01T10:00:00Z',
            airport: 'JFK'
          },
          arrival: {
            time: '2024-01-01T14:00:00Z',
            airport: 'LAX'
          }
        },
        price: {
          total: 4.99,
          displayPrice: '$4.99'
        }
      },
      returnFlight: {
        id: 'test-return-1',
        airline: {
          name: 'Test Airlines',
          code: 'TA'
        },
        flightNumber: 'TA 789',
        flight: {
          departure: {
            time: '2024-01-05T16:00:00Z',
            airport: 'LAX'
          },
          arrival: {
            time: '2024-01-05T20:00:00Z',
            airport: 'JFK'
          }
        },
        price: {
          total: 4.99,
          displayPrice: '$4.99'
        }
      }
    };

    console.log('🧪 Navigating to checkout with return test data:', checkoutData);
    
    navigate('/checkout', {
      state: checkoutData
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            🧪 Checkout Test Page
          </h1>
          
          <p className="text-gray-600 mb-8 text-center">
            Use these buttons to test the checkout flow with valid data
          </p>

          <div className="space-y-4">
            <button
              onClick={testOneWayCheckout}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
            >
              Test One-Way Flight Checkout
            </button>

            <button
              onClick={testReturnCheckout}
              className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-semibold"
            >
              Test Return Flight Checkout
            </button>

            <button
              onClick={() => navigate('/checkout')}
              className="w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors font-semibold"
            >
              Test Empty Checkout (Should Show Error)
            </button>

            <button
              onClick={() => navigate('/')}
              className="w-full bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors font-semibold"
            >
              ← Back to Home
            </button>
          </div>

          <div className="mt-8 p-4 bg-gray-100 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">How to use:</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Click "Test One-Way" to test single flight checkout</li>
              <li>• Click "Test Return" to test round-trip checkout</li>
              <li>• Click "Test Empty" to see the error handling</li>
              <li>• All test data includes valid flight and passenger information</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutTestPage;
