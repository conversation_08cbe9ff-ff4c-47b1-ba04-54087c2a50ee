import React from 'react';

const CheckoutTest2 = () => {
  console.log('🚨 CheckoutTest2: Component rendering!');
  
  return (
    <div className="min-h-screen bg-green-100 p-8">
      <h1 className="text-3xl font-bold text-green-800">✅ CHECKOUT TEST 2</h1>
      <p className="text-green-600 text-xl mt-4">This is a simple test component!</p>
      <p className="text-green-600">URL: {window.location.href}</p>
      <p className="text-green-600">Time: {new Date().toISOString()}</p>
      <div className="mt-8 p-4 bg-white rounded shadow">
        <h2 className="font-bold text-lg">Test Results:</h2>
        <ul className="mt-2 space-y-1">
          <li>✅ React component working</li>
          <li>✅ React Router working</li>
          <li>✅ TailwindCSS working</li>
          <li>✅ No JavaScript errors</li>
        </ul>
      </div>
    </div>
  );
};

export default CheckoutTest2;
