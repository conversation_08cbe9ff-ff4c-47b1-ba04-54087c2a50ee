import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getAllBlogPosts } from '../data/blogPosts';
import UnifiedCTA from '../components/UnifiedCTA';

const BlogPage = () => {
  const blogPosts = getAllBlogPosts();

  // Set page title and meta description
  useEffect(() => {
    document.title = 'Visa Resources & Expert Travel Tips | VerifiedOnward';

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = 'Expert visa tips and resources for flight reservations, embassy applications, and travel planning. Get the latest guides and advice for successful visa applications.';

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservation in 60 Seconds';
    };
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-brand-50/30">
      {/* Premium Header Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 via-blue-500/5 to-accent-500/5"></div>

        <div className="container-modern relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center bg-brand-100 text-brand-700 px-6 py-3 rounded-full text-sm font-semibold mb-8">
              <span className="w-2 h-2 bg-brand-500 rounded-full mr-3 animate-pulse"></span>
              VISA RESOURCES & TIPS
            </div>

            <h1 className="text-5xl md:text-6xl font-black text-brand-800 mb-8 leading-tight">
              Expert
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent"> Travel Guides</span>
            </h1>

            <p className="text-xl md:text-2xl text-brand-700 leading-relaxed font-medium mb-12">
              Professional visa application guides, embassy requirements, and travel tips from industry experts.
              <br className="hidden md:block" />
              <strong className="text-brand-800">Everything you need for successful visa applications.</strong>
            </p>
          </motion.div>
        </div>
      </section>

      {/* Premium Blog Posts Grid */}
      <section className="py-16">
        <div className="container-modern">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
          >
            {blogPosts.map((post) => (
              <motion.article
                key={post.id}
                variants={itemVariants}
                className="premium-card group hover:-translate-y-2 transition-all duration-300"
              >
                <div className="p-8">
                  {/* Premium Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 text-xs font-semibold bg-brand-100 text-brand-700 rounded-full border border-brand-200"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Premium Title */}
                  <h2 className="text-2xl font-bold text-neutral-800 mb-4 group-hover:text-brand-600 transition-colors leading-tight">
                    <Link to={`/blog/${post.slug}`} className="hover:underline">
                      {post.title}
                    </Link>
                  </h2>

                  {/* Premium Meta Information */}
                  <div className="flex items-center text-sm text-neutral-500 mb-6">
                    <svg className="w-4 h-4 mr-2 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="mr-6 font-medium">{new Date(post.publishDate).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}</span>
                    <svg className="w-4 h-4 mr-2 text-accent-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="font-medium">{post.readTime}</span>
                  </div>

                  {/* Premium Excerpt */}
                  <p className="text-neutral-600 mb-8 leading-relaxed text-lg">
                    {post.excerpt}
                  </p>

                  {/* Premium Read More Button */}
                  <Link
                    to={`/blog/${post.slug}`}
                    className="inline-flex items-center bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white font-bold px-6 py-3 rounded-xl transition-all duration-300 shadow-aviation hover:shadow-aviation-hover transform hover:scale-105 group"
                  >
                    <span>Read Full Guide</span>
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </Link>
                </div>
            </motion.article>
          ))}
          </motion.div>

          {/* Premium Call to Action Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-center mt-16"
          >
            <UnifiedCTA />
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default BlogPage;
