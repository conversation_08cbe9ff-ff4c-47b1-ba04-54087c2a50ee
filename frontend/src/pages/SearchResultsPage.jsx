import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useBooking } from '../context/BookingContext';
import { flightAPI } from '../services/api';
import FlightCard from '../components/FlightCard';
import LoadingSpinner from '../components/LoadingSpinner';
import FlightSummaryWithPassengerForm from '../components/FlightSummaryWithPassengerForm';

const SearchResultsPage = () => {
  console.log('🚀 SearchResultsPage: Component rendering...');
  const navigate = useNavigate();
  const {
    searchData,
    setSelectedFlight: setBookingSelectedFlight,
    setSelectedOutboundFlight,
    setSelectedReturnFlight,
    selectedOutboundFlight,
    selectedReturnFlight,
    passengers: bookingPassengers,
    email: bookingEmail,
    setPassengers: setBookingPassengers,
    setEmail: setBookingEmail
  } = useBooking();

  // State for different flight types
  const [outboundFlights, setOutboundFlights] = useState([]);
  const [returnFlights, setReturnFlights] = useState([]);
  const [oneWayFlights, setOneWayFlights] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [loadingMessage, setLoadingMessage] = useState('Searching for flights...');
  const [selectionError, setSelectionError] = useState(null);

  // Passenger details state
  const [passengers, setPassengers] = useState(
    bookingPassengers.length > 0 ? bookingPassengers : [{ id: 1, firstName: '', lastName: '' }]
  );
  const [email, setEmail] = useState(bookingEmail || '');
  const [isPassengerDetailsLoading, setIsPassengerDetailsLoading] = useState(false);

  useEffect(() => {
    if (!searchData) {
      navigate('/');
      return;
    }

    searchFlights();
  }, [searchData]);

  const searchFlights = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setFlights([]); // Clear previous flights
      setLoadingProgress(0);
      setLoadingMessage('Connecting to flight search...');

      // Progress simulation for better UX
      const progressInterval = setInterval(() => {
        setLoadingProgress(prev => {
          if (prev < 90) {
            const increment = Math.random() * 15 + 5; // Random increment between 5-20
            return Math.min(prev + increment, 90);
          }
          return prev;
        });
      }, 1000);

      // Update loading messages
      setTimeout(() => setLoadingMessage('Searching live flight data...'), 2000);
      setTimeout(() => setLoadingMessage('Processing results...'), 8000);

      console.log('🔍 Searching flights with data:', searchData);
      const response = await flightAPI.searchFlights(searchData);
      console.log('📥 Received response:', response);

      clearInterval(progressInterval);
      setLoadingProgress(100);

      if (response && response.success) {
        const { data } = response;

        if (data.tripType === 'return') {
          // Handle return flight response
          console.log(`✅ Setting ${data.outboundFlights?.length || 0} outbound and ${data.returnFlights?.length || 0} return flights`);
          setOutboundFlights(data.outboundFlights || []);
          setReturnFlights(data.returnFlights || []);

          if ((data.outboundFlights?.length || 0) === 0 || (data.returnFlights?.length || 0) === 0) {
            setError('No flights found for your search criteria. Please try different airports or dates.');
          }
        } else {
          // Handle one-way flight response
          if (Array.isArray(data.flights)) {
            console.log(`✅ Setting ${data.flights.length} one-way flights`);
            setOneWayFlights(data.flights);

            if (data.flights.length === 0) {
              setError('No flights found for your search criteria. Please try different airports or dates.');
            }
          } else {
            console.error('❌ Invalid one-way flight data format:', data.flights);
            setError('Invalid flight data received from server');
            setOneWayFlights(getMockFlights());
          }
        }
      } else {
        console.error('❌ API response indicates failure:', response);
        setError(response?.message || 'No flights found for your search criteria');

        // Set mock data based on trip type
        if (searchData.tripType === 'return') {
          setOutboundFlights(getMockFlights());
          setReturnFlights(getMockFlights());
        } else {
          setOneWayFlights(getMockFlights());
        }
      }
    } catch (error) {
      console.error('❌ Flight search error:', error);

      // Clear progress interval if it exists
      clearInterval(progressInterval);

      // Provide specific error messages based on error type
      let errorMessage = 'Failed to search flights';
      if (error.message.includes('timeout')) {
        errorMessage = 'Search took too long. Showing sample results - please try again for live data.';
      } else if (error.message.includes('Network Error')) {
        errorMessage = 'Network error. Showing sample results - please check your connection.';
      } else if (error.message.includes('500')) {
        errorMessage = 'Server temporarily unavailable. Showing sample results.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);

      // No fallback data - show error state
      console.log('❌ Flight search failed, no fallback available');
      setOneWayFlights([]);
      setOutboundFlights([]);
      setReturnFlights([]);
    } finally {
      setIsLoading(false);
      setLoadingProgress(100);
    }
  };

  const handleOneWayFlightSelect = (flight) => {
    // Handle null flight (unselection)
    if (!flight) {
      setSelectedOutboundFlight(null);
      setBookingSelectedFlight(null);
      return;
    }

    // Toggle selection: if same flight is clicked, unselect it
    if (selectedOutboundFlight?.id === flight.id) {
      setSelectedOutboundFlight(null);
      setBookingSelectedFlight(null);
    } else {
      // Store selected flight in context for one-way trips
      setSelectedOutboundFlight(flight);
      setBookingSelectedFlight(flight);
      // No automatic navigation - show sidebar instead
    }
  };

  const handleOutboundFlightSelect = (flight) => {
    // Handle null flight (unselection)
    if (!flight) {
      setSelectedOutboundFlight(null);
      setSelectionError(null);
      return;
    }

    // Toggle selection: if same flight is clicked, unselect it
    if (selectedOutboundFlight?.id === flight.id) {
      setSelectedOutboundFlight(null);
    } else {
      // Store selected outbound flight
      setSelectedOutboundFlight(flight);
    }
    // Clear any selection errors
    setSelectionError(null);
  };

  const handleReturnFlightSelect = (flight) => {
    // Handle null flight (unselection)
    if (!flight) {
      setSelectedReturnFlight(null);
      setSelectionError(null);
      return;
    }

    // Toggle selection: if same flight is clicked, unselect it
    if (selectedReturnFlight?.id === flight.id) {
      setSelectedReturnFlight(null);
    } else {
      // Store selected return flight
      setSelectedReturnFlight(flight);
    }
    // Clear any selection errors
    setSelectionError(null);
  };

  // Removed handleContinueToPassengerDetails - using inline payment flow instead

  const handleNewSearch = () => {
    navigate('/');
  };

  // Handle passenger details form submission - REDIRECT TO CHECKOUT
  const handlePassengerDetailsSubmit = (formData) => {
    console.log('🚀 SearchResultsPage: Passenger details submitted, redirecting to checkout');
    console.log('🚀 SearchResultsPage: Form data:', formData);

    // Save passenger data to booking context
    setIsPassengerDetailsLoading(true);
    setBookingPassengers(formData.passengers);
    setBookingEmail(formData.email);

    // Prepare checkout data for navigation state
    const checkoutData = {
      searchData,
      tripType: searchData.tripType,
      passengers: formData.passengers,
      email: formData.email,
      totalPrice: searchData.tripType === 'return' ? 9.98 : 4.99
    };

    // Add flight data based on trip type
    if (searchData.tripType === 'oneWay' && selectedOutboundFlight) {
      checkoutData.flight = {
        id: selectedOutboundFlight.id,
        airline: selectedOutboundFlight.airline,
        flightNumber: selectedOutboundFlight.flight?.number || selectedOutboundFlight.flightNumber,
        departure: selectedOutboundFlight.departure,
        arrival: selectedOutboundFlight.arrival,
        duration: selectedOutboundFlight.duration,
        price: selectedOutboundFlight.price
      };
    } else if (searchData.tripType === 'return') {
      if (selectedOutboundFlight) {
        checkoutData.outboundFlight = {
          id: selectedOutboundFlight.id,
          airline: selectedOutboundFlight.airline,
          flightNumber: selectedOutboundFlight.flight?.number || selectedOutboundFlight.flightNumber,
          departure: selectedOutboundFlight.departure,
          arrival: selectedOutboundFlight.arrival,
          duration: selectedOutboundFlight.duration,
          price: selectedOutboundFlight.price
        };
      }
      if (selectedReturnFlight) {
        checkoutData.returnFlight = {
          id: selectedReturnFlight.id,
          airline: selectedReturnFlight.airline,
          flightNumber: selectedReturnFlight.flight?.number || selectedReturnFlight.flightNumber,
          departure: selectedReturnFlight.departure,
          arrival: selectedReturnFlight.arrival,
          duration: selectedReturnFlight.duration,
          price: selectedReturnFlight.price
        };
      }
    }

    console.log('🚀 SearchResultsPage: Prepared checkout data:', checkoutData);

    // Also save to localStorage as backup (in case of page refresh)
    try {
      localStorage.setItem('checkoutBackupData', JSON.stringify(checkoutData));
      localStorage.setItem('bookingPassengers', JSON.stringify(formData.passengers));
      localStorage.setItem('bookingEmail', formData.email);
      localStorage.setItem('bookingTripType', tripType);

      // Save selected flights
      if (selectedOutboundFlight) {
        localStorage.setItem('bookingSelectedOutboundFlight', JSON.stringify(selectedOutboundFlight));
      }
      if (selectedReturnFlight) {
        localStorage.setItem('bookingSelectedReturnFlight', JSON.stringify(selectedReturnFlight));
      }
      if (selectedFlight) {
        localStorage.setItem('bookingSelectedFlight', JSON.stringify(selectedFlight));
      }

      console.log('✅ SearchResultsPage: Backup data saved to localStorage');
    } catch (error) {
      console.warn('⚠️ SearchResultsPage: Failed to save backup data:', error);
    }

    // Navigate to checkout page with state data
    setTimeout(() => {
      setIsPassengerDetailsLoading(false);
      console.log('✅ SearchResultsPage: Navigating to checkout with state');
      navigate('/checkout', {
        state: checkoutData
      });
    }, 200);
  };

  // Handle passenger data changes in real-time
  const handlePassengersChange = (updatedPassengers) => {
    setPassengers(updatedPassengers);
    setBookingPassengers(updatedPassengers);
  };

  const handleEmailChange = (updatedEmail) => {
    setEmail(updatedEmail);
    setBookingEmail(updatedEmail);
  };

  // Handle edit flight actions
  const handleEditOutbound = () => {
    setSelectedOutboundFlight(null);
    setBookingSelectedFlight(null);
  };

  const handleEditReturn = () => {
    setSelectedReturnFlight(null);
  };

  if (!searchData) {
    return null;
  }

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* Search Summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-md p-6 mb-8"
        >
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{searchData.origin}</div>
                <div className="text-sm text-gray-500">From</div>
              </div>
              <div className="text-blue-500">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{searchData.destination}</div>
                <div className="text-sm text-gray-500">To</div>
              </div>
              <div className="text-center ml-8">
                <div className="text-lg font-semibold text-gray-900">
                  {new Date(searchData.date).toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                  })}
                </div>
                <div className="text-sm text-gray-500">Departure</div>
              </div>
            </div>
            
            <button
              onClick={handleNewSearch}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              New Search
            </button>
          </div>
        </motion.div>

        {/* Enhanced Loading State */}
        {isLoading && (
          <motion.div
            className="flex flex-col justify-center items-center py-20"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <LoadingSpinner size="lg" text={loadingMessage} />

            {/* Progress Bar */}
            <div className="w-64 bg-gray-200 rounded-full h-2 mt-6">
              <motion.div
                className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${loadingProgress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
            <p className="text-sm text-gray-500 mt-2">{Math.round(loadingProgress)}% complete</p>

            {/* Helpful tip */}
            <div className="mt-6 text-center max-w-md">
              <p className="text-sm text-gray-600">
                🔍 Searching live flight data from multiple airlines...
              </p>
              <p className="text-xs text-gray-500 mt-1">
                This usually takes 5-15 seconds for the best results
              </p>
            </div>
          </motion.div>
        )}

        {/* Error State */}
        {error && !isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-50 border border-red-200 rounded-lg p-8 text-center"
          >
            <div className="text-6xl mb-4">⚠️</div>
            <h3 className="text-xl font-semibold text-red-800 mb-3">
              Flight Search Temporarily Unavailable
            </h3>
            <div className="text-red-600 text-base mb-6 max-w-md mx-auto">
              {error}
            </div>

            <div className="space-y-3">
              <button
                onClick={searchFlights}
                className="bg-red-600 text-white px-8 py-3 rounded-lg hover:bg-red-700 transition-colors font-semibold"
              >
                Try Again
              </button>

              <div className="text-sm text-gray-600">
                <p className="mb-2">If the problem persists, try:</p>
                <ul className="text-left inline-block">
                  <li>• Selecting different airports</li>
                  <li>• Choosing a different date</li>
                  <li>• Refreshing the page</li>
                </ul>
              </div>

              <button
                onClick={handleNewSearch}
                className="text-blue-600 hover:text-blue-800 underline text-sm"
              >
                Start a new search instead
              </button>
            </div>
          </motion.div>
        )}

        {/* Flight Results - One Way */}
        {!isLoading && !error && searchData.tripType === 'oneWay' && oneWayFlights && oneWayFlights.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            {/* Grid Layout with Sidebar */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Flight List - Left Side */}
              <div className="lg:col-span-2">
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    Available Flights
                  </h2>
                  <p className="text-gray-600">
                    Found {oneWayFlights.length} flights for your search
                  </p>
                </div>

                <div className="space-y-4">
                  {oneWayFlights.map((flight, index) => {
                    if (!flight || !flight.id) {
                      console.warn('⚠️ Invalid flight data:', flight);
                      return null;
                    }

                    const isSelected = selectedOutboundFlight?.id === flight.id;

                    return (
                      <motion.div
                        key={flight.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className={isSelected ? 'ring-2 ring-blue-500 rounded-lg' : ''}
                      >
                        <FlightCard
                          flight={flight}
                          onSelect={() => handleOneWayFlightSelect(flight)}
                          isSelected={isSelected}
                        />
                      </motion.div>
                    );
                  })}
                </div>
              </div>

              {/* Sidebar - Right Side */}
              <div className="lg:col-span-1">
                <div className="sticky top-8">
                  {selectedOutboundFlight && (
                    <FlightSummaryWithPassengerForm
                      tripType="oneWay"
                      selectedOutboundFlight={selectedOutboundFlight}
                      selectedReturnFlight={null}
                      searchData={searchData}
                      onEditOutbound={handleEditOutbound}
                      onEditReturn={null}
                      passengers={passengers}
                      email={email}
                      onPassengerDetailsSubmit={handlePassengerDetailsSubmit}
                      onPassengersChange={handlePassengersChange}
                      onEmailChange={handleEmailChange}
                      isPassengerDetailsLoading={isPassengerDetailsLoading}
                      enableInlinePayment={false}
                    />
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Selection Error Message */}
        {selectionError && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"
          >
            <div className="flex items-center">
              <div className="text-red-600 mr-3">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <p className="text-red-800 font-medium">{selectionError}</p>
            </div>
          </motion.div>
        )}

        {/* Flight Results - Return Trip */}
        {!isLoading && !error && searchData.tripType === 'return' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            {/* Grid Layout with Sidebar */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Flight List - Left Side */}
              <div className="lg:col-span-2">
                {/* Outbound Flights Section */}
                {outboundFlights && outboundFlights.length > 0 && (
                  <div className="mb-8" data-section="outbound">
                    <div className="mb-6">
                      <h2 className="text-2xl font-bold text-gray-900 mb-2">
                        Select Departure Flight
                      </h2>
                      <p className="text-gray-600">
                        {searchData.origin} → {searchData.destination} on {new Date(searchData.date).toLocaleDateString()}
                      </p>
                    </div>

                    <div className="space-y-4">
                      {outboundFlights.map((flight, index) => {
                        if (!flight || !flight.id) {
                          console.warn('⚠️ Invalid outbound flight data:', flight);
                          return null;
                        }

                        const isSelected = selectedOutboundFlight?.id === flight.id;

                        return (
                          <motion.div
                            key={flight.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className={isSelected ? 'ring-2 ring-blue-500 rounded-lg' : ''}
                          >
                            <FlightCard
                              flight={flight}
                              onSelect={() => handleOutboundFlightSelect(flight)}
                              isSelected={isSelected}
                            />
                          </motion.div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Return Flights Section */}
                {returnFlights && returnFlights.length > 0 && (
                  <div className="mb-8" data-section="return">
                    <div className="mb-6">
                      <h2 className="text-2xl font-bold text-gray-900 mb-2">
                        Select Return Flight
                      </h2>
                      <p className="text-gray-600">
                        {searchData.destination} → {searchData.origin} on {new Date(searchData.returnDate).toLocaleDateString()}
                      </p>
                    </div>

                    <div className="space-y-4">
                      {returnFlights.map((flight, index) => {
                        if (!flight || !flight.id) {
                          console.warn('⚠️ Invalid return flight data:', flight);
                          return null;
                        }

                        const isSelected = selectedReturnFlight?.id === flight.id;

                        return (
                          <motion.div
                            key={flight.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className={isSelected ? 'ring-2 ring-blue-500 rounded-lg' : ''}
                          >
                            <FlightCard
                              flight={flight}
                              onSelect={() => handleReturnFlightSelect(flight)}
                              isSelected={isSelected}
                            />
                          </motion.div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>

              {/* Sidebar - Right Side */}
              <div className="lg:col-span-1">
                <div className="sticky top-8">
                  {(selectedOutboundFlight || selectedReturnFlight) && (
                    <FlightSummaryWithPassengerForm
                      tripType="return"
                      selectedOutboundFlight={selectedOutboundFlight}
                      selectedReturnFlight={selectedReturnFlight}
                      searchData={searchData}
                      onEditOutbound={handleEditOutbound}
                      onEditReturn={handleEditReturn}
                      passengers={passengers}
                      email={email}
                      onPassengerDetailsSubmit={handlePassengerDetailsSubmit}
                      onPassengersChange={handlePassengersChange}
                      onEmailChange={handleEmailChange}
                      isPassengerDetailsLoading={isPassengerDetailsLoading}
                      enableInlinePayment={false}
                    />
                  )}
                </div>
              </div>
            </div>


            {/* Mobile Floating Sidebar for Return Flights */}
            {(selectedOutboundFlight || selectedReturnFlight) && (
              <motion.div
                initial={{ y: 100, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="lg:hidden fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg max-h-[80vh] overflow-y-auto"
              >
                <div className="p-4">
                  <FlightSummaryWithPassengerForm
                    tripType="return"
                    selectedOutboundFlight={selectedOutboundFlight}
                    selectedReturnFlight={selectedReturnFlight}
                    searchData={searchData}
                    onEditOutbound={handleEditOutbound}
                    onEditReturn={handleEditReturn}
                    passengers={passengers}
                    email={email}
                    onPassengerDetailsSubmit={handlePassengerDetailsSubmit}
                    onPassengersChange={handlePassengersChange}
                    onEmailChange={handleEmailChange}
                    isPassengerDetailsLoading={isPassengerDetailsLoading}
                    enableInlinePayment={false}
                    className="shadow-none border-none"
                  />
                </div>
              </motion.div>
            )}
          </motion.div>
        )}

        {/* Mobile Floating Sidebar for One Way Flights */}
        {!isLoading && !error && searchData.tripType === 'oneWay' && selectedOutboundFlight && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="lg:hidden fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg max-h-[80vh] overflow-y-auto"
          >
            <div className="p-4">
              <FlightSummaryWithPassengerForm
                tripType="oneWay"
                selectedOutboundFlight={selectedOutboundFlight}
                selectedReturnFlight={null}
                searchData={searchData}
                onEditOutbound={handleEditOutbound}
                onEditReturn={null}
                passengers={passengers}
                email={email}
                onPassengerDetailsSubmit={handlePassengerDetailsSubmit}
                onPassengersChange={handlePassengersChange}
                onEmailChange={handleEmailChange}
                isPassengerDetailsLoading={isPassengerDetailsLoading}
                enableInlinePayment={false}
                className="shadow-none border-none"
              />
            </div>
          </motion.div>
        )}

        {/* No Results */}
        {!isLoading && !error && (
          (searchData.tripType === 'oneWay' && (!oneWayFlights || oneWayFlights.length === 0)) ||
          (searchData.tripType === 'return' && ((!outboundFlights || outboundFlights.length === 0) || (!returnFlights || returnFlights.length === 0)))
        ) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-20"
          >
            <div className="text-6xl mb-4">✈️</div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-2">
              No flights found
            </h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              We couldn't find any flights for your search. This might be due to limited availability or our flight search service being temporarily busy.
            </p>

            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  onClick={searchFlights}
                  className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Search Again
                </button>
                <button
                  onClick={handleNewSearch}
                  className="bg-gray-600 text-white px-8 py-3 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  New Search
                </button>
              </div>

              <div className="text-sm text-gray-500 mt-6">
                <p className="mb-2">Try these suggestions:</p>
                <ul className="text-left inline-block space-y-1">
                  <li>• Check if your departure date is correct</li>
                  <li>• Try nearby airports (e.g., LGW instead of LHR)</li>
                  <li>• Select a different date (weekdays often have more options)</li>
                  <li>• Make sure airport codes are valid (3 letters)</li>
                </ul>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default SearchResultsPage;
