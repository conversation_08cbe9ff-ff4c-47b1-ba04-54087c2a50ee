import React from 'react';
import { useNavigate } from 'react-router-dom';

const CheckoutTestReal = () => {
  const navigate = useNavigate();

  const testRealDataNavigation = () => {
    // Simulate real flight and passenger data
    const testData = {
      tripType: 'oneWay',
      passengers: [
        {
          firstName: 'John',
          lastName: 'Smith',
          dateOfBirth: '1990-05-15'
        }
      ],
      email: '<EMAIL>',
      selectedFlight: {
        id: 'real-flight-123',
        airline: {
          name: 'British Airways',
          code: 'BA',
          logo: 'https://www.gstatic.com/flights/airline_logos/70px/BA.png'
        },
        flight: {
          number: 'BA 117',
          departure: {
            airport: 'LHR',
            iataCode: 'LHR',
            city: 'London Heathrow',
            time: '2025-07-15 10:30'
          },
          arrival: {
            airport: 'JFK',
            iataCode: 'JFK',
            city: 'New York JFK',
            time: '2025-07-15 18:15'
          },
          duration: '7h 45m'
        },
        price: {
          total: 4.99,
          displayPrice: 4.99,
          originalPrice: 850
        }
      },
      totalPrice: 4.99
    };

    console.log('🧪 Navigating to checkout with real test data:', testData);
    
    // Navigate to checkout with state
    navigate('/checkout', {
      state: testData
    });
  };

  const testReturnTripNavigation = () => {
    // Simulate return trip data
    const testData = {
      tripType: 'return',
      passengers: [
        {
          firstName: 'Jane',
          lastName: 'Doe',
          dateOfBirth: '1985-12-20'
        }
      ],
      email: '<EMAIL>',
      selectedOutboundFlight: {
        id: 'outbound-flight-456',
        airline: {
          name: 'British Airways',
          code: 'BA',
          logo: 'https://www.gstatic.com/flights/airline_logos/70px/BA.png'
        },
        flight: {
          number: 'BA 177',
          departure: {
            airport: 'LHR',
            iataCode: 'LHR',
            city: 'London Heathrow',
            time: '2025-07-20 10:15'
          },
          arrival: {
            airport: 'JFK',
            iataCode: 'JFK',
            city: 'New York JFK',
            time: '2025-07-20 13:30'
          },
          duration: '8h 15m'
        },
        price: {
          total: 4.99,
          displayPrice: 4.99,
          originalPrice: 650
        }
      },
      selectedReturnFlight: {
        id: 'return-flight-789',
        airline: {
          name: 'British Airways',
          code: 'BA',
          logo: 'https://www.gstatic.com/flights/airline_logos/70px/BA.png'
        },
        flight: {
          number: 'BA 178',
          departure: {
            airport: 'JFK',
            iataCode: 'JFK',
            city: 'New York JFK',
            time: '2025-07-27 16:45'
          },
          arrival: {
            airport: 'LHR',
            iataCode: 'LHR',
            city: 'London Heathrow',
            time: '2025-07-28 05:20'
          },
          duration: '7h 35m'
        },
        price: {
          total: 4.99,
          displayPrice: 4.99,
          originalPrice: 680
        }
      },
      totalPrice: 9.98
    };

    console.log('🧪 Navigating to checkout with return trip data:', testData);
    
    // Navigate to checkout with state
    navigate('/checkout', {
      state: testData
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🧪 Checkout Real Data Test
          </h1>
          <p className="text-gray-600">
            Test the checkout page with real flight and passenger data
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="space-y-6">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-semibold text-green-800 mb-2">✅ What this tests:</h3>
              <ul className="text-sm text-green-700 space-y-1">
                <li>✅ React Router location.state data passing</li>
                <li>✅ Real flight information display</li>
                <li>✅ Real passenger data display</li>
                <li>✅ Proper email handling</li>
                <li>✅ One-way vs Return trip handling</li>
                <li>✅ SessionStorage fallback for page refresh</li>
              </ul>
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              <button
                onClick={testRealDataNavigation}
                className="bg-blue-600 text-white px-6 py-4 rounded-lg hover:bg-blue-700 font-semibold text-lg transition-colors"
              >
                🛫 Test One-Way Flight
                <div className="text-sm font-normal mt-1 opacity-90">
                  Emirates EK 205 • Dubai → London
                </div>
              </button>

              <button
                onClick={testReturnTripNavigation}
                className="bg-purple-600 text-white px-6 py-4 rounded-lg hover:bg-purple-700 font-semibold text-lg transition-colors"
              >
                🔄 Test Return Trip
                <div className="text-sm font-normal mt-1 opacity-90">
                  BA 177/178 • London ⇄ New York
                </div>
              </button>
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-semibold text-blue-800 mb-2">🔧 Additional Tests:</h3>
              <div className="grid md:grid-cols-3 gap-3">
                <button
                  onClick={() => navigate('/checkout')}
                  className="bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 text-sm transition-colors"
                >
                  Direct Checkout Visit
                </button>
                <button
                  onClick={() => {
                    // Clear sessionStorage and visit checkout
                    sessionStorage.removeItem('checkoutData');
                    navigate('/checkout');
                  }}
                  className="bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 text-sm transition-colors"
                >
                  Clean Checkout Visit
                </button>
                <button
                  onClick={() => navigate('/')}
                  className="bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 text-sm transition-colors"
                >
                  Back to Home
                </button>
              </div>
            </div>

            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-semibold text-yellow-800 mb-2">📋 Expected Results:</h3>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Checkout page should show "✅ Complete Your Booking" header</li>
                <li>• Real airline names, flight numbers, and routes should display</li>
                <li>• Real passenger names and email should appear</li>
                <li>• Green "✅ Using your real flight and passenger data" badge should show</li>
                <li>• Payment button should be green and say "Pay $4.99" (no "Demo")</li>
                <li>• Page refresh should maintain data via sessionStorage</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutTestReal;
