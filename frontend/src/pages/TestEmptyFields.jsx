import React, { useState, useEffect } from 'react';
import PassengerDetailsForm from '../components/PassengerDetailsForm';

const TestEmptyFields = () => {
  const [testResults, setTestResults] = useState([]);

  const handleSubmit = (formData) => {
    console.log('Form submitted:', formData);
    
    // Test the submitted data
    const results = [];
    
    // Check email
    if (formData.email === '') {
      results.push('✅ Email field was empty (correct)');
    } else {
      results.push(`❌ Email field contained: "${formData.email}" (should be empty)`);
    }
    
    // Check passengers
    formData.passengers.forEach((passenger, index) => {
      if (passenger.firstName === '') {
        results.push(`✅ Passenger ${index + 1} first name was empty (correct)`);
      } else {
        results.push(`❌ Passenger ${index + 1} first name contained: "${passenger.firstName}" (should be empty)`);
      }
      
      if (passenger.lastName === '') {
        results.push(`✅ Passenger ${index + 1} last name was empty (correct)`);
      } else {
        results.push(`❌ Passenger ${index + 1} last name contained: "${passenger.lastName}" (should be empty)`);
      }
    });
    
    setTestResults(results);
  };

  // Clear any test data on component mount
  useEffect(() => {
    // Clear localStorage test data
    const testEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
    const currentEmail = localStorage.getItem('bookingEmail');
    if (currentEmail && testEmails.includes(currentEmail)) {
      localStorage.removeItem('bookingEmail');
      console.log('Cleared test email from localStorage');
    }

    const passengers = localStorage.getItem('bookingPassengers');
    if (passengers) {
      try {
        const parsedPassengers = JSON.parse(passengers);
        const hasTestData = parsedPassengers.some(p => 
          p.firstName === 'John' || 
          p.lastName === 'Doe' ||
          p.firstName === 'Jane' ||
          p.lastName === 'Smith' ||
          p.firstName === 'Test' ||
          p.lastName === 'User'
        );
        if (hasTestData) {
          localStorage.removeItem('bookingPassengers');
          console.log('Cleared test passenger data from localStorage');
        }
      } catch (e) {
        console.error('Error parsing passengers:', e);
      }
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold mb-6 text-center text-blue-600">
            🧪 Test Empty Passenger Fields
          </h1>
          
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="font-semibold text-blue-800 mb-3">Test Instructions:</h2>
            <ul className="text-sm text-blue-700 space-y-2">
              <li>• <strong>Email field</strong> should be completely empty (no placeholder text visible)</li>
              <li>• <strong>First Name field</strong> should be completely empty</li>
              <li>• <strong>Last Name field</strong> should be completely empty</li>
              <li>• No pre-filled data like "<EMAIL>", "John", "Doe" should appear</li>
              <li>• Fill in the form with test data and click submit to verify initial state</li>
            </ul>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Form Section */}
            <div>
              <h3 className="text-xl font-semibold mb-4">Passenger Form</h3>
              <PassengerDetailsForm
                initialPassengers={[{ id: 1, firstName: '', lastName: '' }]}
                initialEmail=""
                onSubmit={handleSubmit}
                submitButtonText="Test Submit"
              />
            </div>

            {/* Results Section */}
            <div>
              <h3 className="text-xl font-semibold mb-4">Test Results</h3>
              {testResults.length === 0 ? (
                <div className="p-4 bg-gray-100 rounded-lg">
                  <p className="text-gray-600">Submit the form to see test results...</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {testResults.map((result, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg ${
                        result.startsWith('✅') 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {result}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">Debugging Tools:</h3>
            <div className="space-x-4">
              <button
                onClick={() => {
                  console.log('Current localStorage data:');
                  console.log('bookingEmail:', localStorage.getItem('bookingEmail'));
                  console.log('bookingPassengers:', localStorage.getItem('bookingPassengers'));
                }}
                className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
              >
                Log localStorage Data
              </button>
              <button
                onClick={() => {
                  localStorage.removeItem('bookingEmail');
                  localStorage.removeItem('bookingPassengers');
                  window.location.reload();
                }}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Clear Data & Refresh
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestEmptyFields;
