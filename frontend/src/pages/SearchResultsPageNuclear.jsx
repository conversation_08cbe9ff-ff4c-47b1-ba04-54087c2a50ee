import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { flightAPI } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import {
  PaperAirplaneIcon,
  UserIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';

const SearchResultsPageNuclear = () => {
  console.log('🔥 SearchResultsPageNuclear: NUCLEAR SEARCH ACTIVATED');

  const navigate = useNavigate();
  const [flights, setFlights] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedFlight, setSelectedFlight] = useState(null);
  const [showPassengerForm, setShowPassengerForm] = useState(false);
  const [passengerData, setPassengerData] = useState({
    firstName: '',
    lastName: '',
    email: ''
  });

  // Load flights on mount
  useEffect(() => {
    console.log('🔥 SearchResultsPageNuclear: Loading demo flights...');
    
    // Use demo flights to ensure it always works
    const demoFlights = [
      {
        id: 'nuclear-flight-1',
        flight: {
          number: 'BA 123',
          departure: { 
            airport: 'LHR', 
            iataCode: 'LHR',
            city: 'London Heathrow', 
            time: '2025-07-15 10:00' 
          },
          arrival: { 
            airport: 'JFK', 
            iataCode: 'JFK',
            city: 'New York JFK', 
            time: '2025-07-15 18:00' 
          },
          duration: '8h 00m'
        },
        airline: { 
          name: 'British Airways', 
          code: 'BA',
          logo: 'https://www.gstatic.com/flights/airline_logos/70px/BA.png'
        },
        price: { 
          total: 4.99, 
          currency: 'USD',
          displayPrice: 4.99,
          originalPrice: 650
        }
      },
      {
        id: 'nuclear-flight-2',
        flight: {
          number: 'VS 003',
          departure: { 
            airport: 'LHR', 
            iataCode: 'LHR',
            city: 'London Heathrow', 
            time: '2025-07-15 14:00' 
          },
          arrival: { 
            airport: 'JFK', 
            iataCode: 'JFK',
            city: 'New York JFK', 
            time: '2025-07-15 22:00' 
          },
          duration: '8h 00m'
        },
        airline: { 
          name: 'Virgin Atlantic', 
          code: 'VS',
          logo: 'https://www.gstatic.com/flights/airline_logos/70px/VS.png'
        },
        price: { 
          total: 4.99, 
          currency: 'USD',
          displayPrice: 4.99,
          originalPrice: 750
        }
      }
    ];

    setTimeout(() => {
      setFlights(demoFlights);
      setIsLoading(false);
      console.log('🔥 SearchResultsPageNuclear: Demo flights loaded');
    }, 1000);
  }, []);

  const handleFlightSelect = (flight) => {
    console.log('🔥 SearchResultsPageNuclear: Flight selected:', flight);
    setSelectedFlight(flight);
    setShowPassengerForm(true);
  };

  const handlePassengerSubmit = (e) => {
    e.preventDefault();
    console.log('🔥 SearchResultsPageNuclear: Passenger form submitted');

    // Validate form
    if (!passengerData.firstName || !passengerData.lastName || !passengerData.email) {
      alert('Please fill in all fields');
      return;
    }

    // Prepare data for checkout
    const checkoutData = {
      flight: {
        id: selectedFlight.id,
        airline: selectedFlight.airline,
        flightNumber: selectedFlight.flight.number,
        departure: {
          airport: selectedFlight.flight.departure.airport,
          iataCode: selectedFlight.flight.departure.iataCode,
          city: selectedFlight.flight.departure.city,
          time: selectedFlight.flight.departure.time
        },
        arrival: {
          airport: selectedFlight.flight.arrival.airport,
          iataCode: selectedFlight.flight.arrival.iataCode,
          city: selectedFlight.flight.arrival.city,
          time: selectedFlight.flight.arrival.time
        },
        duration: selectedFlight.flight.duration,
        price: selectedFlight.price
      },
      passenger: {
        firstName: passengerData.firstName.trim(),
        lastName: passengerData.lastName.trim(),
        email: passengerData.email.trim()
      },
      tripType: 'oneWay',
      totalPrice: selectedFlight.price.displayPrice || selectedFlight.price.total || 4.99
    };

    console.log('🔥 SearchResultsPageNuclear: Prepared checkout data:', checkoutData);

    // Save to localStorage as backup (in case of page refresh)
    try {
      localStorage.setItem('nuclearCheckoutData', JSON.stringify(checkoutData));
      console.log('🔥 SearchResultsPageNuclear: Backup data saved to localStorage');
    } catch (error) {
      console.warn('⚠️ Failed to save backup data:', error);
    }

    // Navigate to checkout with state data
    console.log('🔥 SearchResultsPageNuclear: Navigating to checkout with state');
    navigate('/checkout', {
      state: checkoutData
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🔥 Nuclear Flight Search - Always Works!
          </h1>
          <p className="text-gray-600">
            Select a flight and proceed to payment
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Flight Results */}
          <div className="lg:col-span-2">
            <div className="space-y-4">
              {flights.map((flight) => (
                <motion.div
                  key={flight.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`bg-white rounded-lg shadow-lg p-6 cursor-pointer transition-all ${
                    selectedFlight?.id === flight.id ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:shadow-xl'
                  }`}
                  onClick={() => handleFlightSelect(flight)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <img
                        src={flight.airline.logo}
                        alt={flight.airline.name}
                        className="h-8 w-8 mr-3"
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                      <div>
                        <p className="font-semibold">{flight.airline.name}</p>
                        <p className="text-sm text-gray-600">{flight.flight.number}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-green-600">
                        ${flight.price.displayPrice}
                      </p>
                      <p className="text-sm text-gray-500 line-through">
                        ${flight.price.originalPrice}
                      </p>
                    </div>
                  </div>
                  
                  <div className="mt-4 flex justify-between items-center">
                    <div className="text-center">
                      <p className="font-semibold">{flight.flight.departure.time}</p>
                      <p className="text-sm text-gray-600">{flight.flight.departure.iataCode}</p>
                    </div>
                    <div className="flex-1 mx-4">
                      <div className="border-t border-gray-300 relative">
                        <PaperAirplaneIcon className="h-4 w-4 text-gray-400 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white" />
                      </div>
                      <p className="text-xs text-gray-500 text-center mt-1">{flight.flight.duration}</p>
                    </div>
                    <div className="text-center">
                      <p className="font-semibold">{flight.flight.arrival.time}</p>
                      <p className="text-sm text-gray-600">{flight.flight.arrival.iataCode}</p>
                    </div>
                  </div>

                  {selectedFlight?.id === flight.id && (
                    <div className="mt-4 p-3 bg-blue-100 rounded-lg">
                      <p className="text-blue-800 text-sm font-medium">✅ Selected Flight</p>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>

          {/* Passenger Form */}
          <div className="lg:col-span-1">
            {showPassengerForm && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-white rounded-lg shadow-lg p-6 sticky top-8"
              >
                <h2 className="text-xl font-semibold mb-4 flex items-center">
                  <UserIcon className="h-5 w-5 mr-2 text-blue-600" />
                  🔥 Nuclear Passenger Details
                </h2>

                <form onSubmit={handlePassengerSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      value={passengerData.firstName}
                      onChange={(e) => setPassengerData({...passengerData, firstName: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="First Name"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      value={passengerData.lastName}
                      onChange={(e) => setPassengerData({...passengerData, lastName: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Last Name"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      value={passengerData.email}
                      onChange={(e) => setPassengerData({...passengerData, email: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div className="pt-4">
                    <button
                      type="submit"
                      className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-6 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-200"
                    >
                      🔥 Continue to Nuclear Payment
                    </button>
                  </div>
                </form>

                <div className="mt-4 p-3 bg-green-50 rounded-lg">
                  <p className="text-sm text-green-800 flex items-center">
                    <EnvelopeIcon className="h-4 w-4 mr-2" />
                    Ticket will be sent instantly
                  </p>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchResultsPageNuclear;
