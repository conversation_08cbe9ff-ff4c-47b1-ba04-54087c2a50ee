import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import UnifiedCTA from '../components/UnifiedCTA';

const TermsOfServicePage = () => {
  // Set page title and meta description
  useEffect(() => {
    document.title = 'Terms of Service | VerifiedOnward';

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = 'Terms of Service for VerifiedOnward. Understand the terms and conditions for using our embassy-approved flight reservation service for visa applications.';

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservation in 60 Seconds';
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-brand-50/30">
      {/* Premium Header Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 via-blue-500/5 to-accent-500/5"></div>

        <div className="container-modern relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center bg-brand-100 text-brand-700 px-6 py-3 rounded-full text-sm font-semibold mb-8">
              <span className="w-2 h-2 bg-brand-500 rounded-full mr-3 animate-pulse"></span>
              TERMS OF SERVICE
            </div>

            <h1 className="text-5xl md:text-6xl font-black text-brand-800 mb-8 leading-tight">
              Terms of
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent"> Service</span>
            </h1>

            <p className="text-xl md:text-2xl text-brand-700 leading-relaxed font-medium mb-12">
              By using VerifiedOnward.com, you agree to the following terms and conditions.
              <br className="hidden md:block" />
              <strong className="text-brand-800">Please read these terms carefully before using our service.</strong>
            </p>
          </motion.div>
        </div>
      </section>

      {/* Premium Content Section */}
      <section className="py-16">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="premium-card max-w-5xl mx-auto p-12"
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-brand-800 mb-4">Service Agreement</h2>
              <div className="w-24 h-1 bg-gradient-to-r from-brand-500 to-accent-500 mx-auto rounded-full"></div>
            </div>

            <div className="space-y-10">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-gradient-to-r from-brand-50 to-brand-100 border-l-4 border-brand-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-brand-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">1</span>
                  Purpose of Service
                </h3>
                <p className="text-neutral-700 text-lg leading-relaxed">
                  VerifiedOnward provides professionally formatted flight reservation documents for visa application purposes only. These are not valid tickets for travel or check-in.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-gradient-to-r from-blue-50 to-blue-100 border-l-4 border-blue-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">2</span>
                  Accuracy of Data
                </h3>
                <p className="text-neutral-700 text-lg leading-relaxed">
                  The flight data we use is pulled from real airline schedules. We do not guarantee availability or pricing of actual flights.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-gradient-to-r from-green-50 to-green-100 border-l-4 border-green-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">3</span>
                  Pricing
                </h3>
                <p className="text-neutral-700 text-lg leading-relaxed">
                  Each reservation document is sold at a fixed rate of $4.99. The price covers instant email delivery, formatting, and support.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-gradient-to-r from-red-50 to-red-100 border-l-4 border-red-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">4</span>
                  Sales Policy
                </h3>
                <div className="text-neutral-700 text-lg leading-relaxed space-y-4">
                  <p>
                    All sales are final and non-refundable. Due to the low price and instant delivery nature of the service, we do not offer refunds under any circumstances. This includes, but is not limited to:
                  </p>
                  <ul className="list-disc list-inside space-y-2 ml-6 text-neutral-600">
                    <li>Mistyped details</li>
                    <li>Change of mind</li>
                    <li>Rejected visa applications</li>
                    <li>Duplicate orders</li>
                  </ul>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-gradient-to-r from-yellow-50 to-yellow-100 border-l-4 border-yellow-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-yellow-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">5</span>
                  Liability
                </h3>
                <p className="text-neutral-700 text-lg leading-relaxed">
                  We are not liable for decisions made by embassies, consulates, or visa officers. We only provide supporting travel documentation in a format typically accepted for visa processing.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="bg-gradient-to-r from-brand-50 to-brand-100 border-l-4 border-brand-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-brand-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">6</span>
                  Contact Information
                </h3>
                <p className="text-neutral-700 text-lg leading-relaxed">
                  For inquiries, contact us at:{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-brand-600 hover:text-brand-800 font-semibold underline decoration-2 underline-offset-2 hover:decoration-brand-800 transition-all duration-200"
                  >
                    <EMAIL>
                  </a>
                </p>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Premium CTA Section */}
      <section className="py-16">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <UnifiedCTA />
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default TermsOfServicePage;
