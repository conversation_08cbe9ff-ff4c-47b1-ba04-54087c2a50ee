import React, { useState } from 'react';
import { flightAPI, healthCheck } from '../services/api';

const APITestPage = () => {
  const [healthStatus, setHealthStatus] = useState(null);
  const [healthError, setHealthError] = useState(null);
  const [searchResult, setSearchResult] = useState(null);
  const [searchError, setSearchError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const testHealthCheck = async () => {
    setIsLoading(true);
    setHealthStatus(null);
    setHealthError(null);
    
    try {
      console.log('🔍 Testing health check...');
      const result = await healthCheck();
      console.log('✅ Health check result:', result);
      setHealthStatus(result);
    } catch (error) {
      console.error('❌ Health check error:', error);
      setHealthError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testFlightSearch = async () => {
    setIsLoading(true);
    setSearchResult(null);
    setSearchError(null);
    
    try {
      console.log('🔍 Testing flight search...');
      const searchParams = {
        origin: 'LHR',
        destination: 'JFK',
        date: '2025-07-15',
        tripType: 'oneWay'
      };
      
      console.log('Search params:', searchParams);
      const result = await flightAPI.searchFlights(searchParams);
      console.log('✅ Flight search result:', result);
      setSearchResult(result);
    } catch (error) {
      console.error('❌ Flight search error:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        code: error.code
      });
      setSearchError({
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        code: error.code
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          API Connection Test
        </h1>

        <div className="space-y-6">
          {/* Health Check Test */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-blue-600 mb-4">
              Health Check Test
            </h2>
            
            <button
              onClick={testHealthCheck}
              disabled={isLoading}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50 mb-4"
            >
              {isLoading ? 'Testing...' : 'Test Health Check'}
            </button>

            {healthStatus && (
              <div className="bg-green-50 border border-green-200 rounded p-4 mb-4">
                <h3 className="font-semibold text-green-800">✅ Health Check Success</h3>
                <pre className="text-sm text-green-700 mt-2">
                  {JSON.stringify(healthStatus, null, 2)}
                </pre>
              </div>
            )}

            {healthError && (
              <div className="bg-red-50 border border-red-200 rounded p-4 mb-4">
                <h3 className="font-semibold text-red-800">❌ Health Check Error</h3>
                <p className="text-red-700 mt-2">{healthError}</p>
              </div>
            )}
          </div>

          {/* Flight Search Test */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-green-600 mb-4">
              Flight Search Test
            </h2>
            
            <button
              onClick={testFlightSearch}
              disabled={isLoading}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50 mb-4"
            >
              {isLoading ? 'Testing...' : 'Test Flight Search (LHR → JFK)'}
            </button>

            {searchResult && (
              <div className="bg-green-50 border border-green-200 rounded p-4 mb-4">
                <h3 className="font-semibold text-green-800">✅ Flight Search Success</h3>
                <p className="text-green-700 mb-2">
                  Found {searchResult.data?.flights?.length || 0} flights
                </p>
                <details className="text-sm">
                  <summary className="cursor-pointer text-green-700 hover:text-green-800">
                    View Full Response
                  </summary>
                  <pre className="text-green-700 mt-2 max-h-96 overflow-auto">
                    {JSON.stringify(searchResult, null, 2)}
                  </pre>
                </details>
              </div>
            )}

            {searchError && (
              <div className="bg-red-50 border border-red-200 rounded p-4 mb-4">
                <h3 className="font-semibold text-red-800">❌ Flight Search Error</h3>
                <div className="text-red-700 mt-2">
                  <p><strong>Message:</strong> {searchError.message}</p>
                  {searchError.status && <p><strong>Status:</strong> {searchError.status}</p>}
                  {searchError.code && <p><strong>Code:</strong> {searchError.code}</p>}
                  {searchError.data && (
                    <details className="mt-2">
                      <summary className="cursor-pointer">Error Data</summary>
                      <pre className="text-sm mt-1">
                        {JSON.stringify(searchError.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* API Configuration Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-purple-600 mb-4">
              API Configuration
            </h2>
            <div className="text-sm text-gray-700 space-y-2">
              <p><strong>Environment:</strong> {import.meta.env.DEV ? 'Development' : 'Production'}</p>
              <p><strong>Base URL:</strong> {import.meta.env.DEV ? '/api (proxy)' : (import.meta.env.VITE_API_BASE_URL || 'http://localhost:5001/api')}</p>
              <p><strong>Current URL:</strong> {window.location.href}</p>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex gap-4">
            <button
              onClick={() => window.history.back()}
              className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
            >
              ← Back
            </button>
            <button
              onClick={() => window.location.href = '/'}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Go to Home
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default APITestPage;
