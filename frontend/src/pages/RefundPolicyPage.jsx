import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import UnifiedCTA from '../components/UnifiedCTA';

const RefundPolicyPage = () => {
  // Set page title and meta description
  useEffect(() => {
    document.title = 'Refund Policy | VerifiedOnward';

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = 'Refund policy for VerifiedOnward.com - All sales are final due to the instant digital nature of our flight reservations.';

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservations';
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-brand-50/30">
      {/* Premium Header Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 via-blue-500/5 to-accent-500/5"></div>

        <div className="container-modern relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center bg-brand-100 text-brand-700 px-6 py-3 rounded-full text-sm font-semibold mb-8">
              <span className="w-2 h-2 bg-brand-500 rounded-full mr-3 animate-pulse"></span>
              REFUND POLICY
            </div>

            <h1 className="text-5xl md:text-6xl font-black text-brand-800 mb-8 leading-tight">
              Sales
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent"> Policy</span>
            </h1>

            <p className="text-xl md:text-2xl text-brand-700 leading-relaxed font-medium mb-12">
              Clear and transparent information about our sales policy for VerifiedOnward services.
              <br className="hidden md:block" />
              <strong className="text-brand-800">All sales are final due to the instant digital nature of our service.</strong>
            </p>
          </motion.div>
        </div>
      </section>

      {/* Premium Content Section */}
      <section className="py-16">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="premium-card max-w-4xl mx-auto p-12"
          >
            <div className="space-y-12">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-brand-800 mb-4">Policy Overview</h2>
                <div className="w-24 h-1 bg-gradient-to-r from-brand-500 to-accent-500 mx-auto rounded-full"></div>
              </div>

              <div className="space-y-10">
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6 }}
                  className="bg-gradient-to-r from-red-50 to-red-100 border-l-4 border-red-500 p-6 rounded-r-xl"
                >
                  <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                    <svg className="w-6 h-6 mr-3 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    Final Sale Policy
                  </h3>
                  <p className="text-neutral-700 text-lg leading-relaxed">
                    <strong>All sales are final and non-refundable.</strong> Due to the nature and affordability of our service, VerifiedOnward.com provides a low-cost, digital product delivered instantly upon payment.
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="bg-gradient-to-r from-blue-50 to-blue-100 border-l-4 border-blue-500 p-6 rounded-r-xl"
                >
                  <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                    <svg className="w-6 h-6 mr-3 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                    </svg>
                    Why No Refunds?
                  </h3>
                  <p className="text-neutral-700 text-lg leading-relaxed">
                    Because each document is generated on-demand with real-time flight data and personal travel information, we are unable to offer refunds under any circumstances. Our system immediately processes your request and delivers your reservation document within minutes.
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="bg-gradient-to-r from-green-50 to-green-100 border-l-4 border-green-500 p-6 rounded-r-xl"
                >
                  <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                    <svg className="w-6 h-6 mr-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Before You Purchase
                  </h3>
                  <p className="text-neutral-700 text-lg leading-relaxed">
                    We encourage users to carefully review all input details before completing their purchase. Please double-check flight details, passenger information, and travel dates as documents cannot be modified once created.
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  className="bg-gradient-to-r from-brand-50 to-brand-100 border-l-4 border-brand-500 p-6 rounded-r-xl"
                >
                  <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                    <svg className="w-6 h-6 mr-3 text-brand-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                    Need Support?
                  </h3>
                  <p className="text-neutral-700 text-lg leading-relaxed">
                    For support or questions, please contact us at{' '}
                    <a
                      href="mailto:<EMAIL>"
                      className="text-brand-600 hover:text-brand-800 font-semibold underline decoration-2 underline-offset-2 hover:decoration-brand-800 transition-all duration-200"
                    >
                      <EMAIL>
                    </a>
                  </p>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Premium CTA Section */}
      <section className="py-16">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <UnifiedCTA />
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default RefundPolicyPage;
