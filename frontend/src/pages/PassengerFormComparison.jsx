import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import PassengerDetailsForm from '../components/PassengerDetailsForm';
import PassengerDetailsSectionWrapper from '../components/PassengerDetailsSectionWrapper';

const PassengerFormComparison = () => {
  const navigate = useNavigate();
  
  // State for old component
  const [oldPassengers, setOldPassengers] = useState([{ id: 1, firstName: '', lastName: '' }]);
  const [oldEmail, setOldEmail] = useState('');
  const [oldLoading, setOldLoading] = useState(false);
  const [oldSubmittedData, setOldSubmittedData] = useState(null);
  
  // State for new component
  const [newLoading, setNewLoading] = useState(false);
  const [newSubmittedData, setNewSubmittedData] = useState(null);

  // Old component handlers
  const handleOldSubmit = (formData) => {
    console.log('🔴 OLD Component submitted:', formData);
    setOldLoading(true);
    
    setTimeout(() => {
      setOldLoading(false);
      setOldSubmittedData(formData);
    }, 1500);
  };

  const handleOldPassengersChange = (updatedPassengers) => {
    setOldPassengers(updatedPassengers);
  };

  const handleOldEmailChange = (updatedEmail) => {
    setOldEmail(updatedEmail);
  };

  // New component handlers
  const handleNewSubmit = (formData) => {
    console.log('🟢 NEW Component submitted:', formData);
    setNewLoading(true);
    
    setTimeout(() => {
      setNewLoading(false);
      setNewSubmittedData(formData);
    }, 1500);
  };

  const resetComparison = () => {
    setOldSubmittedData(null);
    setNewSubmittedData(null);
    setOldPassengers([{ id: 1, firstName: '', lastName: '' }]);
    setOldEmail('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-8 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Passenger Form Comparison
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-6">
            Side-by-side comparison of the old PassengerDetailsForm vs the new PassengerDetailsSection
          </p>
          <button
            onClick={resetComparison}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
          >
            Reset Comparison
          </button>
        </div>

        {/* Comparison Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          {/* Old Component */}
          <div className="space-y-6">
            <div className="bg-red-50 border border-red-200 rounded-xl p-4">
              <h2 className="text-xl font-bold text-red-800 mb-2">
                🔴 OLD: PassengerDetailsForm
              </h2>
              <div className="text-sm text-red-700 space-y-1">
                <p>• Uses controlled props (initialPassengers, initialEmail)</p>
                <p>• Requires parent state management</p>
                <p>• Complex prop drilling</p>
                <p>• Less modern styling</p>
              </div>
            </div>

            {oldSubmittedData && (
              <div className="bg-red-100 border border-red-300 rounded-lg p-4">
                <h3 className="font-semibold text-red-800 mb-2">🔴 OLD Submitted Data:</h3>
                <pre className="text-xs text-red-700 bg-red-50 p-2 rounded overflow-auto">
                  {JSON.stringify(oldSubmittedData, null, 2)}
                </pre>
              </div>
            )}

            <PassengerDetailsForm
              initialPassengers={oldPassengers}
              initialEmail={oldEmail}
              onSubmit={handleOldSubmit}
              onPassengersChange={handleOldPassengersChange}
              onEmailChange={handleOldEmailChange}
              isLoading={oldLoading}
              submitButtonText="Submit Old Form"
            />
          </div>

          {/* New Component */}
          <div className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-xl p-4">
              <h2 className="text-xl font-bold text-green-800 mb-2">
                🟢 NEW: PassengerDetailsSection
              </h2>
              <div className="text-sm text-green-700 space-y-1">
                <p>• Self-contained state management</p>
                <p>• Always empty by default</p>
                <p>• Modern, professional design</p>
                <p>• Smooth animations</p>
                <p>• Better mobile responsiveness</p>
              </div>
            </div>

            {newSubmittedData && (
              <div className="bg-green-100 border border-green-300 rounded-lg p-4">
                <h3 className="font-semibold text-green-800 mb-2">🟢 NEW Submitted Data:</h3>
                <pre className="text-xs text-green-700 bg-green-50 p-2 rounded overflow-auto">
                  {JSON.stringify(newSubmittedData, null, 2)}
                </pre>
              </div>
            )}

            <PassengerDetailsSectionWrapper
              onSubmit={handleNewSubmit}
              isLoading={newLoading}
              submitButtonText="Submit New Form"
            />
          </div>
        </div>

        {/* Feature Comparison Table */}
        <div className="mt-12 bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="p-6 bg-gray-50 border-b">
            <h2 className="text-2xl font-bold text-gray-900">Feature Comparison</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-100">
                <tr>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">Feature</th>
                  <th className="px-6 py-3 text-center text-sm font-medium text-red-700">OLD Component</th>
                  <th className="px-6 py-3 text-center text-sm font-medium text-green-700">NEW Component</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900">Empty by default</td>
                  <td className="px-6 py-4 text-center text-red-600">❌ Depends on props</td>
                  <td className="px-6 py-4 text-center text-green-600">✅ Always empty</td>
                </tr>
                <tr className="bg-gray-50">
                  <td className="px-6 py-4 text-sm text-gray-900">State management</td>
                  <td className="px-6 py-4 text-center text-red-600">❌ External</td>
                  <td className="px-6 py-4 text-center text-green-600">✅ Self-contained</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900">Modern design</td>
                  <td className="px-6 py-4 text-center text-red-600">❌ Basic</td>
                  <td className="px-6 py-4 text-center text-green-600">✅ Professional</td>
                </tr>
                <tr className="bg-gray-50">
                  <td className="px-6 py-4 text-sm text-gray-900">Animations</td>
                  <td className="px-6 py-4 text-center text-red-600">❌ Limited</td>
                  <td className="px-6 py-4 text-center text-green-600">✅ Smooth</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900">Mobile responsive</td>
                  <td className="px-6 py-4 text-center text-yellow-600">⚠️ Basic</td>
                  <td className="px-6 py-4 text-center text-green-600">✅ Optimized</td>
                </tr>
                <tr className="bg-gray-50">
                  <td className="px-6 py-4 text-sm text-gray-900">Validation UX</td>
                  <td className="px-6 py-4 text-center text-yellow-600">⚠️ Basic</td>
                  <td className="px-6 py-4 text-center text-green-600">✅ Enhanced</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900">Code complexity</td>
                  <td className="px-6 py-4 text-center text-red-600">❌ High</td>
                  <td className="px-6 py-4 text-center text-green-600">✅ Clean</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center space-x-4">
          <button
            onClick={() => navigate('/passenger-details-demo')}
            className="text-blue-600 hover:text-blue-800 underline"
          >
            View New Component Demo
          </button>
          <button
            onClick={() => navigate('/')}
            className="text-gray-600 hover:text-gray-800 underline"
          >
            Back to Homepage
          </button>
        </div>
      </div>
    </div>
  );
};

export default PassengerFormComparison;
