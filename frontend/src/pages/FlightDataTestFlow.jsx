import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useBooking } from '../context/BookingContext';

const FlightDataTestFlow = () => {
  const navigate = useNavigate();
  const { setSelectedFlight, setSelectedOutboundFlight, setSelectedReturnFlight, setPassengers, setEmail, setTripType } = useBooking();
  const [testType, setTestType] = useState('oneWay');

  // Sample flight data that matches the real API structure
  const sampleOneWayFlight = {
    id: 'test-flight-ow-001',
    airline: {
      name: 'British Airways',
      code: 'BA',
      logo: 'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png'
    },
    flight: {
      number: 'BA 117'
    },
    flightNumber: 'BA 117',
    departure: {
      airport: 'London Heathrow',
      iataCode: 'LHR',
      time: '2025-07-15 10:30'
    },
    arrival: {
      airport: 'New York JFK',
      iataCode: 'JFK', 
      time: '2025-07-15 15:15'
    },
    duration: '8h 45m',
    price: {
      total: 4.99,
      displayPrice: 4.99,
      originalPrice: 299
    }
  };

  const sampleOutboundFlight = {
    id: 'test-flight-ob-001',
    airline: {
      name: 'Lufthansa',
      code: 'LH',
      logo: 'https://logos-world.net/wp-content/uploads/2020/03/Lufthansa-Logo.png'
    },
    flight: {
      number: 'LH 441'
    },
    flightNumber: 'LH 441',
    departure: {
      airport: 'Frankfurt Airport',
      iataCode: 'FRA',
      time: '2025-07-20 14:20'
    },
    arrival: {
      airport: 'Madrid Barajas',
      iataCode: 'MAD',
      time: '2025-07-20 16:45'
    },
    duration: '2h 25m',
    price: {
      total: 4.99,
      displayPrice: 4.99,
      originalPrice: 189
    }
  };

  const sampleReturnFlight = {
    id: 'test-flight-rt-001',
    airline: {
      name: 'Iberia',
      code: 'IB',
      logo: 'https://logos-world.net/wp-content/uploads/2020/03/Iberia-Logo.png'
    },
    flight: {
      number: 'IB 3218'
    },
    flightNumber: 'IB 3218',
    departure: {
      airport: 'Madrid Barajas',
      iataCode: 'MAD',
      time: '2025-07-27 11:15'
    },
    arrival: {
      airport: 'Frankfurt Airport',
      iataCode: 'FRA',
      time: '2025-07-27 13:50'
    },
    duration: '2h 35m',
    price: {
      total: 4.99,
      displayPrice: 4.99,
      originalPrice: 205
    }
  };

  const samplePassengers = [
    {
      id: 1,
      firstName: 'John',
      lastName: 'Smith'
    },
    {
      id: 2,
      firstName: 'Jane',
      lastName: 'Doe'
    }
  ];

  const testOneWayFlow = () => {
    console.log('🧪 Testing One-Way Flight Flow');
    
    // Set up one-way flight data
    setTripType('oneWay');
    setSelectedFlight(sampleOneWayFlight);
    setSelectedOutboundFlight(sampleOneWayFlight);
    setPassengers(samplePassengers);
    setEmail('<EMAIL>');

    // Navigate to checkout with state data (simulating SearchResultsPage behavior)
    const checkoutData = {
      tripType: 'oneWay',
      passengers: samplePassengers,
      email: '<EMAIL>',
      totalPrice: 4.99,
      flight: sampleOneWayFlight // This is the key that was missing!
    };

    console.log('🧪 Navigating to checkout with data:', checkoutData);
    
    navigate('/checkout', {
      state: checkoutData
    });
  };

  const testReturnFlow = () => {
    console.log('🧪 Testing Return Flight Flow');
    
    // Set up return flight data
    setTripType('return');
    setSelectedOutboundFlight(sampleOutboundFlight);
    setSelectedReturnFlight(sampleReturnFlight);
    setPassengers(samplePassengers);
    setEmail('<EMAIL>');

    // Navigate to checkout with state data (simulating SearchResultsPage behavior)
    const checkoutData = {
      tripType: 'return',
      passengers: samplePassengers,
      email: '<EMAIL>',
      totalPrice: 9.98,
      outboundFlight: sampleOutboundFlight, // These are the keys that were missing!
      returnFlight: sampleReturnFlight
    };

    console.log('🧪 Navigating to checkout with data:', checkoutData);
    
    navigate('/checkout', {
      state: checkoutData
    });
  };

  const testContextOnlyFlow = () => {
    console.log('🧪 Testing Context-Only Flow (no navigation state)');
    
    if (testType === 'oneWay') {
      setTripType('oneWay');
      setSelectedFlight(sampleOneWayFlight);
      setSelectedOutboundFlight(sampleOneWayFlight);
    } else {
      setTripType('return');
      setSelectedOutboundFlight(sampleOutboundFlight);
      setSelectedReturnFlight(sampleReturnFlight);
    }
    
    setPassengers(samplePassengers);
    setEmail('<EMAIL>');

    // Navigate without state data (relying only on context)
    navigate('/checkout');
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Flight Data Test Flow
        </h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Flight Data Flow</h2>
          <p className="text-gray-600 mb-6">
            This page tests different ways flight data is passed to the checkout page.
          </p>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trip Type for Context-Only Test
              </label>
              <select
                value={testType}
                onChange={(e) => setTestType(e.target.value)}
                className="border border-gray-300 rounded px-3 py-2"
              >
                <option value="oneWay">One Way</option>
                <option value="return">Return</option>
              </select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={testOneWayFlow}
                className="bg-blue-600 text-white px-4 py-3 rounded hover:bg-blue-700 font-semibold"
              >
                🧪 Test One-Way Flow
                <div className="text-sm font-normal mt-1">
                  (with navigation state)
                </div>
              </button>

              <button
                onClick={testReturnFlow}
                className="bg-green-600 text-white px-4 py-3 rounded hover:bg-green-700 font-semibold"
              >
                🧪 Test Return Flow
                <div className="text-sm font-normal mt-1">
                  (with navigation state)
                </div>
              </button>

              <button
                onClick={testContextOnlyFlow}
                className="bg-purple-600 text-white px-4 py-3 rounded hover:bg-purple-700 font-semibold"
              >
                🧪 Test Context Only
                <div className="text-sm font-normal mt-1">
                  (no navigation state)
                </div>
              </button>
            </div>

            <div className="border-t pt-4">
              <button
                onClick={() => navigate('/checkout-data-debug')}
                className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 mr-4"
              >
                🔍 Debug Data
              </button>
              
              <button
                onClick={() => navigate('/')}
                className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
              >
                ← Back to Home
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlightDataTestFlow;
