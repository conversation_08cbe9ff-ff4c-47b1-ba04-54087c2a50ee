import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useBooking } from '../context/BookingContext';

const CheckoutTestFlow = () => {
  const navigate = useNavigate();
  const {
    setSelectedFlight,
    setPassengers,
    setEmail,
    setTripType
  } = useBooking();

  useEffect(() => {
    // Simulate booking data
    const testFlight = {
      id: 'test-flight-1',
      airline: {
        name: 'Test Airlines',
        code: 'TA',
        logo: 'https://via.placeholder.com/40x40?text=TA'
      },
      flight: {
        number: 'TA 123',
        departure: {
          airport: 'LHR',
          iataCode: 'LHR',
          city: 'London Heathrow',
          time: '2025-07-15 10:00'
        },
        arrival: {
          airport: 'JFK',
          iataCode: 'JFK',
          city: 'New York JFK',
          time: '2025-07-15 15:30'
        },
        duration: '8h 30m',
        stops: 0
      },
      price: {
        total: 4.99,
        displayPrice: 4.99,
        originalPrice: 299
      }
    };

    const testPassengers = [
      {
        id: 1,
        firstName: '<PERSON>',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01'
      }
    ];

    const testEmail = '<EMAIL>';

    // Set the booking data using individual methods
    setSelectedFlight(testFlight);
    setPassengers(testPassengers);
    setEmail(testEmail);
    setTripType('oneWay');

    console.log('🧪 Test booking data set:', {
      flight: testFlight,
      passengers: testPassengers,
      email: testEmail
    });

    // Navigate to checkout after a short delay
    const timer = setTimeout(() => {
      navigate('/checkout');
    }, 1500);

    return () => clearTimeout(timer);
  }, [navigate, setSelectedFlight, setPassengers, setEmail, setTripType]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-purple-100 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
        <h1 className="text-2xl font-bold text-purple-900 mb-2">
          Setting up test booking...
        </h1>
        <p className="text-purple-700">
          Redirecting to checkout page...
        </p>
      </div>
    </div>
  );
};

export default CheckoutTestFlow;
