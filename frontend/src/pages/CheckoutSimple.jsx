import React from 'react';

const CheckoutSimple = () => {
  console.log('🔍 CheckoutSimple: Component rendering...');
  
  return (
    <div className="min-h-screen bg-blue-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-blue-900 mb-4">
            🛒 Checkout Page
          </h1>
          <p className="text-blue-700 text-lg">
            Complete your dummy flight ticket purchase
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Flight Summary */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold mb-4 text-gray-800">
              ✈️ Flight Summary
            </h2>
            <div className="space-y-4">
              <div className="border rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-semibold">Sample Flight</p>
                    <p className="text-gray-600">ABC 123</p>
                    <p className="text-sm text-gray-500">LHR → JFK</p>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-green-600">$4.99</p>
                    <p className="text-sm text-gray-500">Dummy Ticket</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Payment */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold mb-4 text-gray-800">
              💳 Payment
            </h2>
            
            <div className="space-y-4">
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-2">Total Amount</h3>
                <p className="text-3xl font-bold text-green-600">$4.99</p>
              </div>

              <div className="space-y-3">
                <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-semibold">
                  💳 Pay with Card
                </button>
                <button className="w-full bg-yellow-500 text-white py-3 px-4 rounded-lg hover:bg-yellow-600 transition-colors font-semibold">
                  🅿️ Pay with PayPal
                </button>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center text-green-800 text-sm">
                  <span className="mr-2">🔒</span>
                  <span>Secure SSL encrypted payment</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Debug Info */}
        <div className="mt-8 bg-gray-100 rounded-lg p-4">
          <h3 className="font-semibold mb-2">Debug Information</h3>
          <div className="text-sm text-gray-600 space-y-1">
            <p>✅ React component loaded successfully</p>
            <p>✅ TailwindCSS styles applied</p>
            <p>✅ No JavaScript errors</p>
            <p>✅ URL: {window.location.href}</p>
            <p>✅ Time: {new Date().toISOString()}</p>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-6 text-center space-x-4">
          <button 
            onClick={() => window.history.back()}
            className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
          >
            ← Back
          </button>
          <button 
            onClick={() => window.location.href = '/search'}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Start Over
          </button>
        </div>
      </div>
    </div>
  );
};

export default CheckoutSimple;
