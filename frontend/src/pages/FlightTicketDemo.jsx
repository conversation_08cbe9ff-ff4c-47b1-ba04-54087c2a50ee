import React, { useState } from 'react';
import FlightTicket from '../components/FlightTicket';
import DocumentDownloadButton, { PrintButton } from '../components/PDFDownloadButton';

const FlightTicketDemo = () => {
  const [selectedDemo, setSelectedDemo] = useState('multi-segment');

  // Sample data for multi-segment trip
  const multiSegmentData = {
    tripDates: "18 JUL 2021 › 19 JUL 2021",
    destination: "TRIP TO NEW YORK CITY",
    passengers: [
      { name: "COOPER/JANE" },
      { name: "WILSON/JENNY" }
    ],
    reservationCode: "NHG8IQ",
    airlineReservationCode: "NHG8IQ",
    segments: [
      {
        departureDay: "SUNDAY 18 JUL",
        airline: "CATHAY PACIFIC",
        flightNo: "CX 784",
        duration: "05hr(s) 00min(s)",
        flightClass: "Economy Class (M)",
        status: "Confirmed",
        from: { code: "DPS", city: "Denpasar-Bali, Indonesia", time: "16:05", terminal: "I" },
        to: { code: "HKG", city: "Hong Kong, Hong Kong", time: "21:05", terminal: "1" },
        aircraft: "AIRBUS INDUSTRIE A330-300",
        stops: "0",
        meals: "Not Available",
        distance: "Not Available"
      },
      {
        departureDay: "MONDAY 19 JUL",
        airline: "CATHAY PACIFIC",
        flightNo: "CX 844",
        duration: "15hr(s) 55min(s)",
        flightClass: "Economy Class (M)",
        status: "Confirmed",
        from: { code: "HKG", city: "Hong Kong, Hong Kong", time: "02:05", terminal: "1" },
        to: { code: "JFK", city: "New York, United States Of America", time: "06:00", terminal: "8" },
        aircraft: "BOEING 777-300ER",
        stops: "0",
        meals: "Not Available",
        distance: "Not Available"
      }
    ]
  };

  // Sample data for single segment trip
  const singleSegmentData = {
    tripDates: "25 DEC 2024",
    destination: "CHRISTMAS VACATION TO LONDON",
    passengers: [
      { name: "SMITH/JOHN MR." }
    ],
    reservationCode: "ABC123",
    airlineReservationCode: "ABC123",
    segments: [
      {
        departureDay: "WEDNESDAY 25 DEC",
        airline: "BRITISH AIRWAYS",
        flightNo: "BA 117",
        duration: "08hr(s) 30min(s)",
        flightClass: "Business Class (C)",
        status: "Confirmed",
        from: { code: "JFK", city: "New York, United States", time: "20:30", terminal: "7" },
        to: { code: "LHR", city: "London, United Kingdom", time: "07:00", terminal: "5" },
        aircraft: "BOEING 787-9",
        stops: "0",
        meals: "Available",
        distance: "3,459"
      }
    ]
  };

  // Sample data for return trip
  const returnTripData = {
    tripDates: "01 JAN 2025 › 15 JAN 2025",
    destination: "NEW YEAR TRIP TO TOKYO",
    passengers: [
      { name: "JOHNSON/MARY MS." },
      { name: "JOHNSON/DAVID MR." }
    ],
    reservationCode: "XYZ789",
    airlineReservationCode: "XYZ789",
    segments: [
      {
        departureDay: "WEDNESDAY 01 JAN",
        airline: "JAPAN AIRLINES",
        flightNo: "JL 006",
        duration: "13hr(s) 45min(s)",
        flightClass: "Premium Economy (W)",
        status: "Confirmed",
        from: { code: "LAX", city: "Los Angeles, United States", time: "11:50", terminal: "B" },
        to: { code: "NRT", city: "Tokyo, Japan", time: "16:35", terminal: "2" },
        aircraft: "BOEING 777-300ER",
        stops: "0",
        meals: "Available",
        distance: "5,478"
      },
      {
        departureDay: "WEDNESDAY 15 JAN",
        airline: "JAPAN AIRLINES",
        flightNo: "JL 005",
        duration: "11hr(s) 30min(s)",
        flightClass: "Premium Economy (W)",
        status: "Confirmed",
        from: { code: "NRT", city: "Tokyo, Japan", time: "17:05", terminal: "2" },
        to: { code: "LAX", city: "Los Angeles, United States", time: "10:35", terminal: "B" },
        aircraft: "BOEING 777-300ER",
        stops: "0",
        meals: "Available",
        distance: "5,478"
      }
    ]
  };

  const getCurrentData = () => {
    switch (selectedDemo) {
      case 'single-segment':
        return singleSegmentData;
      case 'return-trip':
        return returnTripData;
      default:
        return multiSegmentData;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Demo Controls */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            ✈️ Flight Ticket Component Demo
          </h1>
          <p className="text-gray-600 mb-6">
            This component generates professional airline e-tickets that are print-ready and PDF-compatible.
          </p>
          
          <div className="flex flex-wrap gap-4 mb-4">
            <button
              onClick={() => setSelectedDemo('multi-segment')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedDemo === 'multi-segment'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Multi-Segment Trip
            </button>
            <button
              onClick={() => setSelectedDemo('single-segment')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedDemo === 'single-segment'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Single Segment
            </button>
            <button
              onClick={() => setSelectedDemo('return-trip')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedDemo === 'return-trip'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Return Trip
            </button>
          </div>

          <div className="flex flex-wrap gap-4">
            <PrintButton buttonText="🖨️ Print Ticket" />
            <DocumentDownloadButton
              ticketData={getCurrentData()}
              buttonText="📄 Download Document"
            />
            <button
              onClick={() => navigator.clipboard.writeText(window.location.href)}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              📋 Copy Link
            </button>
          </div>
        </div>

        {/* Flight Ticket Component */}
        <FlightTicket {...getCurrentData()} />

        {/* Usage Instructions */}
        <div className="bg-white rounded-lg shadow-md p-6 mt-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Usage Instructions</h2>
          <div className="space-y-4 text-sm text-gray-600">
            <div>
              <h3 className="font-semibold text-gray-900">Basic Usage:</h3>
              <pre className="bg-gray-100 p-3 rounded mt-2 overflow-x-auto">
{`<FlightTicket
  tripDates="18 JUL 2021  19 JUL 2021"
  destination="TRIP TO NEW YORK CITY"
  passengers={[{ name: "COOPER/JANE" }]}
  reservationCode="NHG8IQ"
  airlineReservationCode="NHG8IQ"
  segments={[...]}
/>`}
              </pre>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">PDF Generation:</h3>
              <pre className="bg-gray-100 p-3 rounded mt-2 overflow-x-auto">
{`import PDFDownloadButton from '../components/PDFDownloadButton';

<DocumentDownloadButton
  ticketData={ticketData}
  buttonText="Download Document"
/>`}
              </pre>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Features:</h3>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>✅ Server-side document generation with Puppeteer</li>
                <li>✅ Print-ready and document-compatible</li>
                <li>✅ Responsive design with TailwindCSS</li>
                <li>✅ Supports multiple flight segments</li>
                <li>✅ Professional airline e-ticket styling</li>
                <li>✅ Multiple passenger support</li>
                <li>✅ Embassy-approved format for visa applications</li>
                <li>✅ Detailed flight information display</li>
                <li>✅ Professional disclaimer notices</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlightTicketDemo;
