import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import PassengerDetailsSection from '../components/PassengerDetailsSection';

const PassengerDetailsDemo = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [submittedData, setSubmittedData] = useState(null);

  const handleSubmit = async (formData) => {
    console.log('🚀 Form submitted with data:', formData);
    
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setSubmittedData(formData);
      
      // In a real app, you would navigate to checkout with the data
      // navigate('/checkout', { state: { passengerData: formData } });
      
      alert('Form submitted successfully! Check console for data.');
    }, 2000);
  };

  const resetDemo = () => {
    setSubmittedData(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            New Passenger Details Section
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Completely rebuilt from scratch with modern design, dynamic passenger management, 
            and professional validation.
          </p>
        </div>

        {/* Features List */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">✨ Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm text-gray-700">Empty fields by default</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm text-gray-700">Email validation</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm text-gray-700">Dynamic passenger rows</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm text-gray-700">Add/Remove passengers</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm text-gray-700">Maximum 2 passengers</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm text-gray-700">Mobile responsive</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm text-gray-700">Smooth animations</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm text-gray-700">Professional styling</span>
              </div>
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8">
          <h2 className="text-lg font-semibold text-blue-900 mb-3">🧪 Test Instructions</h2>
          <ol className="list-decimal list-inside space-y-2 text-blue-800">
            <li>Try typing in the email field - should be empty and editable</li>
            <li>Add passenger names - should be empty and editable</li>
            <li>Click "Add Passenger" to add a second passenger</li>
            <li>Try to add a third passenger - button should be disabled</li>
            <li>Remove the second passenger using the "Remove" button</li>
            <li>Try submitting with empty fields - should show validation errors</li>
            <li>Fill all fields correctly and submit - should succeed</li>
          </ol>
        </div>

        {/* Submitted Data Display */}
        {submittedData && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-6 mb-8">
            <div className="flex justify-between items-start mb-4">
              <h2 className="text-lg font-semibold text-green-900">✅ Form Submitted Successfully!</h2>
              <button
                onClick={resetDemo}
                className="text-sm text-green-700 hover:text-green-900 underline"
              >
                Reset Demo
              </button>
            </div>
            <div className="space-y-3">
              <div>
                <span className="font-medium text-green-800">Email:</span>
                <span className="ml-2 text-green-700">{submittedData.email}</span>
              </div>
              <div>
                <span className="font-medium text-green-800">Passengers:</span>
                <div className="ml-4 mt-2 space-y-1">
                  {submittedData.passengers.map((passenger, index) => (
                    <div key={index} className="text-green-700">
                      Passenger {index + 1}: {passenger.firstName} {passenger.lastName}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* The New Component */}
        <PassengerDetailsSection
          onSubmit={handleSubmit}
          isLoading={isLoading}
          submitButtonText="Continue to Payment"
        />

        {/* Navigation */}
        <div className="mt-8 text-center">
          <button
            onClick={() => navigate('/')}
            className="text-blue-600 hover:text-blue-800 underline"
          >
            ← Back to Homepage
          </button>
        </div>
      </div>
    </div>
  );
};

export default PassengerDetailsDemo;
