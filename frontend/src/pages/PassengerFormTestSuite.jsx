import React, { useState } from 'react';
import PassengerDetailsForm from '../components/PassengerDetailsForm';

const PassengerFormTestSuite = () => {
  const [passengers, setPassengers] = useState([{ id: 1, firstName: '', lastName: '' }]);
  const [email, setEmail] = useState('');
  const [testResults, setTestResults] = useState([]);

  const addTestResult = (testName, passed, message) => {
    setTestResults(prev => [...prev, { testName, passed, message, timestamp: new Date().toLocaleTimeString() }]);
  };

  const runTests = () => {
    setTestResults([]);
    
    // Test 1: Initial state
    const initialPassengersEmpty = passengers.length === 1 && passengers[0].firstName === '' && passengers[0].lastName === '';
    const initialEmailEmpty = email === '';
    
    addTestResult('Initial State', initialPassengersEmpty && initialEmailEmpty, 
      `Passengers: ${passengers.length} (empty: ${initialPassengersEmpty}), Email: "${email}" (empty: ${initialEmailEmpty})`);

    // Test 2: Add passenger functionality
    const canAddPassenger = passengers.length < 2;
    addTestResult('Can Add Passenger', canAddPassenger,
      `Current passengers: ${passengers.length}/2`);

    // Test 3: Remove passenger functionality (should be disabled with only 1 passenger)
    const cannotRemoveLastPassenger = passengers.length === 1;
    addTestResult('Cannot Remove Last Passenger', cannotRemoveLastPassenger, 
      `Remove button should be hidden with only ${passengers.length} passenger`);
  };

  const handleSubmit = (formData) => {
    console.log('Form submitted:', formData);
    
    // Test form submission
    const hasPassengers = formData.passengers && formData.passengers.length > 0;
    const hasEmail = formData.email && formData.email.trim() !== '';
    const allPassengersValid = formData.passengers.every(p => p.firstName.trim() && p.lastName.trim());
    
    addTestResult('Form Submission', hasPassengers && hasEmail && allPassengersValid, 
      `Passengers: ${formData.passengers?.length || 0}, Email: "${formData.email}", All valid: ${allPassengersValid}`);
    
    alert('Form submitted successfully! Check test results below.');
  };

  const handlePassengersChange = (updatedPassengers) => {
    console.log('Passengers updated:', updatedPassengers);
    setPassengers(updatedPassengers);
    
    // Test passenger updates
    addTestResult('Passenger Update', true, 
      `Updated to ${updatedPassengers.length} passengers`);
  };

  const handleEmailChange = (updatedEmail) => {
    console.log('Email updated:', updatedEmail);
    setEmail(updatedEmail);
    
    // Test email updates
    addTestResult('Email Update', true, 
      `Email updated to: "${updatedEmail}"`);
  };

  const resetForm = () => {
    setPassengers([{ id: 1, firstName: '', lastName: '' }]);
    setEmail('');
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h1 className="text-3xl font-bold mb-6 text-center text-blue-600">
            Passenger Form Test Suite
          </h1>
          
          {/* Test Controls */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="font-semibold text-blue-800 mb-3">Test Controls:</h2>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={runTests}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Run Tests
              </button>
              <button
                onClick={resetForm}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Reset Form
              </button>
            </div>
          </div>

          {/* Current State Display */}
          <div className="mb-6 p-4 bg-green-50 rounded-lg">
            <h2 className="font-semibold text-green-800 mb-2">Current State:</h2>
            <div className="text-sm text-green-700 space-y-1">
              <p><strong>Passengers:</strong> {passengers.length}</p>
              <p><strong>Email:</strong> "{email}"</p>
              <p><strong>Passenger Details:</strong></p>
              <ul className="ml-4 space-y-1">
                {passengers.map((p, index) => (
                  <li key={p.id}>
                    Passenger {index + 1}: "{p.firstName}" "{p.lastName}"
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="mb-6 p-4 bg-yellow-50 rounded-lg">
              <h2 className="font-semibold text-yellow-800 mb-3">Test Results:</h2>
              <div className="space-y-2">
                {testResults.map((result, index) => (
                  <div key={index} className={`p-2 rounded text-sm ${
                    result.passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    <div className="flex justify-between items-start">
                      <span className="font-medium">
                        {result.passed ? '✅' : '❌'} {result.testName}
                      </span>
                      <span className="text-xs opacity-75">{result.timestamp}</span>
                    </div>
                    <div className="text-xs mt-1">{result.message}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Manual Test Instructions */}
          <div className="mb-6 p-4 bg-purple-50 rounded-lg">
            <h2 className="font-semibold text-purple-800 mb-2">Manual Test Instructions:</h2>
            <ol className="text-sm text-purple-700 space-y-1 list-decimal list-inside">
              <li>Verify all fields are empty on page load</li>
              <li>Type in the email field - should be editable</li>
              <li>Type in First Name and Last Name - should be editable</li>
              <li>Click "Add Passenger" - should add a new passenger block</li>
              <li>Add passengers until you reach 2 - button should disable</li>
              <li>Click "Remove" on any passenger (except when only 1 remains)</li>
              <li>Try to submit with empty fields - should show validation errors</li>
              <li>Fill all fields and submit - should succeed</li>
            </ol>
          </div>
        </div>

        {/* The Actual Form */}
        <div className="bg-white rounded-lg shadow-lg">
          <PassengerDetailsForm
            initialPassengers={passengers}
            initialEmail={email}
            onSubmit={handleSubmit}
            onPassengersChange={handlePassengersChange}
            onEmailChange={handleEmailChange}
            submitButtonText="Test Submit"
          />
        </div>
      </div>
    </div>
  );
};

export default PassengerFormTestSuite;
