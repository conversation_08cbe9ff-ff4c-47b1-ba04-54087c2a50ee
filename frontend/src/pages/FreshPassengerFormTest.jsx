import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import PassengerDetailsForm from '../components/PassengerDetailsForm';

const FreshPassengerFormTest = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [submittedData, setSubmittedData] = useState(null);

  const handleSubmit = (formData) => {
    console.log('🚀 Fresh PassengerDetailsForm submitted:', formData);
    setIsLoading(true);
    
    // Simulate processing
    setTimeout(() => {
      setIsLoading(false);
      setSubmittedData(formData);
      alert('Form submitted successfully! Check console and data below.');
    }, 2000);
  };

  const resetTest = () => {
    setSubmittedData(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🆕 Fresh PassengerDetailsForm
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Completely rebuilt from scratch! Clean state management, empty fields by default, 
            working Add/Remove passengers, and professional styling.
          </p>
        </div>

        {/* Features */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">✨ Fresh Implementation Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">All fields empty by default</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Internal state management</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Working Add Passenger button</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Working Remove buttons</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Maximum 2 passengers</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Real-time validation</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Email format validation</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Professional styling</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Mobile responsive</span>
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8">
          <h2 className="text-lg font-semibold text-blue-900 mb-3">🧪 Test Instructions</h2>
          <ol className="list-decimal list-inside space-y-2 text-blue-800">
            <li><strong>Check empty fields:</strong> All fields should be completely empty on page load</li>
            <li><strong>Test email input:</strong> Type in email field - should be fully editable</li>
            <li><strong>Test passenger inputs:</strong> Type in First/Last Name - should be fully editable</li>
            <li><strong>Test Add Passenger:</strong> Click "Add Passenger" - should instantly add Passenger 2</li>
            <li><strong>Test multiple passengers:</strong> Keep adding up to 2 passengers - button should disable</li>
            <li><strong>Test Remove:</strong> Click "Remove" on any passenger except Passenger 1</li>
            <li><strong>Test validation:</strong> Try submitting with empty fields - should show errors</li>
            <li><strong>Test success:</strong> Fill all fields and submit - should work perfectly</li>
          </ol>
        </div>

        {/* State Information */}
        <div className="bg-green-50 border border-green-200 rounded-xl p-6 mb-8">
          <h2 className="text-lg font-semibold text-green-900 mb-3">🔧 Implementation Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-green-800 mb-2">State Structure:</h3>
              <pre className="text-xs text-green-700 bg-green-100 p-2 rounded">
{`const [email, setEmail] = useState('');
const [passengers, setPassengers] = useState([
  { firstName: '', lastName: '' }
]);
const [errors, setErrors] = useState({});`}
              </pre>
            </div>
            <div>
              <h3 className="font-medium text-green-800 mb-2">Key Features:</h3>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• No prop dependencies</li>
                <li>• Self-contained state</li>
                <li>• Clean validation logic</li>
                <li>• Professional error handling</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Submitted Data Display */}
        {submittedData && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-6 mb-8">
            <div className="flex justify-between items-start mb-4">
              <h2 className="text-lg font-semibold text-green-900">✅ Form Submitted Successfully!</h2>
              <button
                onClick={resetTest}
                className="text-sm text-green-700 hover:text-green-900 underline"
              >
                Reset Test
              </button>
            </div>
            <div className="space-y-3">
              <div>
                <span className="font-medium text-green-800">Email:</span>
                <span className="ml-2 text-green-700">{submittedData.email}</span>
              </div>
              <div>
                <span className="font-medium text-green-800">Passengers ({submittedData.passengers.length}):</span>
                <div className="ml-4 mt-2 space-y-1">
                  {submittedData.passengers.map((passenger, index) => (
                    <div key={index} className="text-green-700">
                      Passenger {passenger.id}: {passenger.firstName} {passenger.lastName}
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="mt-4 p-3 bg-green-100 rounded">
              <h3 className="font-medium text-green-800 mb-2">Raw Data:</h3>
              <pre className="text-xs text-green-700 overflow-auto">
                {JSON.stringify(submittedData, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* The Fresh Component */}
        <PassengerDetailsForm
          onSubmit={handleSubmit}
          isLoading={isLoading}
          submitButtonText="Continue to Payment"
        />

        {/* Navigation */}
        <div className="mt-8 text-center space-x-4">
          <button
            onClick={() => navigate('/')}
            className="text-gray-600 hover:text-gray-800 underline"
          >
            Back to Homepage
          </button>
        </div>
      </div>
    </div>
  );
};

export default FreshPassengerFormTest;
