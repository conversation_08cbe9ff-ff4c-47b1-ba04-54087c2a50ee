import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const UseCasesPage = () => {
  const useCases = [
    {
      title: "Visa Applications",
      subtitle: "Schengen, US, UK & More",
      description: "Applying for a visa in a country that requires proof of onward travel? The great news is that you don't need to actually buy a return ticket — a formal reservation made with our service to show at customs or airport control usually suffices. We'll help you ensure a smooth and safe visa application process.",
      image: "https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=400&h=300&fit=crop&crop=center&auto=format",
      features: [
        "Embassy-approved documentation",
        "Accepted by 195+ embassies worldwide",
        "Instant document delivery",
        "Valid for 48 hours"
      ],
      bgColor: "bg-brand-50",
      borderColor: "border-brand-200",
      iconColor: "text-brand-600"
    },
    {
      title: "Digital Nomad Visa",
      subtitle: "Remote Work & Long-term Stay",
      description: "Planning a long-term stay abroad? Our tickets help meet the requirements for digital nomad visas, making your new lifestyle easier to secure. Get proof of onward travel and work remotely or explore new cultures easily. With our onward ticket service, you have the freedom to live and work anywhere.",
      image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop&crop=center&auto=format",
      features: [
        "Perfect for remote workers",
        "Flexible travel dates",
        "Works for multiple countries",
        "No commitment to actual travel"
      ],
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
      iconColor: "text-green-600"
    },
    {
      title: "Flexible Travel Plans",
      subtitle: "Spontaneous & Open-ended Trips",
      description: "Have uncertain plans without a specific return date? Get the onward ticket for proof of travel, eliminating the need for a rigid schedule. Our tickets offer flexibility, allowing you to change your plans without stress. Perfect for spontaneous travelers who like to keep their options open.",
      image: "https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400&h=300&fit=crop&crop=center&auto=format",
      features: [
        "Change plans without penalty",
        "No rigid schedules required",
        "Perfect for backpackers",
        "Explore at your own pace"
      ],
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      iconColor: "text-purple-600"
    },
    {
      title: "Border Entry Requirements",
      subtitle: "Immigration & Customs Control",
      description: "Traveling to a country with strict entry rules and need evidence for border control? Be well-prepared with our dummy airport ticket. Our tickets are accepted by immigration authorities worldwide, ensuring a hassle-free entry. Don't get caught unprepared at the border — travel with confidence using our service.",
      image: "https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=400&h=300&fit=crop&crop=center&auto=format",
      features: [
        "Accepted by immigration worldwide",
        "Prevents border complications",
        "Professional documentation",
        "Instant verification"
      ],
      bgColor: "bg-accent-50",
      borderColor: "border-accent-200",
      iconColor: "text-accent-600"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-brand-50/30">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 via-blue-500/5 to-accent-500/5"></div>
        
        <div className="container-modern relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center bg-brand-100 text-brand-700 px-6 py-3 rounded-full text-sm font-semibold mb-8">
              <span className="w-2 h-2 bg-brand-500 rounded-full mr-3 animate-pulse"></span>
              USE CASES
            </div>
            
            <h1 className="text-5xl md:text-6xl font-black text-brand-800 mb-8 leading-tight">
              When you might need 
              <br />
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent">our service.</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-brand-700 leading-relaxed font-medium mb-12">
              From visa applications to flexible travel plans, discover how VerifiedOnward 
              <br className="hidden md:block" />
              helps travelers in various situations worldwide.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Use Cases Grid */}
      <section className="py-20">
        <div className="container-modern">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {useCases.map((useCase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`${useCase.bgColor} rounded-3xl p-8 border-2 ${useCase.borderColor} shadow-aviation hover:shadow-aviation-hover transition-all duration-300 group relative overflow-hidden transform hover:-translate-y-2`}
              >
                {/* Premium shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                
                <div className="relative z-10">
                  <div className="flex items-start space-x-6 mb-6">
                    <img 
                      src={useCase.image} 
                      alt={useCase.title}
                      className="w-20 h-20 rounded-2xl object-cover shadow-aviation"
                    />
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-neutral-800 mb-2">{useCase.title}</h3>
                      <p className={`text-sm font-semibold ${useCase.iconColor} mb-4`}>{useCase.subtitle}</p>
                    </div>
                  </div>
                  
                  <p className="text-neutral-700 leading-relaxed mb-6">{useCase.description}</p>
                  
                  <div className="space-y-3">
                    {useCase.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <div className={`w-5 h-5 ${useCase.iconColor} rounded-full flex items-center justify-center`}>
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-neutral-700 font-medium">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-brand-500 to-brand-600">
        <div className="container-modern text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl md:text-5xl font-black text-white mb-8">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-brand-100 mb-12 max-w-2xl mx-auto">
              Join 75,000+ travelers who trust VerifiedOnward for their flight reservations.
            </p>
            <Link
              to="/#search-form"
              className="inline-flex items-center bg-white text-brand-600 px-12 py-5 rounded-2xl text-lg font-bold hover:bg-brand-50 transition-all duration-300 shadow-aviation hover:shadow-aviation-hover transform hover:scale-105"
            >
              <span>Get Your Reservation</span>
              <svg className="w-5 h-5 ml-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default UseCasesPage;
