import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useBooking } from '../context/BookingContext';

const DebugPage = () => {
  const navigate = useNavigate();
  const {
    searchData,
    selectedFlight,
    selectedOutboundFlight,
    selectedReturnFlight,
    passengers,
    email,
    setSearchData,
    setSelectedFlight,
    setPassengers,
    setEmail
  } = useBooking();

  const simulateBookingFlow = () => {
    console.log('🧪 Simulating booking flow...');

    // Step 1: Set search data
    const mockSearchData = {
      from: { code: 'JFK', name: 'John F. Kennedy International Airport', city: 'New York' },
      to: { code: 'LAX', name: 'Los Angeles International Airport', city: 'Los Angeles' },
      departureDate: '2025-07-15',
      tripType: 'oneWay',
      passengers: 1
    };
    setSearchData(mockSearchData);
    console.log('✅ Search data set:', mockSearchData);

    // Step 2: Set selected flight
    const mockFlight = {
      id: 'test-flight-1',
      airline: 'American Airlines',
      flightNumber: 'AA123',
      departure: {
        airport: 'JFK',
        city: 'New York',
        time: '2025-07-15T10:00:00Z'
      },
      arrival: {
        airport: 'LAX',
        city: 'Los Angeles',
        time: '2025-07-15T13:00:00Z'
      },
      price: 4.99
    };
    setSelectedFlight(mockFlight);
    console.log('✅ Flight selected:', mockFlight);

    // Step 3: Set passenger data
    const mockPassengers = [
      { firstName: 'Test', lastName: 'User' }
    ];
    setPassengers(mockPassengers);
    console.log('✅ Passengers set:', mockPassengers);

    // Step 4: Set email
    const mockEmail = '<EMAIL>';
    setEmail(mockEmail);
    console.log('✅ Email set:', mockEmail);

    console.log('🎯 All data set, navigating to checkout...');

    // Navigate to checkout
    setTimeout(() => {
      navigate('/checkout');
    }, 1000);
  };

  const setMinimalDataAndGoToCheckout = () => {
    console.log('🚀 Setting minimal data for checkout...');

    // Just set the absolute minimum needed
    const mockPassengers = [{ firstName: 'Test', lastName: 'User' }];
    const mockEmail = '<EMAIL>';

    setPassengers(mockPassengers);
    setEmail(mockEmail);

    console.log('✅ Minimal data set - passengers and email');
    console.log('- passengers:', mockPassengers);
    console.log('- email:', mockEmail);

    // Navigate immediately
    navigate('/checkout');
  };

  return (
    <div className="min-h-screen py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Debug Page</h1>
        
        <div className="bg-white rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current Booking State</h2>
          <div className="space-y-2 text-sm">
            <div><strong>Search Data:</strong> {searchData ? '✅ Present' : '❌ Missing'}</div>
            <div><strong>Selected Flight:</strong> {selectedFlight ? '✅ Present' : '❌ Missing'}</div>
            <div><strong>Passengers:</strong> {passengers.length > 0 ? `✅ ${passengers.length} passengers` : '❌ Missing'}</div>
            <div><strong>Email:</strong> {email ? '✅ Present' : '❌ Missing'}</div>
          </div>
          
          {searchData && (
            <details className="mt-4">
              <summary className="cursor-pointer font-medium">Search Data Details</summary>
              <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                {JSON.stringify(searchData, null, 2)}
              </pre>
            </details>
          )}
          
          {selectedFlight && (
            <details className="mt-4">
              <summary className="cursor-pointer font-medium">Flight Details</summary>
              <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                {JSON.stringify(selectedFlight, null, 2)}
              </pre>
            </details>
          )}
        </div>

        <div className="bg-white rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Actions</h2>
          <div className="space-x-4">
            <button
              onClick={simulateBookingFlow}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              Simulate Complete Booking Flow
            </button>

            <button
              onClick={setMinimalDataAndGoToCheckout}
              className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700"
            >
              Set Minimal Data & Go to Checkout
            </button>

            <button
              onClick={() => navigate('/checkout')}
              className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700"
            >
              Go to Checkout (Current State)
            </button>

            <button
              onClick={() => {
                console.log('🔍 Current localStorage data:');
                console.log('- searchData:', localStorage.getItem('bookingSearchData'));
                console.log('- selectedFlight:', localStorage.getItem('bookingSelectedFlight'));
                console.log('- passengers:', localStorage.getItem('bookingPassengers'));
                console.log('- email:', localStorage.getItem('bookingEmail'));
              }}
              className="bg-yellow-600 text-white px-6 py-2 rounded-lg hover:bg-yellow-700"
            >
              Check localStorage
            </button>

            <button
              onClick={() => navigate('/simple-checkout')}
              className="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700"
            >
              Test Simple Checkout
            </button>

            <button
              onClick={() => navigate('/')}
              className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"
            >
              Go to Home
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugPage;
