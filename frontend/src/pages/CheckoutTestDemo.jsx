import React from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

const CheckoutTestDemo = () => {
  const navigate = useNavigate();

  const testCheckoutWithData = () => {
    // Create comprehensive test data
    const testData = {
      searchData: {
        from: 'London Heathrow (LHR)',
        to: 'New York JFK (JFK)',
        departureDate: '2025-07-15',
        tripType: 'oneWay'
      },
      selectedFlight: {
        id: 'test-flight-1',
        airline: {
          name: 'British Airways',
          code: 'BA',
          logo: 'https://via.placeholder.com/40x40?text=BA'
        },
        flightNumber: 'BA 117',
        from: 'London Heathrow (LHR)',
        to: 'New York JFK (JFK)',
        departure: {
          airport: 'London Heathrow (LHR)',
          time: '10:00',
          date: '2025-07-15'
        },
        arrival: {
          airport: 'New York JFK (JFK)',
          time: '15:30',
          date: '2025-07-15'
        },
        duration: '8h 30m',
        price: 4.99,
        stops: 0
      },
      passengers: [
        {
          id: 1,
          firstName: 'HUDIFA',
          lastName: 'MISRATI',
          dateOfBirth: '1990-01-01'
        }
      ],
      email: '<EMAIL>',
      tripType: 'oneWay',
      totalPrice: 4.99
    };

    // Save to sessionStorage for reload-proofing
    sessionStorage.setItem('checkoutBookingData', JSON.stringify(testData));

    // Navigate to checkout with state
    navigate('/checkout', {
      state: testData
    });
  };

  const testCheckoutWithReturnFlight = () => {
    // Create test data with return flight
    const testData = {
      searchData: {
        from: 'London Heathrow (LHR)',
        to: 'New York JFK (JFK)',
        departureDate: '2025-07-15',
        returnDate: '2025-07-22',
        tripType: 'return'
      },
      selectedOutboundFlight: {
        id: 'test-outbound-1',
        airline: {
          name: 'British Airways',
          code: 'BA',
          logo: 'https://via.placeholder.com/40x40?text=BA'
        },
        flightNumber: 'BA 117',
        from: 'London Heathrow (LHR)',
        to: 'New York JFK (JFK)',
        departure: {
          airport: 'London Heathrow (LHR)',
          time: '10:00',
          date: '2025-07-15'
        },
        arrival: {
          airport: 'New York JFK (JFK)',
          time: '15:30',
          date: '2025-07-15'
        },
        duration: '8h 30m',
        price: 4.99,
        stops: 0
      },
      selectedReturnFlight: {
        id: 'test-return-1',
        airline: {
          name: 'American Airlines',
          code: 'AA',
          logo: 'https://via.placeholder.com/40x40?text=AA'
        },
        flightNumber: 'AA 106',
        from: 'New York JFK (JFK)',
        to: 'London Heathrow (LHR)',
        departure: {
          airport: 'New York JFK (JFK)',
          time: '18:00',
          date: '2025-07-22'
        },
        arrival: {
          airport: 'London Heathrow (LHR)',
          time: '06:30',
          date: '2025-07-23'
        },
        duration: '7h 30m',
        price: 4.99,
        stops: 0
      },
      passengers: [
        {
          id: 1,
          firstName: 'HUDIFA',
          lastName: 'MISRATI',
          dateOfBirth: '1990-01-01'
        }
      ],
      email: '<EMAIL>',
      tripType: 'return',
      totalPrice: 9.98
    };

    // Save to sessionStorage for reload-proofing
    sessionStorage.setItem('checkoutBookingData', JSON.stringify(testData));

    // Navigate to checkout with state
    navigate('/checkout', {
      state: testData
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🧪 Checkout Test Demo
          </h1>
          <p className="text-gray-600">
            Test the new checkout page with demo payment buttons
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {/* One Way Test */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-lg p-6"
          >
            <h2 className="text-xl font-semibold mb-4 text-blue-600">
              ✈️ One Way Flight Test
            </h2>
            <div className="space-y-3 mb-6">
              <div className="text-sm text-gray-600">
                <p><strong>Route:</strong> London (LHR) → New York (JFK)</p>
                <p><strong>Passenger:</strong> HUDIFA MISRATI</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Price:</strong> $4.99</p>
              </div>
            </div>
            <button
              onClick={testCheckoutWithData}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
            >
              Test One Way Checkout
            </button>
          </motion.div>

          {/* Return Trip Test */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-lg shadow-lg p-6"
          >
            <h2 className="text-xl font-semibold mb-4 text-purple-600">
              🔄 Return Trip Test
            </h2>
            <div className="space-y-3 mb-6">
              <div className="text-sm text-gray-600">
                <p><strong>Route:</strong> London ⇄ New York</p>
                <p><strong>Outbound:</strong> BA 117 (Jul 15)</p>
                <p><strong>Return:</strong> AA 106 (Jul 22)</p>
                <p><strong>Passenger:</strong> HUDIFA MISRATI</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Price:</strong> $9.98</p>
              </div>
            </div>
            <button
              onClick={testCheckoutWithReturnFlight}
              className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors font-semibold"
            >
              Test Return Trip Checkout
            </button>
          </motion.div>
        </div>

        {/* Features List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-lg p-6 mt-8"
        >
          <h2 className="text-xl font-semibold mb-4 text-green-600">
            ✅ New Checkout Features
          </h2>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-900">✨ UI Improvements</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Dynamic flight summary display</li>
                <li>• Passenger details with email confirmation</li>
                <li>• Trust badges (SSL, Instant Delivery)</li>
                <li>• Professional card-based layout</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-900">💳 Payment Options</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Stripe demo payment button</li>
                <li>• PayPal demo payment button</li>
                <li>• Success toast notifications</li>
                <li>• Reload-proof with sessionStorage</li>
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Navigation */}
        <div className="text-center mt-8">
          <button
            onClick={() => navigate('/')}
            className="bg-gray-600 text-white py-2 px-6 rounded-lg hover:bg-gray-700 transition-colors"
          >
            ← Back to Home
          </button>
        </div>
      </div>
    </div>
  );
};

export default CheckoutTestDemo;
