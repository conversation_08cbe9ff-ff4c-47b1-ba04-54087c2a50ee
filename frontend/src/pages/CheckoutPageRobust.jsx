import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useBooking } from '../context/BookingContext';
import StripePayment from '../components/StripePayment';
import PayPalPayment from '../components/PayPalPayment';
import { generateReservationCode } from '../utils/reservationCodeGenerator';
import {
  CreditCardIcon,
  ArrowLeftIcon,
  ShieldCheckIcon,
  UserIcon,
  EnvelopeIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/outline';

const CheckoutPageRobust = () => {
  console.log('🔍 CheckoutPageRobust: Component rendering...');

  const navigate = useNavigate();
  const [paymentMethod, setPaymentMethod] = useState('stripe');
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [finalBookingData, setFinalBookingData] = useState(null);

  // Get booking context data
  let contextData = null;
  try {
    contextData = useBooking();
    console.log('🔍 CheckoutPageRobust: Context data:', contextData);
  } catch (error) {
    console.warn('⚠️ CheckoutPageRobust: Error accessing booking context:', error);
  }

  // Load booking data from context and localStorage
  useEffect(() => {
    console.log('🔍 CheckoutPageRobust: Loading booking data...');

    try {

      // Try to get data from localStorage as backup
      const localStorageData = {
        passengers: localStorage.getItem('bookingPassengers'),
        email: localStorage.getItem('bookingEmail'),
        selectedFlight: localStorage.getItem('bookingSelectedFlight'),
        selectedOutboundFlight: localStorage.getItem('bookingSelectedOutboundFlight'),
        selectedReturnFlight: localStorage.getItem('bookingSelectedReturnFlight'),
        tripType: localStorage.getItem('bookingTripType')
      };

      console.log('🔍 CheckoutPageRobust: localStorage data:', localStorageData);

      // Parse localStorage data
      let parsedLocalData = {};
      try {
        if (localStorageData.passengers) {
          parsedLocalData.passengers = JSON.parse(localStorageData.passengers);
        }
        if (localStorageData.selectedFlight) {
          parsedLocalData.selectedFlight = JSON.parse(localStorageData.selectedFlight);
        }
        if (localStorageData.selectedOutboundFlight) {
          parsedLocalData.selectedOutboundFlight = JSON.parse(localStorageData.selectedOutboundFlight);
        }
        if (localStorageData.selectedReturnFlight) {
          parsedLocalData.selectedReturnFlight = JSON.parse(localStorageData.selectedReturnFlight);
        }
        parsedLocalData.email = localStorageData.email;
        parsedLocalData.tripType = localStorageData.tripType || 'oneWay';
      } catch (parseError) {
        console.warn('⚠️ CheckoutPageRobust: Error parsing localStorage data:', parseError);
      }

      // Merge context and localStorage data (context takes priority)
      const mergedData = {
        selectedFlight: contextData?.selectedFlight || parsedLocalData.selectedFlight,
        selectedOutboundFlight: contextData?.selectedOutboundFlight || parsedLocalData.selectedOutboundFlight,
        selectedReturnFlight: contextData?.selectedReturnFlight || parsedLocalData.selectedReturnFlight,
        passengers: contextData?.passengers || parsedLocalData.passengers || [],
        email: contextData?.email || parsedLocalData.email || '',
        tripType: contextData?.tripType || parsedLocalData.tripType || 'oneWay'
      };

      console.log('🔍 CheckoutPageRobust: Merged data:', mergedData);
      setFinalBookingData(mergedData);

    } catch (error) {
      console.error('🚨 CheckoutPageRobust: Error loading booking data:', error);
      setFinalBookingData({
        selectedFlight: null,
        selectedOutboundFlight: null,
        selectedReturnFlight: null,
        passengers: [],
        email: '',
        tripType: 'oneWay'
      });
    }
  }, [contextData]);

  // Create demo data as fallback
  const createDemoData = () => ({
    departureFlightData: {
      id: 'demo-flight-1',
      flight: {
        number: 'BA 123',
        departure: {
          airport: 'LHR',
          iataCode: 'LHR',
          city: 'London Heathrow',
          time: '2025-07-15 10:00'
        },
        arrival: {
          airport: 'JFK',
          iataCode: 'JFK',
          city: 'New York JFK',
          time: '2025-07-15 18:00'
        },
        duration: '8h 00m'
      },
      airline: {
        name: 'British Airways',
        code: 'BA',
        logo: 'https://www.gstatic.com/flights/airline_logos/70px/BA.png'
      },
      price: {
        total: 4.99,
        currency: 'USD',
        displayPrice: 4.99,
        originalPrice: 650
      }
    },
    passengers: [{
      id: 1,
      firstName: 'Demo',
      lastName: 'User',
      dateOfBirth: '1990-01-01'
    }],
    email: '<EMAIL>',
    tripType: 'oneWay'
  });

  // Complete loading after data is loaded
  useEffect(() => {
    if (finalBookingData !== null) {
      console.log('🔍 CheckoutPageRobust: Data loaded, completing load...');

      const timer = setTimeout(() => {
        setIsLoading(false);
        console.log('🔍 CheckoutPageRobust: Loading complete');
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [finalBookingData]);

  // Determine what data to use
  const getDisplayData = () => {
    if (!finalBookingData) {
      console.log('🔍 CheckoutPageRobust: No booking data, using demo data');
      return createDemoData();
    }

    // Check if we have real flight data
    const departureFlightData = finalBookingData.selectedFlight || finalBookingData.selectedOutboundFlight;
    const returnFlightData = finalBookingData.selectedReturnFlight;
    const passengers = finalBookingData.passengers || [];
    const email = finalBookingData.email || '';

    if (departureFlightData && passengers.length > 0 && email) {
      console.log('🔍 CheckoutPageRobust: Using real booking data');
      return {
        departureFlightData,
        returnFlightData,
        passengers,
        email,
        tripType: finalBookingData.tripType || 'oneWay'
      };
    } else {
      console.log('🔍 CheckoutPageRobust: Incomplete booking data, using demo data');
      return createDemoData();
    }
  };

  const displayData = getDisplayData();

  // Handle payment success
  const handlePaymentSuccess = async (paymentData) => {
    console.log('🎉 CheckoutPageRobust: Payment successful:', paymentData);
    setIsProcessingPayment(true);

    try {
      // Generate booking reference
      const bookingRef = generateReservationCode();

      // Navigate to success page
      navigate('/success', {
        state: {
          bookingReference: bookingRef,
          paymentData,
          selectedFlight: displayData.departureFlightData,
          returnFlight: displayData.returnFlightData,
          passengers: displayData.passengers,
          email: displayData.email,
          tripType: displayData.tripType
        }
      });
    } catch (err) {
      console.error('🚨 CheckoutPageRobust: Error processing payment:', err);
      setError('Payment processing failed. Please try again.');
      setIsProcessingPayment(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading checkout...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Complete Your Booking
          </h1>
          <p className="text-gray-600">
            Secure checkout for your dummy flight ticket
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Flight Summary */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg shadow-lg p-6 mb-6"
            >
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <PaperAirplaneIcon className="h-5 w-5 mr-2 text-blue-600" />
                Flight Summary
              </h2>
              
              {/* Departure Flight */}
              <div className="border rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <img
                      src={displayData.departureFlightData.airline.logo}
                      alt={displayData.departureFlightData.airline.name}
                      className="h-8 w-8 mr-3"
                      onError={(e) => {
                        e.target.style.display = 'none';
                      }}
                    />
                    <div>
                      <p className="font-semibold">{displayData.departureFlightData.airline.name}</p>
                      <p className="text-sm text-gray-600">{displayData.departureFlightData.flight.number}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      ${displayData.departureFlightData.price.displayPrice}
                    </p>
                  </div>
                </div>
                <div className="flex justify-between text-sm text-gray-600">
                  <span>
                    {displayData.departureFlightData.flight.departure.city}
                    ({displayData.departureFlightData.flight.departure.iataCode})
                  </span>
                  <span>→</span>
                  <span>
                    {displayData.departureFlightData.flight.arrival.city}
                    ({displayData.departureFlightData.flight.arrival.iataCode})
                  </span>
                </div>
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>{displayData.departureFlightData.flight.departure.time}</span>
                  <span>{displayData.departureFlightData.flight.arrival.time}</span>
                </div>
              </div>

              {/* Return Flight (if applicable) */}
              {displayData.returnFlightData && (
                <div className="border rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <img
                        src={displayData.returnFlightData.airline.logo}
                        alt={displayData.returnFlightData.airline.name}
                        className="h-8 w-8 mr-3"
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                      <div>
                        <p className="font-semibold">{displayData.returnFlightData.airline.name}</p>
                        <p className="text-sm text-gray-600">{displayData.returnFlightData.flight.number}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-green-600">
                        ${displayData.returnFlightData.price.displayPrice}
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>
                      {displayData.returnFlightData.flight.departure.city}
                      ({displayData.returnFlightData.flight.departure.iataCode})
                    </span>
                    <span>→</span>
                    <span>
                      {displayData.returnFlightData.flight.arrival.city}
                      ({displayData.returnFlightData.flight.arrival.iataCode})
                    </span>
                  </div>
                  <div className="flex justify-between text-sm text-gray-500 mt-1">
                    <span>{displayData.returnFlightData.flight.departure.time}</span>
                    <span>{displayData.returnFlightData.flight.arrival.time}</span>
                  </div>
                </div>
              )}
            </motion.div>

            {/* Passenger Details */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-lg shadow-lg p-6 mb-6"
            >
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <UserIcon className="h-5 w-5 mr-2 text-blue-600" />
                Passenger Details
              </h2>
              
              {displayData.passengers.map((passenger, index) => (
                <div key={index} className="border rounded-lg p-4 mb-3 last:mb-0">
                  <p className="font-semibold">Passenger {index + 1}</p>
                  <p className="text-gray-600">{passenger.firstName} {passenger.lastName}</p>
                  {passenger.dateOfBirth && (
                    <p className="text-sm text-gray-500">DOB: {passenger.dateOfBirth}</p>
                  )}
                </div>
              ))}

              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800 flex items-center">
                  <EnvelopeIcon className="h-4 w-4 mr-2" />
                  Ticket will be sent to: {displayData.email}
                </p>
              </div>
            </motion.div>
          </div>

          {/* Payment Section */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-lg shadow-lg p-6 sticky top-8"
            >
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <CreditCardIcon className="h-5 w-5 mr-2 text-blue-600" />
                Payment
              </h2>

              {/* Total */}
              <div className="border-b pb-4 mb-4">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold">Total</span>
                  <span className="text-2xl font-bold text-green-600">
                    ${((displayData.departureFlightData?.price?.displayPrice || displayData.departureFlightData?.price?.total || 4.99) +
                       (displayData.returnFlightData?.price?.displayPrice || displayData.returnFlightData?.price?.total || 0)).toFixed(2)}
                  </span>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {displayData.passengers.length} passenger{displayData.passengers.length > 1 ? 's' : ''}
                </p>
              </div>

              {/* Payment Method Selection */}
              <div className="mb-6">
                <div className="flex space-x-2 mb-4">
                  <button
                    onClick={() => setPaymentMethod('stripe')}
                    className={`flex-1 py-2 px-3 rounded-lg border text-sm font-medium transition-colors ${
                      paymentMethod === 'stripe'
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    💳 Card
                  </button>
                  <button
                    onClick={() => setPaymentMethod('paypal')}
                    className={`flex-1 py-2 px-3 rounded-lg border text-sm font-medium transition-colors ${
                      paymentMethod === 'paypal'
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    🅿️ PayPal
                  </button>
                </div>

                {/* Payment Component */}
                {paymentMethod === 'stripe' && (
                  <StripePayment
                    amount={(displayData.departureFlightData?.price?.displayPrice || displayData.departureFlightData?.price?.total || 4.99) +
                           (displayData.returnFlightData?.price?.displayPrice || displayData.returnFlightData?.price?.total || 0)}
                    onSuccess={handlePaymentSuccess}
                    onError={(err) => setError(err)}
                    disabled={isProcessingPayment}
                  />
                )}

                {paymentMethod === 'paypal' && (
                  <PayPalPayment
                    amount={(displayData.departureFlightData?.price?.displayPrice || displayData.departureFlightData?.price?.total || 4.99) +
                           (displayData.returnFlightData?.price?.displayPrice || displayData.returnFlightData?.price?.total || 0)}
                    onSuccess={handlePaymentSuccess}
                    onError={(err) => setError(err)}
                    disabled={isProcessingPayment}
                  />
                )}
              </div>

              {/* Security Notice */}
              <div className="bg-green-50 rounded-lg p-3 mb-4">
                <div className="flex items-center text-green-800 text-sm">
                  <ShieldCheckIcon className="h-4 w-4 mr-2" />
                  <span>Secure SSL encrypted payment</span>
                </div>
              </div>

              {/* Error Display */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                  <p className="text-red-800 text-sm">{error}</p>
                </div>
              )}

              {/* Back Button */}
              <button
                onClick={() => navigate(-1)}
                className="w-full flex items-center justify-center py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                disabled={isProcessingPayment}
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Flight Selection
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPageRobust;
