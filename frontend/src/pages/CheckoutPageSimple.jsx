import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useBooking } from '../context/BookingContext';
import StripePayment from '../components/StripePayment';
import PayPalPayment from '../components/PayPalPayment';
import ErrorBoundary from '../components/ErrorBoundary';
import { ticketAPI } from '../services/api';
import { generateReservationCode } from '../utils/reservationCodeGenerator';
import {
  LockClosedIcon,
  ShieldCheckIcon,
  CreditCardIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  UserIcon,
  EnvelopeIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/outline';

const CheckoutPageSimple = () => {
  console.log('🔍 CheckoutPageSimple: Component rendering...');

  // Emergency early return for debugging - ALWAYS show this for now
  if (process.env.NODE_ENV === 'development') {
    console.log('🚨 DEBUG: CheckoutPageSimple component called!');

    // Show debug info for any checkout page visit - ALWAYS for now
    return (
      <div className="min-h-screen bg-red-100 p-8">
        <h1 className="text-2xl font-bold text-red-800">🚨 CHECKOUT DEBUG MODE</h1>
        <p className="text-red-600">CheckoutPageSimple component is working!</p>
        <p className="text-red-600">URL: {window.location.href}</p>
        <p className="text-red-600">Time: {new Date().toISOString()}</p>
        <div className="mt-4 p-4 bg-white rounded">
          <h2 className="font-bold">Debug Info:</h2>
          <p>Component loaded successfully</p>
          <p>React Router working</p>
          <p>No JavaScript errors so far</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 bg-blue-500 text-white px-4 py-2 rounded"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  const navigate = useNavigate();
  const [paymentMethod, setPaymentMethod] = useState('stripe');
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [contextError, setContextError] = useState(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  // Add debugging for blank page issue
  console.log('🔍 CheckoutPageSimple: Starting component render...');

  // Get booking data with fallbacks
  let bookingData = {};
  try {
    console.log('🔍 CheckoutPageSimple: Accessing booking context...');
    bookingData = useBooking();
    console.log('🔍 CheckoutPageSimple: Booking context data:', bookingData);
    console.log('🔍 CheckoutPageSimple: Context keys:', Object.keys(bookingData || {}));

    // Check if context is properly loaded
    if (!bookingData.isLoaded) {
      console.log('🔍 CheckoutPageSimple: Context not yet loaded, waiting...');
    }
  } catch (err) {
    console.error('❌ CheckoutPageSimple: Booking context error:', err);
    setContextError(err.message);
    bookingData = {
      passengers: [{ firstName: '', lastName: '', id: 1 }],
      email: '',
      searchData: { tripType: 'oneWay' },
      isLoaded: true
    };
  }

  // Validate essential booking data
  const validateBookingData = () => {
    const errors = [];

    if (!bookingData.passengers || bookingData.passengers.length === 0) {
      errors.push('No passenger information found');
    }

    if (!bookingData.email) {
      errors.push('No contact email found');
    }

    if (!bookingData.searchData) {
      errors.push('No flight search data found');
    }

    const hasFlightData = bookingData.selectedFlight ||
                         bookingData.selectedOutboundFlight ||
                         bookingData.selectedReturnFlight;

    if (!hasFlightData) {
      errors.push('No flight selection found');
    }

    return errors;
  };

  // Ensure we have passenger data structure
  if (!bookingData.passengers || bookingData.passengers.length === 0) {
    console.log('🔧 CheckoutPageSimple: No passenger data available, using empty passenger');
    bookingData.passengers = [{ firstName: '', lastName: '', id: 1 }];
  }

  if (!bookingData.email) {
    console.log('🔧 CheckoutPageSimple: No email available, using empty email');
    bookingData.email = '';
  }

  if (!bookingData.searchData) {
    console.log('🔧 CheckoutPageSimple: Adding fallback search data');
    bookingData.searchData = { tripType: 'oneWay' };
  }

  const { passengers = [], email = '', searchData = {}, isLoaded = false } = bookingData;

  console.log('🔍 CheckoutPageSimple: Extracted data:', {
    passengers: passengers?.length || 0,
    email,
    searchData,
    tripType: searchData?.tripType
  });

  // Check for validation errors
  const validationErrors = validateBookingData();
  const hasValidationErrors = validationErrors.length > 0;

  const handlePaymentSuccess = async (paymentResult) => {
    console.log('Payment successful:', paymentResult);
    setIsProcessingPayment(true);

    try {
      // Save payment info to booking context if available
      if (bookingData?.setPaymentId) {
        bookingData.setPaymentId(paymentResult.paymentId);
      }

      // Generate booking reference if not available
      let bookingRef = bookingData.bookingReference;
      if (!bookingRef) {
        bookingRef = generateReservationCode();
        if (bookingData?.setBookingReference) {
          bookingData.setBookingReference(bookingRef);
        }
      }

      // Generate and send ticket
      try {
        let flightData;
        if (isReturnTrip) {
          // For return flights, create a structure with both flights
          flightData = {
            tripType: 'return',
            outboundFlight: bookingData.selectedOutboundFlight,
            returnFlight: bookingData.selectedReturnFlight,
            searchData: searchData
          };
        } else {
          // For one-way flights, use the selected flight
          flightData = bookingData.selectedFlight || bookingData.selectedOutboundFlight;
        }

        const ticketData = {
          flightData: flightData,
          passengerData: passengers,
          paymentId: paymentResult.paymentId,
          email: email,
          tripType: searchData?.tripType || 'oneWay',
          bookingReference: bookingRef
        };

        console.log('🎫 Generating ticket with data:', ticketData);
        const ticketResponse = await ticketAPI.generateTicket(ticketData);

        if (ticketResponse.bookingReference && bookingData?.setBookingReference) {
          bookingData.setBookingReference(ticketResponse.bookingReference);
        }

        console.log('✅ Ticket generated successfully');
      } catch (ticketError) {
        console.error('⚠️ Ticket generation error:', ticketError);
        // Don't fail the entire flow if ticket generation fails
        // The success page will handle fallback ticket generation
      }

      // Navigate to success page
      navigate('/success');
    } catch (error) {
      console.error('Post-payment processing error:', error);
      // Still navigate to success since payment was successful
      navigate('/success');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const handlePaymentError = (errorMessage) => {
    console.error('Payment error:', errorMessage);
    setError(errorMessage);
  };

  // Calculate price
  const totalPrice = searchData?.tripType === 'return' ? 9.98 : 4.99;

  useEffect(() => {
    // Wait for context to load before showing content
    const timer = setTimeout(() => {
      console.log('🔍 CheckoutPageSimple: Loading complete');
      console.log('🔍 CheckoutPageSimple: Context loaded:', isLoaded);
      console.log('🔍 CheckoutPageSimple: Final data:', { passengers, email, searchData });
      setIsLoading(false);
    }, isLoaded ? 100 : 500); // Wait longer if context isn't loaded

    return () => clearTimeout(timer);
  }, [isLoaded, passengers, email, searchData]);

  console.log('🔍 CheckoutPageSimple: About to render component...');
  console.log('🔍 CheckoutPageSimple: isLoading:', isLoading);
  console.log('🔍 CheckoutPageSimple: hasValidationErrors:', hasValidationErrors);
  console.log('🔍 CheckoutPageSimple: validationErrors:', validationErrors);

  // Emergency debug render - if we see this, component is working
  if (process.env.NODE_ENV === 'development') {
    console.log('🚨 DEBUG: CheckoutPageSimple is rendering!');
    // Emergency test render to debug blank page
    if (window.location.pathname === '/checkout') {
      console.log('🚨 DEBUG: On checkout route, attempting render...');
    }
  }

  // Show loading state
  if (isLoading) {
    console.log('🔍 CheckoutPageSimple: Rendering loading state...');
    return (
      <div className="min-h-screen py-8 px-4 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading checkout...</p>
        </div>
      </div>
    );
  }

  // Show error state if booking data is missing
  if (hasValidationErrors) {
    return (
      <div className="min-h-screen py-8 px-4 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Booking Data Missing</h2>
            <p className="text-gray-600 mb-4">
              We couldn't find your booking information. Please start a new search.
            </p>
            <div className="text-left bg-red-50 rounded-lg p-4 mb-6">
              <p className="text-sm font-medium text-red-800 mb-2">Missing information:</p>
              <ul className="text-sm text-red-700 space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
            <button
              onClick={() => navigate('/')}
              className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium transition-colors"
            >
              Start New Search
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Helper function to format flight details
  const formatFlightDetails = (flight) => {
    if (!flight) return null;
    return {
      airline: flight.airline?.name || 'Unknown Airline',
      flightNumber: flight.flight?.number || 'N/A',
      departure: flight.departure || {},
      arrival: flight.arrival || {},
      duration: flight.duration || 'N/A'
    };
  };

  // Get flight details for display
  const outboundFlight = formatFlightDetails(bookingData.selectedOutboundFlight || bookingData.selectedFlight);
  const returnFlight = formatFlightDetails(bookingData.selectedReturnFlight);
  const isReturnTrip = searchData?.tripType === 'return';

  console.log('🔍 CheckoutPageSimple: Rendering main content...');

  return (
    <ErrorBoundary fallbackMessage="There was an error loading the checkout page. Please try refreshing or start a new booking.">
      <div className="min-h-screen py-8 px-4 bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Complete Your Payment
            </h1>
            <p className="text-gray-600">
              Review your booking details and complete your secure payment
            </p>
          </motion.div>

          {/* Debug Info - Only show in development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-yellow-800 mb-2">Debug Info:</h3>
              <div className="text-sm text-yellow-700">
                <p>Passengers: {passengers.length > 0 ? `${passengers.length} passengers` : 'None'}</p>
                <p>Email: {email || 'Not provided'}</p>
                <p>Trip Type: {searchData?.tripType || 'Unknown'}</p>
                <p>Total Price: ${totalPrice}</p>
                <p>Context Loaded: {isLoaded ? 'Yes' : 'No'}</p>
                {contextError && <p className="text-red-600">Context Error: {contextError}</p>}
              </div>
            </div>
          )}

          {/* Main Content Grid */}
          <div className="grid lg:grid-cols-3 gap-6 lg:gap-8">
            {/* Left Column - Booking Summary */}
            <div className="lg:col-span-2 space-y-6">
              {/* Flight Details */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
              >
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                  <h2 className="text-xl font-semibold text-white flex items-center">
                    <PaperAirplaneIcon className="h-6 w-6 mr-2" />
                    Flight Details
                  </h2>
                </div>

                <div className="p-6 space-y-4">
                  {/* Outbound Flight */}
                  {outboundFlight && (
                    <div className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-semibold text-gray-900">
                          {isReturnTrip ? 'Departure Flight' : 'Flight'}
                        </h3>
                        <span className="text-sm text-gray-500">{outboundFlight.flightNumber}</span>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">From</p>
                          <p className="font-medium">{outboundFlight.departure.airport || 'N/A'}</p>
                          <p className="text-gray-600">{outboundFlight.departure.time || 'N/A'}</p>
                        </div>
                        <div className="text-center sm:text-center">
                          <p className="text-gray-500">Duration</p>
                          <p className="font-medium">{outboundFlight.duration}</p>
                          <p className="text-gray-600">{outboundFlight.airline}</p>
                        </div>
                        <div className="text-left sm:text-right">
                          <p className="text-gray-500">To</p>
                          <p className="font-medium">{outboundFlight.arrival.airport || 'N/A'}</p>
                          <p className="text-gray-600">{outboundFlight.arrival.time || 'N/A'}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Return Flight */}
                  {returnFlight && isReturnTrip && (
                    <div className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-semibold text-gray-900">Return Flight</h3>
                        <span className="text-sm text-gray-500">{returnFlight.flightNumber}</span>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">From</p>
                          <p className="font-medium">{returnFlight.departure.airport || 'N/A'}</p>
                          <p className="text-gray-600">{returnFlight.departure.time || 'N/A'}</p>
                        </div>
                        <div className="text-center sm:text-center">
                          <p className="text-gray-500">Duration</p>
                          <p className="font-medium">{returnFlight.duration}</p>
                          <p className="text-gray-600">{returnFlight.airline}</p>
                        </div>
                        <div className="text-left sm:text-right">
                          <p className="text-gray-500">To</p>
                          <p className="font-medium">{returnFlight.arrival.airport || 'N/A'}</p>
                          <p className="text-gray-600">{returnFlight.arrival.time || 'N/A'}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>

              {/* Passenger Details */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
              >
                <div className="bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4">
                  <h2 className="text-xl font-semibold text-white flex items-center">
                    <UserIcon className="h-6 w-6 mr-2" />
                    Passenger Details
                  </h2>
                </div>

                <div className="p-6">
                  <div className="space-y-4">
                    {passengers.map((passenger, index) => (
                      <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                          <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">
                            {passenger.firstName || 'First Name'} {passenger.lastName || 'Last Name'}
                          </p>
                          <p className="text-sm text-gray-500">Passenger {index + 1}</p>
                        </div>
                      </div>
                    ))}

                    <div className="flex items-center p-3 bg-blue-50 rounded-lg">
                      <EnvelopeIcon className="h-5 w-5 text-blue-600 mr-3" />
                      <div>
                        <p className="font-medium text-gray-900">{email || 'Email not provided'}</p>
                        <p className="text-sm text-gray-500">Contact Email</p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Right Column - Payment */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden lg:sticky lg:top-8"
              >
                {/* Order Summary Header */}
                <div className="bg-gradient-to-r from-purple-600 to-pink-600 px-6 py-4">
                  <h2 className="text-xl font-semibold text-white flex items-center">
                    <CheckCircleIcon className="h-6 w-6 mr-2" />
                    Order Summary
                  </h2>
                </div>

                <div className="p-6 space-y-6">
                  {/* Price Breakdown */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">
                        {isReturnTrip ? 'Return Flight Ticket' : 'One-Way Flight Ticket'}
                      </span>
                      <span className="font-semibold">${totalPrice}</span>
                    </div>
                    <div className="flex justify-between items-center text-sm text-gray-500">
                      <span>Passengers: {passengers.length || 1}</span>
                      <span>Instant Ticket Download</span>
                    </div>
                    <div className="border-t pt-3">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-semibold text-gray-900">Total</span>
                        <span className="text-2xl font-bold text-green-600">${totalPrice}</span>
                      </div>
                    </div>
                  </div>

                  {/* Security Badges */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <LockClosedIcon className="h-4 w-4 mr-1" />
                        <span>SSL Secured</span>
                      </div>
                      <div className="flex items-center">
                        <ShieldCheckIcon className="h-4 w-4 mr-1" />
                        <span>Safe Payment</span>
                      </div>
                    </div>
                  </div>

                  {/* Payment Method Selection */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Choose Payment Method</h3>
                    <div className="space-y-3">
                      <label className="flex items-center space-x-3 cursor-pointer p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <input
                          type="radio"
                          value="stripe"
                          checked={paymentMethod === 'stripe'}
                          onChange={(e) => setPaymentMethod(e.target.value)}
                          className="w-4 h-4 text-blue-600"
                        />
                        <CreditCardIcon className="h-5 w-5 text-gray-600" />
                        <span className="font-medium">Credit/Debit Card</span>
                      </label>

                      <label className="flex items-center space-x-3 cursor-pointer p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <input
                          type="radio"
                          value="paypal"
                          checked={paymentMethod === 'paypal'}
                          onChange={(e) => setPaymentMethod(e.target.value)}
                          className="w-4 h-4 text-blue-600"
                        />
                        <div className="w-5 h-5 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold">P</div>
                        <span className="font-medium">PayPal</span>
                      </label>
                    </div>
                  </div>

                  {/* Payment Form */}
                  <div>
                    {paymentMethod === 'stripe' && (
                      <StripePayment
                        amount={totalPrice}
                        onSuccess={handlePaymentSuccess}
                        onError={handlePaymentError}
                        isProcessing={isProcessingPayment}
                        setIsProcessing={setIsProcessingPayment}
                      />
                    )}

                    {paymentMethod === 'paypal' && (
                      <PayPalPayment
                        amount={totalPrice}
                        onSuccess={handlePaymentSuccess}
                        onError={handlePaymentError}
                        isProcessing={isProcessingPayment}
                        setIsProcessing={setIsProcessingPayment}
                      />
                    )}
                  </div>

                  {/* Error Display */}
                  {error && (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-red-600 text-sm">{error}</p>
                    </div>
                  )}
                </div>
              </motion.div>
            </div>
          </div>

          {/* Back Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-center mt-8"
          >
            <button
              onClick={() => navigate(-1)}
              disabled={isProcessingPayment}
              className="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Flight Selection
            </button>
          </motion.div>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default CheckoutPageSimple;
