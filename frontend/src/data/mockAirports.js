// Comprehensive International Airport Database
// Source: Wikipedia - List of international airports by country
// https://en.wikipedia.org/wiki/List_of_international_airports_by_country
// Complete dataset with 1000+ international airports worldwide

import { comprehensiveAirports } from './comprehensiveAirports.js';

export const mockAirports = comprehensiveAirports;

// Search function for airports with enhanced relevance scoring
export const searchAirports = (query, limit = 15) => {
  if (!query || query.length < 2) {
    return [];
  }

  const searchTerm = query.toLowerCase();
  
  const results = mockAirports.filter(airport => 
    airport.name.toLowerCase().includes(searchTerm) ||
    airport.city.toLowerCase().includes(searchTerm) ||
    airport.iataCode.toLowerCase().includes(searchTerm) ||
    airport.country.toLowerCase().includes(searchTerm)
  );

  // Enhanced sorting by relevance with multiple criteria
  results.sort((a, b) => {
    // 1. Exact IATA code match gets highest priority
    const aIataExact = a.iataCode.toLowerCase() === searchTerm;
    const bIataExact = b.iataCode.toLowerCase() === searchTerm;
    if (aIataExact && !bIataExact) return -1;
    if (!aIataExact && bIataExact) return 1;
    
    // 2. IATA code starts with search term
    const aIataStarts = a.iataCode.toLowerCase().startsWith(searchTerm);
    const bIataStarts = b.iataCode.toLowerCase().startsWith(searchTerm);
    if (aIataStarts && !bIataStarts) return -1;
    if (!aIataStarts && bIataStarts) return 1;
    
    // 3. City name exact match
    const aCityExact = a.city.toLowerCase() === searchTerm;
    const bCityExact = b.city.toLowerCase() === searchTerm;
    if (aCityExact && !bCityExact) return -1;
    if (!aCityExact && bCityExact) return 1;
    
    // 4. City name starts with search term
    const aCityStarts = a.city.toLowerCase().startsWith(searchTerm);
    const bCityStarts = b.city.toLowerCase().startsWith(searchTerm);
    if (aCityStarts && !bCityStarts) return -1;
    if (!aCityStarts && bCityStarts) return 1;
    
    // 5. Airport name starts with search term
    const aNameStarts = a.name.toLowerCase().startsWith(searchTerm);
    const bNameStarts = b.name.toLowerCase().startsWith(searchTerm);
    if (aNameStarts && !bNameStarts) return -1;
    if (!aNameStarts && bNameStarts) return 1;
    
    // 6. Country name starts with search term
    const aCountryStarts = a.country.toLowerCase().startsWith(searchTerm);
    const bCountryStarts = b.country.toLowerCase().startsWith(searchTerm);
    if (aCountryStarts && !bCountryStarts) return -1;
    if (!aCountryStarts && bCountryStarts) return 1;
    
    // 7. Alphabetical by city name as final tiebreaker
    return a.city.localeCompare(b.city);
  });

  return results.slice(0, limit);
};

// Get airport statistics
export const getAirportStats = () => {
  const countries = [...new Set(mockAirports.map(airport => airport.country))];
  const cities = [...new Set(mockAirports.map(airport => airport.city))];
  
  return {
    totalAirports: mockAirports.length,
    totalCountries: countries.length,
    totalCities: cities.length,
    continents: {
      'Africa': mockAirports.filter(a => [
        'Algeria', 'Egypt', 'Libya', 'Morocco', 'Sudan', 'Tunisia', // Northern Africa
        'Benin', 'Burkina Faso', 'Cape Verde', 'The Gambia', 'Ghana', 'Guinea', 'Guinea-Bissau', 'Ivory Coast', 'Liberia', 'Mali', 'Mauritania', 'Niger', 'Nigeria', 'Saint Helena, Ascension and Tristan da Cunha', 'Senegal', 'Sierra Leone', 'Togo', // Western Africa
        'Angola', 'Cameroon', 'Central African Republic', 'Chad', 'Democratic Republic of the Congo', 'Equatorial Guinea', 'Gabon', 'Republic of the Congo', 'São Tomé and Príncipe', // Central Africa
        'Botswana', 'Eswatini', 'Lesotho', 'Namibia', 'South Africa', // Southern Africa
        'Burundi', 'Comoros', 'Djibouti', 'Eritrea', 'Ethiopia', 'Kenya', 'Madagascar', 'Malawi', 'Mauritius', 'Mayotte', 'Mozambique', 'Réunion', 'Rwanda', 'Seychelles', 'Somalia', 'South Sudan', 'Tanzania', 'Uganda', 'Zambia', 'Zimbabwe' // Eastern Africa
      ].includes(a.country)).length,
      'Asia': mockAirports.filter(a => [
        'Kazakhstan', 'Kyrgyzstan', 'Tajikistan', 'Turkmenistan', 'Uzbekistan', // Central Asia
        'China', 'Hong Kong', 'Japan', 'Macau', 'Mongolia', 'North Korea', 'South Korea', 'Taiwan', // Eastern Asia
        'Bangladesh', 'Bhutan', 'India', 'Maldives', 'Nepal', 'Pakistan', 'Sri Lanka', // Southern Asia
        'Brunei', 'Cambodia', 'Indonesia', 'Laos', 'Malaysia', 'Myanmar', 'Philippines', 'Singapore', 'Thailand', 'Timor-Leste', 'Vietnam', // Southeast Asia
        'Afghanistan', 'Armenia', 'Azerbaijan', 'Bahrain', 'Cyprus', 'Georgia', 'Iran', 'Iraq', 'Israel', 'Jordan', 'Kuwait', 'Lebanon', 'Oman', 'Qatar', 'Saudi Arabia', 'Turkey', 'United Arab Emirates' // Southwest Asia and Middle East
      ].includes(a.country)).length,
      'Europe': mockAirports.filter(a => [
        'United Kingdom', 'France', 'Germany', 'Netherlands', 'Belgium', 'Ireland', 'Luxembourg', // Western Europe
        'Austria', 'Switzerland', 'Czech Republic', 'Hungary', 'Poland', 'Slovakia', // Central Europe
        'Spain', 'Italy', 'Portugal', 'Greece', 'Malta', 'Croatia', // Southern Europe
        'Russia', 'Ukraine', 'Romania', 'Bulgaria', 'Serbia', // Eastern Europe
        'Denmark', 'Sweden', 'Norway', 'Finland', 'Iceland', 'Estonia', 'Latvia', 'Lithuania' // Nordic Region
      ].includes(a.country)).length,
      'North America': mockAirports.filter(a => ['United States', 'Canada', 'Mexico'].includes(a.country)).length,
      'South America': mockAirports.filter(a => ['Brazil', 'Argentina', 'Chile', 'Colombia', 'Peru', 'Ecuador', 'Venezuela'].includes(a.country)).length,
      'Oceania': mockAirports.filter(a => ['Australia', 'New Zealand', 'Fiji', 'French Polynesia', 'Guam', 'Papua New Guinea', 'Samoa', 'Tonga', 'Vanuatu', 'Cook Islands'].includes(a.country)).length
    }
  };
};
