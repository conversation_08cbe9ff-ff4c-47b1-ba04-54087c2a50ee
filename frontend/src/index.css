@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import 'react-toastify/dist/ReactToastify.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Design System Base Styles */
@layer base {
  body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Premium Aviation Component Styles */
@layer components {
  /* Premium Aviation Button Styles */
  .btn-primary {
    @apply bg-brand-500 hover:bg-brand-600 text-white font-semibold px-6 py-3 rounded-xl transition-all duration-300 shadow-aviation hover:shadow-aviation-hover focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 transform hover:scale-105;
  }

  .btn-secondary {
    @apply bg-white hover:bg-brand-50 text-brand-600 font-semibold px-6 py-3 rounded-xl border border-brand-200 transition-all duration-300 shadow-soft hover:shadow-aviation focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 transform hover:scale-105;
  }

  .btn-accent {
    @apply bg-accent-500 hover:bg-accent-600 text-white font-semibold px-6 py-3 rounded-xl transition-all duration-300 shadow-gold hover:shadow-accent-glow focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 transform hover:scale-105;
  }

  /* Enhanced CTA Button System - Consistent & Accessible */
  .btn-cta-primary {
    @apply bg-gradient-to-r from-brand-500 via-brand-600 to-brand-700 hover:from-brand-600 hover:via-brand-700 hover:to-brand-800 text-white font-bold px-8 py-4 rounded-2xl transition-all duration-300 shadow-aviation hover:shadow-aviation-hover focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 transform hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden group;
  }

  .btn-cta-secondary {
    @apply bg-white hover:bg-brand-50 text-brand-600 hover:text-brand-700 font-bold px-8 py-4 rounded-2xl border-2 border-brand-200 hover:border-brand-300 transition-all duration-300 shadow-soft hover:shadow-aviation focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 transform hover:scale-[1.02] active:scale-[0.98];
  }

  .btn-cta-large {
    @apply px-12 py-6 text-lg md:text-xl rounded-3xl;
  }

  .btn-cta-small {
    @apply px-6 py-3 text-sm rounded-xl;
  }

  /* Premium Aviation CTA Button (Legacy Support) */
  .btn-aviation-primary {
    @apply btn-cta-primary;
  }

  /* Premium Aviation Form Elements */
  .input-modern {
    @apply w-full px-4 py-3 border border-neutral-300 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-brand-500 transition-all duration-300 bg-white placeholder-neutral-400 text-neutral-900;
  }

  .input-modern:focus {
    @apply shadow-aviation;
  }

  .input-aviation {
    @apply w-full px-5 py-4 border-2 border-neutral-200 rounded-2xl focus:ring-2 focus:ring-brand-500 focus:border-brand-500 transition-all duration-300 bg-white placeholder-neutral-400 text-neutral-900 font-medium shadow-soft hover:shadow-aviation;
  }

  /* Date Input Specific Styles */
  .input-modern[type="date"] {
    @apply cursor-pointer;
    position: relative;
    z-index: 1;
  }

  .input-modern[type="date"]::-webkit-calendar-picker-indicator {
    @apply cursor-pointer opacity-100;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    z-index: 2;
  }

  .input-modern[type="date"]::-webkit-inner-spin-button,
  .input-modern[type="date"]::-webkit-clear-button {
    display: none;
    -webkit-appearance: none;
  }

  /* Premium Aviation Card Styles */
  .card-modern {
    @apply bg-white rounded-2xl shadow-soft border border-neutral-100 p-6 transition-all duration-300;
  }

  .card-modern:hover {
    @apply shadow-aviation transform hover:-translate-y-1;
  }

  .card-elevated {
    @apply bg-white rounded-2xl shadow-aviation border border-neutral-100 p-8;
  }

  .card-aviation {
    @apply bg-white rounded-3xl shadow-aviation border border-brand-100 p-8 transition-all duration-300 hover:shadow-aviation-hover hover:-translate-y-2;
  }

  .card-premium {
    @apply bg-gradient-to-br from-white to-brand-50/30 rounded-3xl shadow-luxury border border-brand-100/50 p-8 backdrop-blur-sm;
  }

  /* Enhanced Typography Hierarchy for Better Readability */
  .heading-hero {
    @apply text-5xl md:text-6xl lg:text-7xl font-black text-neutral-900 leading-[1.1] tracking-tight;
  }

  .heading-primary {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold text-neutral-900 leading-tight tracking-tight;
  }

  .heading-secondary {
    @apply text-2xl md:text-3xl font-semibold text-neutral-800 leading-tight;
  }

  .heading-tertiary {
    @apply text-xl md:text-2xl font-semibold text-neutral-800 leading-tight;
  }

  .heading-quaternary {
    @apply text-lg md:text-xl font-semibold text-neutral-700 leading-tight;
  }

  .text-body {
    @apply text-base text-neutral-600 leading-relaxed font-normal;
  }

  .text-body-large {
    @apply text-lg text-neutral-600 leading-relaxed font-normal;
  }

  .text-body-small {
    @apply text-sm text-neutral-600 leading-relaxed font-normal;
  }

  /* Improved Gradient Text Effects - More Subtle */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-brand-700 via-brand-600 to-brand-500 bg-clip-text text-transparent;
  }

  .text-gradient-accent {
    @apply bg-gradient-to-r from-accent-700 via-accent-600 to-accent-500 bg-clip-text text-transparent;
  }

  .text-gradient-subtle {
    @apply bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent;
  }

  /* Enhanced Spacing Utilities */
  .section-spacing {
    @apply py-16 md:py-20 lg:py-24;
  }

  .content-spacing {
    @apply space-y-6 md:space-y-8;
  }

  /* Professional Sections */
  .section-padding {
    @apply py-16 md:py-24;
  }

  .container-modern {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Modern CTA Styles */
.cta-box {
  @apply bg-gradient-to-br from-brand-50 to-accent-50 border-2 border-brand-100 shadow-soft rounded-2xl;
}

.cta-button {
  @apply btn-primary transform hover:scale-105;
}

/* Modern Flight Search Enhancements */
.flight-inputs-container {
  @apply w-full;
}

.airport-input-enhanced {
  @apply relative;
}

.airport-tooltip {
  @apply absolute bottom-full left-1/2 transform -translate-x-1/2 bg-neutral-900 text-white px-3 py-2 rounded-lg text-xs whitespace-nowrap z-50 opacity-0 pointer-events-none transition-opacity duration-200 mb-2;
}

.airport-tooltip::after {
  @apply absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-neutral-900;
  content: '';
}

.airport-input-enhanced:hover .airport-tooltip {
  @apply opacity-100;
}

/* Modern Responsive Enhancements */
@layer utilities {
  .airport-input-wide {
    @apply md:min-w-[250px] lg:min-w-[280px] xl:min-w-[320px];
  }

  .airport-input-mobile {
    @apply min-h-[48px] text-base; /* Prevents zoom on iOS */
  }

  /* Professional hover effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-medium;
  }

  /* Focus states */
  .focus-modern {
    @apply focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2;
  }

  /* Touch targets for mobile accessibility */
  .touch-target {
    min-width: 44px;
    min-height: 44px;
  }

  /* Extra small screen optimizations */
  @media (min-width: 475px) {
    .xs\:inline {
      display: inline;
    }
    .xs\:hidden {
      display: none;
    }
  }

  /* Premium Aviation Design Elements */
  .aviation-gradient {
    background: linear-gradient(135deg, #0ea5e9 0%, #eab308 100%);
    background-size: 200% 200%;
  }

  .aviation-gradient-hero {
    background: linear-gradient(135deg, #f0f9ff 0%, #fefce8 50%, #e0f2fe 100%);
  }

  .premium-card {
    @apply bg-white rounded-3xl shadow-luxury border border-brand-100/50 backdrop-blur-sm;
  }

  .premium-button {
    @apply bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white font-semibold px-8 py-4 rounded-2xl transition-all duration-300 shadow-aviation hover:shadow-aviation-hover transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2;
  }

  .premium-button-large {
    @apply premium-button text-lg px-12 py-5 rounded-3xl;
  }

  /* Enhanced Trust Badge System - More Variety */
  .trust-badge {
    @apply inline-flex items-center px-4 py-2 bg-brand-50 text-brand-700 rounded-full text-sm font-medium border border-brand-200 shadow-brand-glow transition-all duration-300 hover:bg-brand-100;
  }

  .trust-badge-gold {
    @apply inline-flex items-center px-4 py-2 bg-accent-50 text-accent-700 rounded-full text-sm font-medium border border-accent-200 shadow-accent-glow transition-all duration-300 hover:bg-accent-100;
  }

  .trust-badge-green {
    @apply inline-flex items-center px-4 py-2 bg-green-50 text-green-700 rounded-full text-sm font-medium border border-green-200 shadow-success-glow transition-all duration-300 hover:bg-green-100;
  }

  .trust-badge-purple {
    @apply inline-flex items-center px-4 py-2 bg-purple-50 text-purple-700 rounded-full text-sm font-medium border border-purple-200 transition-all duration-300 hover:bg-purple-100;
  }

  .urgency-indicator {
    @apply inline-flex items-center px-3 py-1 bg-red-50 text-red-700 rounded-full text-xs font-medium border border-red-200 animate-pulse-glow;
  }

  /* Enhanced Form Styling */
  .form-section {
    @apply bg-white rounded-3xl shadow-aviation border border-brand-100/50 p-8 md:p-12 relative overflow-hidden;
  }

  .form-input-enhanced {
    @apply w-full px-4 py-3 border-2 border-neutral-200 rounded-xl focus:border-brand-500 focus:ring-2 focus:ring-brand-500/20 transition-all duration-300 text-base font-medium placeholder-neutral-400;
  }

  .form-label-enhanced {
    @apply block text-sm font-semibold text-neutral-700 mb-2;
  }

  .form-helper-text {
    @apply text-xs text-neutral-500 mt-1;
  }

  .price-highlight {
    @apply text-4xl md:text-5xl font-bold bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent;
  }

  .aviation-hero-text {
    @apply text-5xl md:text-7xl font-black bg-gradient-to-r from-brand-700 via-brand-600 to-brand-500 bg-clip-text text-transparent leading-tight;
  }

  .shimmer-effect {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  /* Premium Animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 5px rgba(249, 115, 22, 0.5); }
    50% { box-shadow: 0 0 20px rgba(249, 115, 22, 0.8), 0 0 30px rgba(249, 115, 22, 0.4); }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Premium Shadow Effects */
  .shadow-luxury {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-glow {
    box-shadow: 0 10px 25px -5px rgba(30, 41, 59, 0.3), 0 10px 10px -5px rgba(30, 41, 59, 0.04);
  }

  .shadow-success-glow {
    box-shadow: 0 10px 25px -5px rgba(249, 115, 22, 0.3), 0 10px 10px -5px rgba(249, 115, 22, 0.04);
  }

  /* Premium Aviation Animation Utilities */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px -10px rgba(14, 165, 233, 0.2);
  }

  .hover-aviation {
    transition: all 0.3s ease;
  }

  .hover-aviation:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 16px 48px -8px rgba(14, 165, 233, 0.3);
  }

  .aviation-shimmer {
    background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.4), rgba(234, 179, 8, 0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 3s infinite;
  }

  .animate-aviation-slide {
    animation: aviation-slide 3s ease-in-out infinite;
  }

  .animate-aviation-glow {
    animation: aviation-glow 3s ease-in-out infinite;
  }

  /* Enhanced Mobile Optimizations */
  @media (max-width: 768px) {
    .heading-hero {
      font-size: 2.5rem;
      line-height: 1.1;
    }

    .heading-primary {
      font-size: 2rem;
      line-height: 1.2;
    }

    .heading-secondary {
      font-size: 1.5rem;
      line-height: 1.3;
    }

    .card-aviation {
      padding: 1.5rem;
    }

    .btn-cta-primary, .btn-aviation-primary {
      padding: 0.875rem 1.5rem;
      font-size: 1rem;
    }

    .btn-cta-large {
      padding: 1rem 2rem;
      font-size: 1.125rem;
    }

    .input-aviation {
      padding: 0.875rem 1rem;
      font-size: 1rem;
      min-height: 48px; /* Better touch targets */
    }

    .container-modern {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .trust-badge, .trust-badge-gold, .trust-badge-green, .trust-badge-purple {
      padding: 0.5rem 0.75rem;
      font-size: 0.75rem;
    }

    .form-section {
      padding: 1.5rem;
    }
  }

  @media (max-width: 640px) {
    .heading-hero {
      font-size: 2rem;
      line-height: 1.1;
    }

    .heading-primary {
      font-size: 1.75rem;
      line-height: 1.2;
    }

    .price-highlight {
      font-size: 2rem;
    }

    .card-aviation {
      padding: 1rem;
      border-radius: 1.5rem;
    }

    .shadow-aviation {
      box-shadow: 0 4px 16px -4px rgba(14, 165, 233, 0.2);
    }

    .btn-cta-primary, .btn-aviation-primary {
      padding: 0.75rem 1.25rem;
      font-size: 0.875rem;
    }

    .btn-cta-large {
      padding: 0.875rem 1.5rem;
      font-size: 1rem;
    }

    .trust-badge, .trust-badge-gold, .trust-badge-green, .trust-badge-purple {
      padding: 0.375rem 0.625rem;
      font-size: 0.6875rem;
    }

    /* Improved touch targets for mobile */
    .touch-target {
      min-width: 48px;
      min-height: 48px;
    }

    /* Better spacing on small screens */
    .section-spacing {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
  }

  /* Touch-friendly interactions */
  @media (hover: none) and (pointer: coarse) {
    .hover-aviation:hover {
      transform: none;
    }

    .btn-aviation-primary:active {
      transform: scale(0.95);
    }

    .card-aviation:active {
      transform: scale(0.98);
    }
  }

  /* High DPI displays */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .shadow-aviation {
      box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.25);
    }
  }

  /* Performance optimizations */
  @media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-bounce-subtle,
    .animate-pulse-glow,
    .animate-shimmer,
    .animate-aviation-slide,
    .animate-aviation-glow {
      animation: none;
    }

    .hover-aviation:hover,
    .hover-lift:hover {
      transform: none;
    }
  }

  /* Accessibility improvements */
  @media (prefers-contrast: high) {
    .shadow-aviation {
      box-shadow: 0 8px 32px -8px rgba(0, 0, 0, 0.4);
    }

    .text-brand-600 {
      color: #0369a1;
    }

    .text-accent-600 {
      color: #ca8a04;
    }
  }

  /* Print styles */
  @media print {
    .shadow-aviation,
    .shadow-aviation-hover,
    .shadow-luxury {
      box-shadow: none;
    }

    .bg-gradient-to-r,
    .bg-gradient-to-br,
    .aviation-gradient-hero {
      background: white !important;
    }

    .text-white {
      color: black !important;
    }
  }

  /* Accessibility styles */
  .skip-links {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
  }

  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    transition: top 0.3s;
  }

  .skip-link:focus {
    top: 6px;
  }

  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Focus styles for keyboard navigation */
  .keyboard-navigation *:focus {
    outline: 2px solid #0ea5e9;
    outline-offset: 2px;
  }

  /* Tooltip styles */
  .tooltip {
    position: absolute;
    background: #1f2937;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 1000;
    max-width: 200px;
    word-wrap: break-word;
  }

  /* £20,000+ Premium Design System Enhancements */

  /* Advanced Glassmorphism Effects */
  .glass-luxury {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-aviation {
    background: rgba(14, 165, 233, 0.1);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(14, 165, 233, 0.2);
    box-shadow: 0 20px 40px -10px rgba(14, 165, 233, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-accent {
    background: rgba(234, 179, 8, 0.1);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(234, 179, 8, 0.2);
    box-shadow: 0 20px 40px -10px rgba(234, 179, 8, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  /* Luxury Typography Hierarchy */
  .text-luxury-display {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 900;
    line-height: 0.9;
    letter-spacing: -0.02em;
  }

  .text-luxury-display-stable {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 900;
    line-height: 0.9;
    letter-spacing: -0.02em;
    color: #1e293b;
  }

  .text-luxury-hero {
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 800;
    line-height: 1.1;
    letter-spacing: -0.01em;
    background: linear-gradient(135deg, #1e293b 0%, #0ea5e9 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-luxury-title {
    font-size: clamp(1.875rem, 4vw, 3rem);
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.01em;
    color: #1e293b;
  }

  .text-luxury-subtitle {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
    font-weight: 600;
    line-height: 1.4;
    color: #475569;
  }

  /* Premium Shadow System */
  .shadow-luxury-soft {
    box-shadow: 0 4px 16px -4px rgba(14, 165, 233, 0.1),
                0 2px 8px -2px rgba(14, 165, 233, 0.05);
  }

  .shadow-luxury-medium {
    box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.2),
                0 4px 16px -4px rgba(14, 165, 233, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-luxury-strong {
    box-shadow: 0 16px 64px -16px rgba(14, 165, 233, 0.3),
                0 8px 32px -8px rgba(14, 165, 233, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .shadow-luxury-glow {
    box-shadow: 0 0 40px rgba(14, 165, 233, 0.4),
                0 16px 64px -16px rgba(14, 165, 233, 0.3),
                0 8px 32px -8px rgba(14, 165, 233, 0.2);
  }

  /* Advanced Gradient System */
  .gradient-luxury-primary {
    background: linear-gradient(135deg,
      #0ea5e9 0%,
      #0284c7 25%,
      #0369a1 50%,
      #075985 75%,
      #0c4a6e 100%);
  }

  .gradient-luxury-accent {
    background: linear-gradient(135deg,
      #eab308 0%,
      #d97706 25%,
      #dc2626 50%,
      #b91c1c 75%,
      #991b1b 100%);
  }

  .gradient-luxury-hero {
    background: linear-gradient(135deg,
      rgba(14, 165, 233, 0.1) 0%,
      rgba(59, 130, 246, 0.05) 25%,
      rgba(147, 197, 253, 0.1) 50%,
      rgba(234, 179, 8, 0.05) 75%,
      rgba(251, 191, 36, 0.1) 100%);
  }

  /* Premium Animation Keyframes */
  @keyframes luxury-float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
      opacity: 0.8;
    }
    25% {
      transform: translateY(-10px) rotate(1deg);
      opacity: 0.9;
    }
    50% {
      transform: translateY(-20px) rotate(0deg);
      opacity: 1;
    }
    75% {
      transform: translateY(-10px) rotate(-1deg);
      opacity: 0.9;
    }
  }

  @keyframes luxury-pulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 40px rgba(14, 165, 233, 0.6);
    }
  }

  @keyframes luxury-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes luxury-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(14, 165, 233, 0.3),
                  0 0 40px rgba(14, 165, 233, 0.1);
    }
    50% {
      box-shadow: 0 0 40px rgba(14, 165, 233, 0.6),
                  0 0 80px rgba(14, 165, 233, 0.3);
    }
  }

  /* Professional Text Effects */
  @keyframes subtle-glow {
    0%, 100% {
      text-shadow: 0 2px 4px rgba(14, 165, 233, 0.1);
    }
    50% {
      text-shadow: 0 2px 8px rgba(14, 165, 233, 0.2);
    }
  }

  .text-professional-glow {
    animation: subtle-glow 4s ease-in-out infinite;
  }

  /* Premium Animation Classes */
  .animate-luxury-float {
    animation: luxury-float 8s ease-in-out infinite;
  }

  .animate-luxury-pulse {
    animation: luxury-pulse 3s ease-in-out infinite;
  }

  .animate-luxury-shimmer {
    position: relative;
    overflow: hidden;
  }

  .animate-luxury-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
    animation: luxury-shimmer 6s infinite;
    pointer-events: none;
  }

  .animate-luxury-glow {
    animation: luxury-glow 4s ease-in-out infinite;
  }

  /* £20,000+ Premium Component Styles */

  /* Luxury Card System */
  .card-luxury {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-luxury:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 32px 64px -12px rgba(14, 165, 233, 0.3),
                0 16px 32px -8px rgba(14, 165, 233, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .card-luxury-aviation {
    background: linear-gradient(135deg,
      rgba(14, 165, 233, 0.1) 0%,
      rgba(255, 255, 255, 0.95) 100%);
    border: 1px solid rgba(14, 165, 233, 0.2);
  }

  .card-luxury-accent {
    background: linear-gradient(135deg,
      rgba(234, 179, 8, 0.1) 0%,
      rgba(255, 255, 255, 0.95) 100%);
    border: 1px solid rgba(234, 179, 8, 0.2);
  }

  /* Premium Button System */
  .btn-luxury-primary {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    color: white;
    font-weight: 700;
    padding: 16px 32px;
    border-radius: 16px;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
  }

  .btn-luxury-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent);
    transition: left 0.6s ease;
  }

  .btn-luxury-primary:hover::before {
    left: 100%;
  }

  .btn-luxury-primary:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 16px 48px -8px rgba(14, 165, 233, 0.5),
                0 8px 24px -4px rgba(14, 165, 233, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  .btn-luxury-primary:active {
    transform: translateY(0) scale(1.02);
  }

  .btn-luxury-secondary {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    color: #0ea5e9;
    font-weight: 600;
    padding: 16px 32px;
    border-radius: 16px;
    border: 1px solid rgba(14, 165, 233, 0.2);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px -4px rgba(14, 165, 233, 0.2);
  }

  .btn-luxury-secondary:hover {
    background: rgba(14, 165, 233, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.3);
  }

  /* Premium Input System */
  .input-luxury {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(14, 165, 233, 0.2);
    border-radius: 16px;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 500;
    color: #1e293b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px -4px rgba(14, 165, 233, 0.1);
  }

  .input-luxury:focus {
    outline: none;
    border-color: #0ea5e9;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.3),
                0 0 0 3px rgba(14, 165, 233, 0.1);
    transform: translateY(-2px);
  }

  .input-luxury::placeholder {
    color: #94a3b8;
    font-weight: 400;
  }

  /* Premium Navigation */
  .nav-luxury {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(14, 165, 233, 0.1);
    box-shadow: 0 4px 16px -4px rgba(14, 165, 233, 0.1);
  }

  .nav-luxury-item {
    color: #475569;
    font-weight: 600;
    padding: 12px 20px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }

  .nav-luxury-item:hover {
    color: #0ea5e9;
    background: rgba(14, 165, 233, 0.1);
    transform: translateY(-1px);
  }

  .nav-luxury-item.active {
    color: #0ea5e9;
    background: rgba(14, 165, 233, 0.15);
  }

  .nav-luxury-item.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 2px;
    background: linear-gradient(90deg, #0ea5e9, #eab308);
    border-radius: 1px;
  }
}
