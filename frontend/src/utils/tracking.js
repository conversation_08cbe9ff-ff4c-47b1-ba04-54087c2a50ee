// Tracking utility for Meta Pixel and Google Ads events

// Meta Pixel tracking functions
export const trackFacebookEvent = (eventName, parameters = {}) => {
  if (typeof window !== 'undefined' && window.fbq) {
    window.fbq('track', eventName, parameters);
  }
};

// Google Ads tracking functions
export const trackGoogleEvent = (eventName, parameters = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, parameters);
  }
};

// Combined tracking function
export const trackEvent = (eventName, parameters = {}) => {
  trackFacebookEvent(eventName, parameters);
  trackGoogleEvent(eventName, parameters);
};

// Specific event tracking functions
export const trackPageView = (pageName) => {
  trackEvent('PageView', {
    page_title: pageName,
    page_location: window.location.href
  });
};

export const trackBlogView = (blogTitle, blogSlug) => {
  trackEvent('ViewContent', {
    content_type: 'blog_post',
    content_name: blogTitle,
    content_ids: [blogSlug],
    value: 0,
    currency: 'USD'
  });
};

export const trackSearchFlights = (searchParams) => {
  trackEvent('Search', {
    search_term: `${searchParams.from} to ${searchParams.to}`,
    content_category: 'flight_search',
    value: 0,
    currency: 'USD'
  });
};

export const trackInitiateCheckout = (flightDetails) => {
  trackEvent('InitiateCheckout', {
    content_type: 'product',
    content_name: 'Dummy Flight Ticket',
    content_ids: ['dummy_ticket'],
    value: 4.99,
    currency: 'USD',
    num_items: 1
  });
};

export const trackPurchase = (orderDetails) => {
  trackEvent('Purchase', {
    content_type: 'product',
    content_name: 'Dummy Flight Ticket',
    content_ids: ['dummy_ticket'],
    value: 4.99,
    currency: 'USD',
    transaction_id: orderDetails.transactionId || Date.now().toString()
  });
};

export const trackCTAClick = (ctaLocation, ctaText) => {
  trackEvent('Lead', {
    content_category: 'cta_click',
    content_name: ctaText,
    source: ctaLocation,
    value: 0,
    currency: 'USD'
  });
};

export const trackBlogCTAClick = (blogTitle) => {
  trackCTAClick('blog_post', `Blog CTA - ${blogTitle}`);
};

// Initialize tracking on app load
export const initializeTracking = () => {
  // Track initial page view
  trackPageView(document.title);
  
  // Set up automatic link tracking for internal links
  document.addEventListener('click', (event) => {
    const link = event.target.closest('a');
    if (link && link.href && link.href.includes(window.location.origin)) {
      const linkText = link.textContent || link.innerText || 'Unknown Link';
      trackEvent('ClickInternalLink', {
        link_text: linkText,
        link_url: link.href,
        source_page: window.location.pathname
      });
    }
  });
};
