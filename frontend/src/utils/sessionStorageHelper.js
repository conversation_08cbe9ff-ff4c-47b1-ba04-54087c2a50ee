/**
 * Session Storage Helper Utility
 * Provides safe methods for storing and retrieving booking data from sessionStorage
 */

const STORAGE_KEYS = {
  CHECKOUT_DATA: 'checkoutBookingData',
  FLIGHT_SEARCH: 'flightSearchData',
  PASSENGER_DATA: 'passengerData'
};

/**
 * Safely save data to sessionStorage
 * @param {string} key - Storage key
 * @param {any} data - Data to store
 * @returns {boolean} - Success status
 */
export const saveToSessionStorage = (key, data) => {
  try {
    const serializedData = JSON.stringify(data);
    sessionStorage.setItem(key, serializedData);
    console.log(`✅ SessionStorage: Saved data to ${key}`);
    return true;
  } catch (error) {
    console.error(`❌ SessionStorage: Failed to save data to ${key}:`, error);
    return false;
  }
};

/**
 * Safely retrieve data from sessionStorage
 * @param {string} key - Storage key
 * @returns {any|null} - Retrieved data or null if not found/error
 */
export const getFromSessionStorage = (key) => {
  try {
    const serializedData = sessionStorage.getItem(key);
    if (serializedData === null) {
      console.log(`⚠️ SessionStorage: No data found for ${key}`);
      return null;
    }
    const data = JSON.parse(serializedData);
    console.log(`✅ SessionStorage: Retrieved data from ${key}`);
    return data;
  } catch (error) {
    console.error(`❌ SessionStorage: Failed to retrieve data from ${key}:`, error);
    return null;
  }
};

/**
 * Remove data from sessionStorage
 * @param {string} key - Storage key
 * @returns {boolean} - Success status
 */
export const removeFromSessionStorage = (key) => {
  try {
    sessionStorage.removeItem(key);
    console.log(`✅ SessionStorage: Removed data from ${key}`);
    return true;
  } catch (error) {
    console.error(`❌ SessionStorage: Failed to remove data from ${key}:`, error);
    return false;
  }
};

/**
 * Clear all booking-related data from sessionStorage
 */
export const clearBookingData = () => {
  try {
    Object.values(STORAGE_KEYS).forEach(key => {
      sessionStorage.removeItem(key);
    });
    console.log('✅ SessionStorage: Cleared all booking data');
    return true;
  } catch (error) {
    console.error('❌ SessionStorage: Failed to clear booking data:', error);
    return false;
  }
};

/**
 * Save checkout booking data
 * @param {Object} bookingData - Complete booking data object
 */
export const saveCheckoutData = (bookingData) => {
  return saveToSessionStorage(STORAGE_KEYS.CHECKOUT_DATA, bookingData);
};

/**
 * Get checkout booking data
 * @returns {Object|null} - Booking data or null
 */
export const getCheckoutData = () => {
  return getFromSessionStorage(STORAGE_KEYS.CHECKOUT_DATA);
};

/**
 * Save flight search data
 * @param {Object} searchData - Flight search parameters
 */
export const saveFlightSearchData = (searchData) => {
  return saveToSessionStorage(STORAGE_KEYS.FLIGHT_SEARCH, searchData);
};

/**
 * Get flight search data
 * @returns {Object|null} - Search data or null
 */
export const getFlightSearchData = () => {
  return getFromSessionStorage(STORAGE_KEYS.FLIGHT_SEARCH);
};

/**
 * Save passenger data
 * @param {Object} passengerData - Passenger information
 */
export const savePassengerData = (passengerData) => {
  return saveToSessionStorage(STORAGE_KEYS.PASSENGER_DATA, passengerData);
};

/**
 * Get passenger data
 * @returns {Object|null} - Passenger data or null
 */
export const getPassengerData = () => {
  return getFromSessionStorage(STORAGE_KEYS.PASSENGER_DATA);
};

/**
 * Check if sessionStorage is available
 * @returns {boolean} - Availability status
 */
export const isSessionStorageAvailable = () => {
  try {
    const testKey = '__sessionStorageTest__';
    sessionStorage.setItem(testKey, 'test');
    sessionStorage.removeItem(testKey);
    return true;
  } catch (error) {
    console.warn('⚠️ SessionStorage: Not available:', error);
    return false;
  }
};

export { STORAGE_KEYS };
