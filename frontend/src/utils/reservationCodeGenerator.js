/**
 * Reservation Code Generator Utility (Frontend)
 * Generates short, authentic-looking airline reservation codes
 */

/**
 * Generate a short reservation code (6-7 characters)
 * Format: XXXXXX or XXXXXXX (uppercase letters and numbers)
 * Examples: NHG8IQ, A1B2C3, K9M4P7, etc.
 */
export function generateReservationCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  const length = Math.random() < 0.5 ? 6 : 7; // Randomly choose 6 or 7 characters
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Generate an airline-specific reservation code
 * Uses airline code as prefix for more authenticity
 */
export function generateAirlineReservationCode(airlineCode = null) {
  if (airlineCode && airlineCode.length >= 2) {
    // Use airline code as first 2 characters, then add 4-5 random chars
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const remainingLength = Math.random() < 0.5 ? 4 : 5;
    let result = airlineCode.substring(0, 2).toUpperCase();
    
    for (let i = 0; i < remainingLength; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }
  
  // Fallback to regular reservation code
  return generateReservationCode();
}

/**
 * Generate both reservation codes for a ticket
 * Returns an object with both codes
 */
export function generateReservationCodes(airlineCode = null) {
  const reservationCode = generateReservationCode();
  const airlineReservationCode = generateAirlineReservationCode(airlineCode);
  
  return {
    reservationCode,
    airlineReservationCode
  };
}
