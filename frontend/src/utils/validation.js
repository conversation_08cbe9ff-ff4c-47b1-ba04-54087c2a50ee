// Validation utilities for the booking flow

export const validateEmail = (email) => {
  if (!email) {
    return 'Email is required';
  }
  if (!/\S+@\S+\.\S+/.test(email)) {
    return 'Please enter a valid email address';
  }
  return null;
};

export const validateRequired = (value, fieldName) => {
  if (!value || (typeof value === 'string' && !value.trim())) {
    return `${fieldName} is required`;
  }
  return null;
};

export const validateDate = (date, fieldName) => {
  if (!date) {
    return `${fieldName} is required`;
  }
  
  const today = new Date().toISOString().split('T')[0];
  if (date < today) {
    return `${fieldName} cannot be in the past`;
  }
  
  return null;
};

export const validateReturnDate = (departureDate, returnDate) => {
  if (!returnDate) {
    return 'Return date is required';
  }
  
  if (!departureDate) {
    return 'Please select departure date first';
  }
  
  if (returnDate <= departureDate) {
    return 'Return date must be after departure date';
  }
  
  const today = new Date().toISOString().split('T')[0];
  if (returnDate < today) {
    return 'Return date cannot be in the past';
  }
  
  return null;
};

export const validateAirportSelection = (origin, destination) => {
  const errors = {};

  // Validate origin airport
  if (!origin) {
    errors.origin = 'Please select an origin airport from the dropdown';
  } else if (typeof origin !== 'string' || origin.length !== 3 || !/^[A-Z]{3}$/.test(origin)) {
    errors.origin = 'Please select a valid airport from the dropdown (not just type text)';
  }

  // Validate destination airport
  if (!destination) {
    errors.destination = 'Please select a destination airport from the dropdown';
  } else if (typeof destination !== 'string' || destination.length !== 3 || !/^[A-Z]{3}$/.test(destination)) {
    errors.destination = 'Please select a valid airport from the dropdown (not just type text)';
  }

  // Validate that origin and destination are different
  if (origin && destination && origin === destination) {
    errors.destination = 'Destination must be different from origin';
  }

  return errors;
};

export const validateFlightSelection = (tripType, selectedFlight, selectedOutboundFlight, selectedReturnFlight) => {
  if (tripType === 'return') {
    if (!selectedOutboundFlight && !selectedReturnFlight) {
      return 'Please select both outbound and return flights';
    }
    if (!selectedOutboundFlight) {
      return 'Please select an outbound flight';
    }
    if (!selectedReturnFlight) {
      return 'Please select a return flight';
    }
  } else {
    if (!selectedFlight) {
      return 'Please select a flight';
    }
  }
  
  return null;
};

export const validatePassenger = (passenger, index) => {
  const errors = {};
  const passengerNumber = index + 1;
  
  if (!passenger.firstName || !passenger.firstName.trim()) {
    errors[`passenger_${passenger.id}_firstName`] = `Passenger ${passengerNumber} first name is required`;
  }
  
  if (!passenger.lastName || !passenger.lastName.trim()) {
    errors[`passenger_${passenger.id}_lastName`] = `Passenger ${passengerNumber} last name is required`;
  }
  
  // Additional name validation
  if (passenger.firstName && passenger.firstName.trim().length < 2) {
    errors[`passenger_${passenger.id}_firstName`] = `Passenger ${passengerNumber} first name must be at least 2 characters`;
  }
  
  if (passenger.lastName && passenger.lastName.trim().length < 2) {
    errors[`passenger_${passenger.id}_lastName`] = `Passenger ${passengerNumber} last name must be at least 2 characters`;
  }
  
  return errors;
};

export const validateBookingData = (searchData, selectedFlight, selectedOutboundFlight, selectedReturnFlight, passengers, email) => {
  const errors = {};
  
  // Validate search data
  if (!searchData) {
    errors.general = 'Missing search data. Please start a new search.';
    return errors;
  }
  
  // Validate flight selection
  const flightError = validateFlightSelection(
    searchData.tripType, 
    selectedFlight, 
    selectedOutboundFlight, 
    selectedReturnFlight
  );
  if (flightError) {
    errors.flights = flightError;
  }
  
  // Validate passengers
  if (!passengers || passengers.length === 0) {
    errors.passengers = 'At least one passenger is required';
  } else {
    passengers.forEach((passenger, index) => {
      const passengerErrors = validatePassenger(passenger, index);
      Object.assign(errors, passengerErrors);
    });
  }
  
  // Validate email
  const emailError = validateEmail(email);
  if (emailError) {
    errors.email = emailError;
  }
  
  return errors;
};

// Helper function to check if there are any validation errors
export const hasValidationErrors = (errors) => {
  return Object.keys(errors).some(key => errors[key]);
};

// Helper function to get first error message
export const getFirstError = (errors) => {
  const errorKeys = Object.keys(errors);
  if (errorKeys.length === 0) return null;
  return errors[errorKeys[0]];
};
