/**
 * Flight Ticket Helper Utility
 * Converts flight booking data to FlightTicket component format
 */

/**
 * Format time for display in tickets
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted time (HH:MM)
 */
export const formatTicketTime = (dateString) => {
  if (!dateString) return '--:--';
  try {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  } catch (error) {
    console.warn('Invalid date string for ticket time:', dateString);
    return '--:--';
  }
};

/**
 * Format date for ticket display
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date (WEEKDAY DD MMM)
 */
export const formatTicketDate = (dateString) => {
  if (!dateString) {
    return new Date().toLocaleDateString('en-GB', {
      weekday: 'long',
      day: '2-digit',
      month: 'short'
    }).toUpperCase();
  }
  
  try {
    return new Date(dateString).toLocaleDateString('en-GB', {
      weekday: 'long',
      day: '2-digit',
      month: 'short'
    }).toUpperCase();
  } catch (error) {
    console.warn('Invalid date string for ticket date:', dateString);
    return new Date().toLocaleDateString('en-GB', {
      weekday: 'long',
      day: '2-digit',
      month: 'short'
    }).toUpperCase();
  }
};

/**
 * Format trip dates for ticket header
 * @param {string} departureDate - ISO date string
 * @param {string} returnDate - ISO date string (optional)
 * @returns {string} - Formatted trip dates
 */
export const formatTripDates = (departureDate, returnDate = null) => {
  const formatDate = (dateString) => {
    if (!dateString) return new Date().toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    }).replace(/\//g, ' ').toUpperCase();
    
    try {
      return new Date(dateString).toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      }).replace(/\//g, ' ').toUpperCase();
    } catch (error) {
      return new Date().toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      }).replace(/\//g, ' ').toUpperCase();
    }
  };

  const departure = formatDate(departureDate);
  if (returnDate) {
    const returnFormatted = formatDate(returnDate);
    return `${departure}  ${returnFormatted}`;
  }
  return departure;
};

/**
 * Format passenger name for ticket display
 * @param {Object} passenger - Passenger object with firstName and lastName
 * @returns {string} - Formatted passenger name (LASTNAME/FIRSTNAME)
 */
export const formatPassengerName = (passenger) => {
  if (!passenger) return 'PASSENGER/NAME';

  const lastName = (passenger.lastName || 'PASSENGER').toUpperCase();
  const firstName = (passenger.firstName || 'NAME').toUpperCase();

  return `${lastName}/${firstName}`;
};

/**
 * Calculate flight duration (placeholder - would need actual calculation)
 * @param {string} departureTime - ISO date string
 * @param {string} arrivalTime - ISO date string
 * @returns {string} - Formatted duration
 */
export const calculateDuration = (departureTime, arrivalTime) => {
  if (!departureTime || !arrivalTime) return '08hr(s) 30min(s)';
  
  try {
    const departure = new Date(departureTime);
    const arrival = new Date(arrivalTime);
    const diffMs = arrival - departure;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${diffHours.toString().padStart(2, '0')}hr(s) ${diffMinutes.toString().padStart(2, '0')}min(s)`;
  } catch (error) {
    console.warn('Error calculating duration:', error);
    return '08hr(s) 30min(s)';
  }
};

/**
 * Convert booking data to FlightTicket component format
 * @param {Object} bookingData - Booking data object
 * @returns {Object} - FlightTicket component props
 */
export const convertBookingToTicketFormat = (bookingData) => {
  const {
    selectedFlight,
    selectedOutboundFlight,
    selectedReturnFlight,
    passengers = [],
    bookingReference = 'ABC123',
    tripType = 'oneWay'
  } = bookingData;

  // Determine primary flight
  const primaryFlight = selectedFlight || selectedOutboundFlight;
  
  if (!primaryFlight) {
    console.warn('No flight data available for ticket generation');
    return createFallbackTicketData(passengers, bookingReference);
  }

  // Create segments array
  const segments = [];
  
  // Add outbound flight
  if (primaryFlight) {
    segments.push(createFlightSegment(primaryFlight, 'outbound'));
  }
  
  // Add return flight if exists
  if (selectedReturnFlight && tripType === 'return') {
    segments.push(createFlightSegment(selectedReturnFlight, 'return'));
  }

  // Determine trip dates
  const departureDate = primaryFlight?.flight?.departure?.time;
  const returnDate = selectedReturnFlight?.flight?.departure?.time;
  
  return {
    tripDates: formatTripDates(departureDate, returnDate),
    destination: tripType === 'return' ? 'ROUND TRIP RESERVATION' : 'ONE-WAY FLIGHT RESERVATION',
    passengers: passengers.map(formatPassengerName),
    reservationCode: bookingReference,
    airlineReservationCode: bookingReference,
    segments
  };
};

/**
 * Create a flight segment for the ticket
 * @param {Object} flight - Flight data
 * @param {string} type - 'outbound' or 'return'
 * @returns {Object} - Flight segment object
 */
const createFlightSegment = (flight, type = 'outbound') => {
  const flightInfo = flight?.flight || {};
  const airline = flight?.airline || {};
  const departure = flightInfo.departure || {};
  const arrival = flightInfo.arrival || {};

  return {
    departureDay: formatTicketDate(departure.time),
    airline: airline.name?.toUpperCase() || 'VERIFIEDONWARD AIRLINES',
    flightNo: flightInfo.number || flight.flightNumber || 'VO 123',
    duration: calculateDuration(departure.time, arrival.time),
    flightClass: 'Economy Class (M)',
    status: 'Confirmed',
    from: {
      code: departure.airport || 'JFK',
      city: departure.city || 'New York, United States',
      time: formatTicketTime(departure.time),
      terminal: departure.terminal || '1'
    },
    to: {
      code: arrival.airport || 'LHR',
      city: arrival.city || 'London, United Kingdom',
      time: formatTicketTime(arrival.time),
      terminal: arrival.terminal || '5'
    },
    aircraft: flight.aircraft || 'BOEING 737-800',
    stops: '0',
    meals: 'Available',
    distance: '3,459'
  };
};

/**
 * Create fallback ticket data when no flight data is available
 * @param {Array} passengers - Passenger array
 * @param {string} bookingReference - Booking reference
 * @returns {Object} - Fallback ticket data
 */
const createFallbackTicketData = (passengers, bookingReference) => {
  return {
    tripDates: formatTripDates(),
    destination: 'EMBASSY VISA APPLICATION FLIGHT',
    passengers: passengers.length > 0 ? passengers.map(formatPassengerName) : [{ name: 'PASSENGER/NAME' }],
    reservationCode: bookingReference,
    airlineReservationCode: bookingReference,
    segments: [{
      departureDay: formatTicketDate(),
      airline: 'VERIFIEDONWARD AIRLINES',
      flightNo: 'VO 123',
      duration: '08hr(s) 30min(s)',
      flightClass: 'Economy Class (M)',
      status: 'Confirmed',
      from: {
        code: 'JFK',
        city: 'New York, United States',
        time: '20:30',
        terminal: '7'
      },
      to: {
        code: 'LHR',
        city: 'London, United Kingdom',
        time: '07:00',
        terminal: '5'
      },
      aircraft: 'BOEING 737-800',
      stops: '0',
      meals: 'Available',
      distance: '3,459'
    }]
  };
};
