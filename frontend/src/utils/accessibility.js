// Accessibility utilities for VerifiedOnward

/**
 * Announce content to screen readers
 * @param {string} message - Message to announce
 * @param {string} priority - 'polite' or 'assertive'
 */
export const announceToScreenReader = (message, priority = 'polite') => {
  const announcer = document.createElement('div');
  announcer.setAttribute('aria-live', priority);
  announcer.setAttribute('aria-atomic', 'true');
  announcer.className = 'sr-only';
  announcer.textContent = message;
  
  document.body.appendChild(announcer);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcer);
  }, 1000);
};

/**
 * Manage focus for modal dialogs and overlays
 */
export class FocusManager {
  constructor(element) {
    this.element = element;
    this.focusableElements = this.getFocusableElements();
    this.firstFocusableElement = this.focusableElements[0];
    this.lastFocusableElement = this.focusableElements[this.focusableElements.length - 1];
    this.previouslyFocusedElement = document.activeElement;
  }

  getFocusableElements() {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'textarea:not([disabled])',
      'input[type="text"]:not([disabled])',
      'input[type="radio"]:not([disabled])',
      'input[type="checkbox"]:not([disabled])',
      'select:not([disabled])',
      '[tabindex]:not([tabindex="-1"])'
    ];

    return this.element.querySelectorAll(focusableSelectors.join(', '));
  }

  trapFocus(event) {
    if (event.key !== 'Tab') return;

    if (event.shiftKey) {
      if (document.activeElement === this.firstFocusableElement) {
        this.lastFocusableElement.focus();
        event.preventDefault();
      }
    } else {
      if (document.activeElement === this.lastFocusableElement) {
        this.firstFocusableElement.focus();
        event.preventDefault();
      }
    }
  }

  activate() {
    this.element.addEventListener('keydown', this.trapFocus.bind(this));
    this.firstFocusableElement?.focus();
  }

  deactivate() {
    this.element.removeEventListener('keydown', this.trapFocus.bind(this));
    this.previouslyFocusedElement?.focus();
  }
}

/**
 * Check if element is visible to screen readers
 * @param {Element} element - Element to check
 * @returns {boolean} True if visible to screen readers
 */
export const isVisibleToScreenReader = (element) => {
  const style = window.getComputedStyle(element);
  return !(
    style.display === 'none' ||
    style.visibility === 'hidden' ||
    style.opacity === '0' ||
    element.hasAttribute('aria-hidden') ||
    element.hasAttribute('hidden')
  );
};

/**
 * Generate unique IDs for form elements
 * @param {string} prefix - Prefix for the ID
 * @returns {string} Unique ID
 */
export const generateUniqueId = (prefix = 'element') => {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Add skip links for keyboard navigation
 */
export const addSkipLinks = () => {
  const skipLinks = document.createElement('div');
  skipLinks.className = 'skip-links';
  skipLinks.innerHTML = `
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <a href="#navigation" class="skip-link">Skip to navigation</a>
  `;
  
  document.body.insertBefore(skipLinks, document.body.firstChild);
};

/**
 * Validate form accessibility
 * @param {Element} form - Form element to validate
 * @returns {Array} Array of accessibility issues
 */
export const validateFormAccessibility = (form) => {
  const issues = [];
  const inputs = form.querySelectorAll('input, textarea, select');
  
  inputs.forEach(input => {
    // Check for labels
    const label = form.querySelector(`label[for="${input.id}"]`) || 
                  input.closest('label');
    
    if (!label && !input.getAttribute('aria-label') && !input.getAttribute('aria-labelledby')) {
      issues.push(`Input ${input.name || input.type} is missing a label`);
    }
    
    // Check for required field indicators
    if (input.hasAttribute('required') && !input.getAttribute('aria-required')) {
      input.setAttribute('aria-required', 'true');
    }
    
    // Check for error messages
    if (input.getAttribute('aria-invalid') === 'true') {
      const errorId = input.getAttribute('aria-describedby');
      if (!errorId || !document.getElementById(errorId)) {
        issues.push(`Input ${input.name || input.type} has aria-invalid but no error message`);
      }
    }
  });
  
  return issues;
};

/**
 * Enhance button accessibility
 * @param {Element} button - Button element to enhance
 * @param {Object} options - Enhancement options
 */
export const enhanceButtonAccessibility = (button, options = {}) => {
  // Add role if not present
  if (!button.getAttribute('role') && button.tagName !== 'BUTTON') {
    button.setAttribute('role', 'button');
  }
  
  // Add keyboard support for non-button elements
  if (button.tagName !== 'BUTTON' && !button.hasAttribute('tabindex')) {
    button.setAttribute('tabindex', '0');
    
    button.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        button.click();
      }
    });
  }
  
  // Add loading state support
  if (options.hasLoadingState) {
    const originalText = button.textContent;
    
    button.setLoadingState = (isLoading) => {
      if (isLoading) {
        button.setAttribute('aria-busy', 'true');
        button.disabled = true;
        button.textContent = options.loadingText || 'Loading...';
      } else {
        button.removeAttribute('aria-busy');
        button.disabled = false;
        button.textContent = originalText;
      }
    };
  }
};

/**
 * Create accessible tooltips
 * @param {Element} trigger - Element that triggers the tooltip
 * @param {string} content - Tooltip content
 * @param {Object} options - Tooltip options
 */
export const createAccessibleTooltip = (trigger, content, options = {}) => {
  const tooltipId = generateUniqueId('tooltip');
  
  const tooltip = document.createElement('div');
  tooltip.id = tooltipId;
  tooltip.className = 'tooltip';
  tooltip.setAttribute('role', 'tooltip');
  tooltip.textContent = content;
  tooltip.style.display = 'none';
  
  document.body.appendChild(tooltip);
  
  trigger.setAttribute('aria-describedby', tooltipId);
  
  const showTooltip = () => {
    tooltip.style.display = 'block';
    announceToScreenReader(content, 'polite');
  };
  
  const hideTooltip = () => {
    tooltip.style.display = 'none';
  };
  
  trigger.addEventListener('mouseenter', showTooltip);
  trigger.addEventListener('mouseleave', hideTooltip);
  trigger.addEventListener('focus', showTooltip);
  trigger.addEventListener('blur', hideTooltip);
  
  // Cleanup function
  return () => {
    document.body.removeChild(tooltip);
    trigger.removeAttribute('aria-describedby');
    trigger.removeEventListener('mouseenter', showTooltip);
    trigger.removeEventListener('mouseleave', hideTooltip);
    trigger.removeEventListener('focus', showTooltip);
    trigger.removeEventListener('blur', hideTooltip);
  };
};

/**
 * Advanced error handling with accessibility
 */
export class AccessibleErrorHandler {
  constructor() {
    this.errorContainer = null;
    this.init();
  }

  init() {
    // Create error announcement container
    this.errorContainer = document.createElement('div');
    this.errorContainer.setAttribute('aria-live', 'assertive');
    this.errorContainer.setAttribute('aria-atomic', 'true');
    this.errorContainer.className = 'sr-only';
    document.body.appendChild(this.errorContainer);
  }

  handleError(error, context = '') {
    const errorMessage = `Error ${context}: ${error.message}`;

    // Announce to screen readers
    this.announceError(errorMessage);

    // Log for debugging
    console.error('Accessible Error:', error, context);

    // Show visual error if needed
    this.showVisualError(errorMessage);
  }

  announceError(message) {
    this.errorContainer.textContent = message;

    // Clear after announcement
    setTimeout(() => {
      this.errorContainer.textContent = '';
    }, 1000);
  }

  showVisualError(message) {
    // Create visual error notification
    const errorNotification = document.createElement('div');
    errorNotification.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50';
    errorNotification.setAttribute('role', 'alert');
    errorNotification.textContent = message;

    document.body.appendChild(errorNotification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (errorNotification.parentNode) {
        errorNotification.parentNode.removeChild(errorNotification);
      }
    }, 5000);
  }
}

/**
 * Premium touch interactions for mobile
 */
export class TouchInteractionManager {
  constructor() {
    this.touchStartTime = 0;
    this.touchStartPos = { x: 0, y: 0 };
    this.init();
  }

  init() {
    // Add touch feedback to interactive elements
    this.addTouchFeedback();

    // Handle touch gestures
    this.setupGestureHandling();

    // Optimize for touch devices
    this.optimizeForTouch();
  }

  addTouchFeedback() {
    document.addEventListener('touchstart', (e) => {
      const target = e.target.closest('button, [role="button"], a, [tabindex]');
      if (target) {
        target.classList.add('touch-active');

        // Add haptic feedback if available
        if (navigator.vibrate) {
          navigator.vibrate(10);
        }
      }
    });

    document.addEventListener('touchend', (e) => {
      const target = e.target.closest('button, [role="button"], a, [tabindex]');
      if (target) {
        setTimeout(() => {
          target.classList.remove('touch-active');
        }, 150);
      }
    });
  }

  setupGestureHandling() {
    document.addEventListener('touchstart', (e) => {
      this.touchStartTime = Date.now();
      this.touchStartPos = {
        x: e.touches[0].clientX,
        y: e.touches[0].clientY
      };
    });

    document.addEventListener('touchend', (e) => {
      const touchEndTime = Date.now();
      const touchDuration = touchEndTime - this.touchStartTime;

      // Detect long press (500ms+)
      if (touchDuration > 500) {
        this.handleLongPress(e);
      }
    });
  }

  handleLongPress(e) {
    const target = e.target.closest('[data-long-press]');
    if (target) {
      const event = new CustomEvent('longpress', {
        detail: { target, originalEvent: e }
      });
      target.dispatchEvent(event);
    }
  }

  optimizeForTouch() {
    // Ensure minimum touch target size
    const interactiveElements = document.querySelectorAll('button, [role="button"], a, input, select, textarea');

    interactiveElements.forEach(element => {
      const rect = element.getBoundingClientRect();
      if (rect.width < 44 || rect.height < 44) {
        element.style.minWidth = '44px';
        element.style.minHeight = '44px';
      }
    });
  }
}

/**
 * Advanced keyboard navigation
 */
export class KeyboardNavigationManager {
  constructor() {
    this.focusableElements = [];
    this.currentFocusIndex = -1;
    this.init();
  }

  init() {
    this.updateFocusableElements();
    this.setupKeyboardShortcuts();
    this.enhanceTabNavigation();
  }

  updateFocusableElements() {
    const selector = [
      'a[href]',
      'button:not([disabled])',
      'textarea:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[role="button"]:not([disabled])',
      '[role="link"]:not([disabled])'
    ].join(', ');

    this.focusableElements = Array.from(document.querySelectorAll(selector))
      .filter(el => this.isVisible(el));
  }

  isVisible(element) {
    const style = window.getComputedStyle(element);
    return style.display !== 'none' &&
           style.visibility !== 'hidden' &&
           style.opacity !== '0';
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Alt + H: Go to homepage
      if (e.altKey && e.key === 'h') {
        e.preventDefault();
        window.location.href = '/';
      }

      // Alt + S: Focus search
      if (e.altKey && e.key === 's') {
        e.preventDefault();
        const searchInput = document.querySelector('[data-search-input]');
        if (searchInput) {
          searchInput.focus();
        }
      }

      // Escape: Close modals/overlays
      if (e.key === 'Escape') {
        this.closeModals();
      }
    });
  }

  enhanceTabNavigation() {
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        this.updateFocusableElements();

        // Announce current focus for screen readers
        setTimeout(() => {
          const focused = document.activeElement;
          if (focused && focused.getAttribute('aria-label')) {
            announceToScreenReader(`Focused: ${focused.getAttribute('aria-label')}`);
          }
        }, 100);
      }
    });
  }

  closeModals() {
    const modals = document.querySelectorAll('[role="dialog"], .modal, .overlay');
    modals.forEach(modal => {
      if (modal.style.display !== 'none') {
        const closeButton = modal.querySelector('[data-close], .close, [aria-label*="close"]');
        if (closeButton) {
          closeButton.click();
        }
      }
    });
  }
}

/**
 * Initialize accessibility features
 */
export const initAccessibilityFeatures = () => {
  // Add skip links
  addSkipLinks();

  // Enhance all buttons
  document.querySelectorAll('button, [role="button"]').forEach(button => {
    enhanceButtonAccessibility(button);
  });

  // Initialize advanced accessibility managers
  const errorHandler = new AccessibleErrorHandler();
  const touchManager = new TouchInteractionManager();
  const keyboardManager = new KeyboardNavigationManager();

  // Add focus visible polyfill for older browsers
  if (!CSS.supports('selector(:focus-visible)')) {
    document.addEventListener('keydown', () => {
      document.body.classList.add('keyboard-navigation');
    });

    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });
  }

  // Monitor for accessibility violations in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Advanced accessibility monitoring enabled');

    // Check for common accessibility issues
    setTimeout(() => {
      validatePageAccessibility();
    }, 1000);
  }

  return { errorHandler, touchManager, keyboardManager };
};

/**
 * Validate page accessibility
 */
export const validatePageAccessibility = () => {
  const issues = [];

  // Check for images without alt text
  const images = document.querySelectorAll('img:not([alt])');
  if (images.length > 0) {
    issues.push(`${images.length} images missing alt text`);
  }

  // Check for buttons without accessible names
  const buttons = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
  const buttonsWithoutText = Array.from(buttons).filter(btn => !btn.textContent.trim());
  if (buttonsWithoutText.length > 0) {
    issues.push(`${buttonsWithoutText.length} buttons without accessible names`);
  }

  // Check for form inputs without labels
  const inputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
  const inputsWithoutLabels = Array.from(inputs).filter(input => {
    const label = document.querySelector(`label[for="${input.id}"]`);
    return !label && !input.closest('label');
  });
  if (inputsWithoutLabels.length > 0) {
    issues.push(`${inputsWithoutLabels.length} form inputs without labels`);
  }

  // Check color contrast (basic check)
  const lowContrastElements = document.querySelectorAll('[style*="color"]');
  // This would need a more sophisticated contrast checking algorithm

  if (issues.length > 0) {
    console.warn('Accessibility issues found:', issues);
  } else {
    console.log('✅ No major accessibility issues detected');
  }

  return issues;
};
