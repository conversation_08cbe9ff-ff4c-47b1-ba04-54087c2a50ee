import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { flightAPI } from '../services/api';
import { searchAirports as searchMockAirports } from '../data/mockAirports';

// Country to flag emoji mapping
const getCountryFlag = (country) => {
  const flagMap = {
    // Major countries
    'United States': '🇺🇸',
    'United Kingdom': '🇬🇧',
    'Canada': '🇨🇦',
    'France': '🇫🇷',
    'Germany': '🇩🇪',
    'Spain': '🇪🇸',
    'Italy': '🇮🇹',
    'Netherlands': '🇳🇱',
    'Belgium': '🇧🇪',
    'Switzerland': '🇨🇭',
    'Austria': '🇦🇹',
    'Portugal': '🇵🇹',
    'Greece': '🇬🇷',
    'Turkey': '🇹🇷',
    'Poland': '🇵🇱',
    'Czech Republic': '🇨🇿',
    'Hungary': '🇭🇺',
    'Croatia': '🇭🇷',
    'Slovenia': '🇸🇮',
    'Slovakia': '🇸🇰',
    'Romania': '🇷🇴',
    'Bulgaria': '🇧🇬',
    'Ireland': '🇮🇪',
    'Denmark': '🇩🇰',
    'Sweden': '🇸🇪',
    'Norway': '🇳🇴',
    'Finland': '🇫🇮',
    'Iceland': '🇮🇸',
    'Luxembourg': '🇱🇺',
    'Malta': '🇲🇹',

    // Asia
    'Japan': '🇯🇵',
    'China': '🇨🇳',
    'South Korea': '🇰🇷',
    'India': '🇮🇳',
    'Thailand': '🇹🇭',
    'Singapore': '🇸🇬',
    'Malaysia': '🇲🇾',
    'Indonesia': '🇮🇩',
    'Philippines': '🇵🇭',
    'Vietnam': '🇻🇳',
    'Taiwan': '🇹🇼',
    'Hong Kong': '🇭🇰',
    'Macau': '🇲🇴',
    'Mongolia': '🇲🇳',
    'Kazakhstan': '🇰🇿',
    'Uzbekistan': '🇺🇿',
    'Kyrgyzstan': '🇰🇬',
    'Tajikistan': '🇹🇯',
    'Turkmenistan': '🇹🇲',
    'Bangladesh': '🇧🇩',
    'Pakistan': '🇵🇰',
    'Sri Lanka': '🇱🇰',
    'Nepal': '🇳🇵',
    'Bhutan': '🇧🇹',
    'Maldives': '🇲🇻',
    'Myanmar': '🇲🇲',
    'Cambodia': '🇰🇭',
    'Laos': '🇱🇦',
    'Brunei': '🇧🇳',
    'Timor-Leste': '🇹🇱',

    // Middle East
    'United Arab Emirates': '🇦🇪',
    'Saudi Arabia': '🇸🇦',
    'Qatar': '🇶🇦',
    'Kuwait': '🇰🇼',
    'Bahrain': '🇧🇭',
    'Oman': '🇴🇲',
    'Iran': '🇮🇷',
    'Iraq': '🇮🇶',
    'Israel': '🇮🇱',
    'Jordan': '🇯🇴',
    'Lebanon': '🇱🇧',
    'Syria': '🇸🇾',
    'Cyprus': '🇨🇾',
    'Georgia': '🇬🇪',
    'Armenia': '🇦🇲',
    'Azerbaijan': '🇦🇿',
    'Afghanistan': '🇦🇫',
    'Yemen': '🇾🇪',

    // Africa
    'Egypt': '🇪🇬',
    'South Africa': '🇿🇦',
    'Morocco': '🇲🇦',
    'Algeria': '🇩🇿',
    'Tunisia': '🇹🇳',
    'Libya': '🇱🇾',
    'Sudan': '🇸🇩',
    'Ethiopia': '🇪🇹',
    'Kenya': '🇰🇪',
    'Nigeria': '🇳🇬',
    'Ghana': '🇬🇭',
    'Tanzania': '🇹🇿',
    'Uganda': '🇺🇬',
    'Rwanda': '🇷🇼',
    'Zimbabwe': '🇿🇼',
    'Zambia': '🇿🇲',
    'Botswana': '🇧🇼',
    'Namibia': '🇳🇦',
    'Angola': '🇦🇴',
    'Cameroon': '🇨🇲',
    'Senegal': '🇸🇳',
    'Ivory Coast': '🇨🇮',
    'Mali': '🇲🇱',
    'Burkina Faso': '🇧🇫',
    'Niger': '🇳🇪',
    'Chad': '🇹🇩',
    'Central African Republic': '🇨🇫',
    'Democratic Republic of the Congo': '🇨🇩',
    'Republic of the Congo': '🇨🇬',
    'Gabon': '🇬🇦',
    'Equatorial Guinea': '🇬🇶',
    'São Tomé and Príncipe': '🇸🇹',
    'Cape Verde': '🇨🇻',
    'Guinea': '🇬🇳',
    'Guinea-Bissau': '🇬🇼',
    'Sierra Leone': '🇸🇱',
    'Liberia': '🇱🇷',
    'Togo': '🇹🇬',
    'Benin': '🇧🇯',
    'The Gambia': '🇬🇲',
    'Mauritania': '🇲🇷',
    'Mauritius': '🇲🇺',
    'Seychelles': '🇸🇨',
    'Comoros': '🇰🇲',
    'Madagascar': '🇲🇬',
    'Réunion': '🇷🇪',
    'Mayotte': '🇾🇹',
    'Djibouti': '🇩🇯',
    'Eritrea': '🇪🇷',
    'Somalia': '🇸🇴',
    'South Sudan': '🇸🇸',
    'Malawi': '🇲🇼',
    'Mozambique': '🇲🇿',
    'Eswatini': '🇸🇿',
    'Lesotho': '🇱🇸',
    'Burundi': '🇧🇮',

    // Americas
    'Mexico': '🇲🇽',
    'Brazil': '🇧🇷',
    'Argentina': '🇦🇷',
    'Chile': '🇨🇱',
    'Colombia': '🇨🇴',
    'Peru': '🇵🇪',
    'Venezuela': '🇻🇪',
    'Ecuador': '🇪🇨',
    'Bolivia': '🇧🇴',
    'Uruguay': '🇺🇾',
    'Paraguay': '🇵🇾',
    'Guyana': '🇬🇾',
    'Suriname': '🇸🇷',
    'French Guiana': '🇬🇫',
    'Costa Rica': '🇨🇷',
    'Panama': '🇵🇦',
    'Nicaragua': '🇳🇮',
    'Honduras': '🇭🇳',
    'El Salvador': '🇸🇻',
    'Guatemala': '🇬🇹',
    'Belize': '🇧🇿',
    'Cuba': '🇨🇺',
    'Jamaica': '🇯🇲',
    'Haiti': '🇭🇹',
    'Dominican Republic': '🇩🇴',
    'Puerto Rico': '🇵🇷',
    'Trinidad and Tobago': '🇹🇹',
    'Barbados': '🇧🇧',
    'The Bahamas': '🇧🇸',
    'Bermuda': '🇧🇲',
    'Greenland': '🇬🇱',

    // Oceania
    'Australia': '🇦🇺',
    'New Zealand': '🇳🇿',
    'Fiji': '🇫🇯',
    'Papua New Guinea': '🇵🇬',
    'Samoa': '🇼🇸',
    'Tonga': '🇹🇴',
    'Vanuatu': '🇻🇺',
    'Solomon Islands': '🇸🇧',
    'Palau': '🇵🇼',
    'Marshall Islands': '🇲🇭',
    'Federated States of Micronesia': '🇫🇲',
    'Kiribati': '🇰🇮',
    'Nauru': '🇳🇷',
    'Tuvalu': '🇹🇻',
    'Cook Islands': '🇨🇰',
    'French Polynesia': '🇵🇫',
    'New Caledonia': '🇳🇨',
    'Guam': '🇬🇺',
    'American Samoa': '🇦🇸',
    'Northern Mariana Islands': '🇲🇵',
    'Christmas Island': '🇨🇽',
    'Cocos (Keeling) Islands': '🇨🇨',
    'Norfolk Island': '🇳🇫',
    'Niue': '🇳🇺',
    'Wallis and Futuna': '🇼🇫',

    // Special territories and regions
    'Gibraltar': '🇬🇮',
    'Isle of Man': '🇮🇲',
    'Jersey': '🇯🇪',
    'Guernsey': '🇬🇬',
    'Faroe Islands': '🇫🇴',
    'Åland': '🇦🇽',
    'Saint Helena': '🇸🇭',
    'Ascension Island': '🇦🇨',
    'Falkland Islands': '🇫🇰',
    'Easter Island': '🇨🇱', // Part of Chile
    'Martinique': '🇲🇶',
    'Guadeloupe': '🇬🇵',
    'Saint Barthélemy': '🇧🇱',
    'Saint Pierre and Miquelon': '🇵🇲',
    'Aruba': '🇦🇼',
    'Curaçao': '🇨🇼',
    'Sint Maarten': '🇸🇽',
    'Caribbean Netherlands': '🇧🇶',
    'British Virgin Islands': '🇻🇬',
    'U.S. Virgin Islands': '🇻🇮',
    'Cayman Islands': '🇰🇾',
    'Turks and Caicos Islands': '🇹🇨',
    'Anguilla': '🇦🇮',
    'Montserrat': '🇲🇸',
    'Saint Kitts and Nevis': '🇰🇳',
    'Antigua and Barbuda': '🇦🇬',
    'Dominica': '🇩🇲',
    'Saint Lucia': '🇱🇨',
    'Saint Vincent and the Grenadines': '🇻🇨',
    'Grenada': '🇬🇩'
  };

  return flagMap[country] || '🌍'; // Default globe emoji if country not found
};

const AirportAutocomplete = ({ value, onChange, placeholder, hasError = false }) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAirport, setSelectedAirport] = useState(null);
  const inputRef = useRef(null);
  const debounceRef = useRef(null);

  // Update query when value changes (for controlled component)
  useEffect(() => {
    if (value && selectedAirport && selectedAirport.iataCode === value) {
      setQuery(`${selectedAirport.name} (${selectedAirport.iataCode}), ${selectedAirport.country}`);
    } else if (!value) {
      setQuery('');
      setSelectedAirport(null);
    }
  }, [value, selectedAirport]);

  // Search function using API with mock data fallback
  const searchAirports = async (searchQuery) => {
    if (searchQuery.length < 2) {
      setSuggestions([]);
      return;
    }

    setIsLoading(true);
    try {
      // Try to use the backend API first
      const response = await flightAPI.searchAirports(searchQuery);

      if (response.success) {
        setSuggestions(response.data);
      }
    } catch (error) {
      console.warn('Backend API unavailable, using mock data:', error.message);
      // Fallback to mock data when backend is unavailable
      const mockResults = searchMockAirports(searchQuery, 15);
      setSuggestions(mockResults);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input change with debouncing
  const handleInputChange = (e) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    setIsOpen(true);

    // Clear previous debounce
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // Set new debounce
    debounceRef.current = setTimeout(() => {
      searchAirports(newQuery);
    }, 300);
  };

  // Handle airport selection
  const handleSelect = (airport) => {
    setSelectedAirport(airport);
    // Use the new one-line format for display
    setQuery(`${airport.name} (${airport.iataCode}), ${airport.country}`);
    setIsOpen(false);
    setSuggestions([]);

    // Call onChange with only the IATA code
    onChange(airport.iataCode);
  };

  // Handle input focus
  const handleFocus = () => {
    setIsOpen(true);
    if (query.length >= 2) {
      searchAirports(query);
    }
  };

  // Handle input blur
  const handleBlur = () => {
    // Delay closing to allow for selection
    setTimeout(() => {
      setIsOpen(false);
    }, 200);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
      inputRef.current?.blur();
    }
  };

  // Dynamic font size based on content length
  const getDynamicFontSize = (text) => {
    if (!text) return 'text-sm';
    if (text.length > 40) return 'text-xs';
    if (text.length > 30) return 'text-sm';
    return 'text-sm';
  };

  // Check if text is too long for comfortable display (for tooltip only)
  const isTextLong = (text) => {
    return text && text.length > 30;
  };

  // Clear field functionality
  const handleClearField = (e) => {
    e.stopPropagation();
    setQuery('');
    setSelectedAirport(null);
    setSuggestions([]);
    onChange('');
    inputRef.current?.focus();
  };

  return (
    <div className="relative airport-input-enhanced">
      <div className="relative group">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`
            input-aviation pl-12 pr-16
            ${getDynamicFontSize(query)}
            overflow-hidden text-ellipsis whitespace-nowrap
            airport-input-wide airport-input-mobile
            min-h-[52px] md:min-h-[48px]
            text-base md:text-sm
            ${query ? 'font-semibold' : 'font-medium'}
            ${hasError ? 'border-red-500 focus:ring-red-500' : ''}
            touch-manipulation
          `}
          autoComplete="off"
          title={query || placeholder} // Show full text on hover
          style={{
            minWidth: '0', // Allow shrinking
            textOverflow: 'ellipsis' // Add ellipsis for overflow
          }}
        />

        {/* Enhanced tooltip for long airport names */}
        {isTextLong(query) && (
          <div className="airport-tooltip">
            {query}
          </div>
        )}

        {/* Premium Aviation icon */}
        <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
          <svg className="h-5 w-5 text-brand-400" fill="currentColor" viewBox="0 0 24 24">
            <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
          </svg>
        </div>

        {/* Right side icons container */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 space-x-1">
          {/* Clear button */}
          {query && (
            <button
              type="button"
              onClick={handleClearField}
              className="p-1 hover:bg-neutral-100 rounded-full transition-colors duration-200 group-hover:opacity-100 opacity-70 touch-manipulation"
              title="Clear field"
              aria-label="Clear airport selection"
            >
              <svg className="h-4 w-4 text-neutral-400 hover:text-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}

          {/* Loading spinner or search icon */}
          {isLoading ? (
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-brand-500"></div>
          ) : (
            <svg className="h-5 w-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          )}
        </div>
      </div>

      {/* Suggestions dropdown */}
      <AnimatePresence>
        {isOpen && suggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute z-50 w-full mt-2 bg-white border-2 border-brand-200 rounded-2xl shadow-aviation max-h-64 md:max-h-60 overflow-y-auto"
          >
            {suggestions.map((airport, index) => (
              <motion.div
                key={airport.iataCode}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: index * 0.05 }}
                onClick={() => handleSelect(airport)}
                className="px-4 py-3 hover:bg-brand-50 cursor-pointer border-b border-neutral-100 last:border-b-0 transition-colors duration-200"
                title={`${airport.name} (${airport.iataCode}) - ${airport.city}, ${airport.country}`}
              >
                <div className="flex items-center justify-between gap-3">
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:gap-2">
                      {/* Airport name and code */}
                      <div className="font-semibold text-neutral-900 truncate">
                        {airport.name} ({airport.iataCode})
                      </div>
                      {/* Country - separate line on mobile, same line on desktop */}
                      <div className="text-sm text-neutral-600 truncate sm:flex-shrink-0">
                        {airport.country}
                      </div>
                    </div>
                    {/* City info if available and different from airport name */}
                    {airport.city && airport.city !== airport.name && (
                      <div className="text-xs text-neutral-500 mt-1 truncate">
                        {airport.city}
                      </div>
                    )}
                  </div>
                  <div
                    className="text-xl flex-shrink-0"
                    title={airport.country}
                  >
                    {getCountryFlag(airport.country)}
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* No results message */}
      <AnimatePresence>
        {isOpen && !isLoading && query.length >= 2 && suggestions.length === 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg p-4 text-center text-gray-500"
          >
            No airports found. Try a different search term.
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AirportAutocomplete;
