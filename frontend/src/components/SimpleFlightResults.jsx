import React, { useState, useEffect } from 'react';
import StickyFlightSidebar from './StickyFlightSidebar';

// BULLETPROOF FLIGHT RESULTS COMPONENT
const SimpleFlightResults = (props) => {
  console.log('🚨 SimpleFlightResults COMPONENT RENDERED!', props);

  const {
    isLoading = false,
    searchError = null,
    tripType = 'oneWay',
    oneWayFlights = [],
    selectedOutboundFlight = null,
    onOneWayFlightSelect = () => {},
    onContinue = () => {},
    searchData = {},
    onModifySearch = () => {}
  } = props || {};

  const [departureSelected, setDepartureSelected] = useState(false);

  // ALWAYS RENDER SOMETHING - NEVER RETURN NULL
  return (
    <div className="p-8 bg-red-100 border-4 border-red-500 rounded-lg">
      <h1 className="text-2xl font-bold text-red-800 mb-4">🚨 SIMPLE FLIGHT RESULTS COMPONENT</h1>
      <div className="space-y-2 text-sm">
        <p><strong>Component Status:</strong> RENDERED SUCCESSFULLY</p>
        <p><strong>isLoading:</strong> {isLoading ? 'TRUE' : 'FALSE'}</p>
        <p><strong>searchError:</strong> {searchError || 'NONE'}</p>
        <p><strong>tripType:</strong> {tripType}</p>
        <p><strong>oneWayFlights:</strong> {oneWayFlights?.length || 0} flights</p>
        <p><strong>selectedOutboundFlight:</strong> {selectedOutboundFlight ? 'EXISTS' : 'NULL'}</p>
        <p><strong>searchData:</strong> {JSON.stringify(searchData)}</p>
      </div>

      {oneWayFlights && oneWayFlights.length > 0 && (
        <div className="mt-4 p-4 bg-green-100 border border-green-500 rounded">
          <h3 className="font-bold text-green-800">✅ FLIGHTS FOUND!</h3>
          <p>We have {oneWayFlights.length} flights to display</p>
          <div className="mt-2 space-y-2">
            {oneWayFlights.slice(0, 3).map((flight, index) => (
              <div key={index} className="p-2 bg-white rounded border">
                <p><strong>Flight {index + 1}:</strong> {flight?.airline?.name || 'Unknown Airline'}</p>
                <p><strong>ID:</strong> {flight?.id || 'No ID'}</p>
                <button
                  onClick={() => {
                    console.log('🔍 FLIGHT CLICKED:', flight);
                    onOneWayFlightSelect(flight);
                  }}
                  className="mt-1 px-3 py-1 bg-blue-500 text-white rounded text-sm"
                >
                  SELECT THIS FLIGHT
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {(!oneWayFlights || oneWayFlights.length === 0) && !isLoading && (
        <div className="mt-4 p-4 bg-yellow-100 border border-yellow-500 rounded">
          <h3 className="font-bold text-yellow-800">⚠️ NO FLIGHTS</h3>
          <p>oneWayFlights is empty or null</p>
        </div>
      )}

      {isLoading && (
        <div className="mt-4 p-4 bg-blue-100 border border-blue-500 rounded">
          <h3 className="font-bold text-blue-800">🔄 LOADING</h3>
          <p>Component is in loading state</p>
        </div>
      )}
    </div>
  );

  // Loading state
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-8 text-center">
        <div className="text-blue-600 text-lg mb-4">🔍 Searching flights...</div>
        <p className="text-gray-600">Please wait while we find the best flights for you.</p>
      </div>
    );
  }

  // Error state
  if (searchError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-8 text-center">
        <div className="text-6xl mb-4">⚠️</div>
        <h3 className="text-xl font-semibold text-red-800 mb-3">Flight Search Error</h3>
        <div className="text-red-600 text-base mb-6 max-w-md mx-auto">
          {searchError}
        </div>
        <button
          onClick={onModifySearch}
          className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors font-medium"
        >
          🔄 Try Different Search
        </button>
      </div>
    );
  }

  // No flights
  if (!oneWayFlights || oneWayFlights.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">✈️</div>
        <h3 className="text-xl font-semibold text-gray-800 mb-3">No flights found</h3>
        <p className="text-gray-600 mb-6">Try adjusting your search criteria</p>
        <button
          onClick={onModifySearch}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          🔄 Modify Search
        </button>
      </div>
    );
  }

  // Flight results
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">✈️ Available Flights</h2>
        <p className="text-gray-600">Found {oneWayFlights.length} flights</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Flight List */}
        <div className="lg:col-span-2 space-y-4">
          {oneWayFlights.slice(0, 8).map((flight, index) => {
            const isSelected = selectedOutboundFlight?.id === flight?.id;
            
            return (
              <div
                key={flight?.id || index}
                className={`bg-white border rounded-lg p-6 hover:shadow-md transition-all cursor-pointer ${
                  isSelected ? 'border-green-500 bg-green-50' : 'border-gray-200'
                }`}
                onClick={() => {
                  console.log('🔍 Flight clicked:', flight);
                  // Toggle selection: if same flight is clicked, unselect it
                  if (selectedOutboundFlight?.id === flight?.id) {
                    console.log('🔄 Unselecting flight:', flight.id);
                    onOneWayFlightSelect(null);
                  } else {
                    console.log('✅ Selecting flight:', flight.id);
                    onOneWayFlightSelect(flight);
                  }
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-semibold text-sm">
                        {flight?.airline?.name?.charAt(0) || '✈️'}
                      </span>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">
                        {flight?.airline?.name || 'Airline'}
                      </div>
                      <div className="text-sm text-gray-500">
                        {flight?.flight?.number || 'Flight'}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-green-600">$4.99</div>
                    <div className="text-sm text-gray-400 line-through">
                      ${flight?.price?.originalPrice || '119'}
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
                  <span>
                    {flight?.flight?.departure?.time ? 
                      new Date(flight.flight.departure.time).toLocaleTimeString('en-US', {hour: '2-digit', minute: '2-digit', hour12: false}) : 
                      '17:10'
                    } → {flight?.flight?.arrival?.time ? 
                      new Date(flight.flight.arrival.time).toLocaleTimeString('en-US', {hour: '2-digit', minute: '2-digit', hour12: false}) : 
                      '21:35'
                    }
                  </span>
                  <span>{flight?.flight?.duration || '3h 25m'}</span>
                  <span>{flight?.flight?.stops === 0 ? 'Direct' : `${flight?.flight?.stops} stop${flight?.flight?.stops > 1 ? 's' : ''}`}</span>
                </div>

                {isSelected && (
                  <div className="mt-3 p-2 bg-green-100 rounded text-sm text-green-800 text-center">
                    ✅ Selected
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-8">
            <StickyFlightSidebar
              tripType={tripType}
              selectedOutboundFlight={selectedOutboundFlight}
              selectedReturnFlight={null}
              searchData={searchData}
              onEditOutbound={() => {}}
              onEditReturn={() => {}}
              onContinue={onContinue}
              departureSelected={departureSelected}
              returnSelected={false}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleFlightResults;
