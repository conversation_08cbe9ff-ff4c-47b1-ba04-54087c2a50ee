import React, { useState } from 'react';
import AirportAutocomplete from './AirportAutocomplete';

const AirportInputDemo = () => {
  const [origin, setOrigin] = useState('');
  const [destination, setDestination] = useState('');

  // Test data with long airport names
  const testAirports = [
    'Manchester Airport (MAN), United Kingdom',
    'Frankfurt am Main Airport (FRA), Germany', 
    'London Heathrow Airport (LHR), United Kingdom',
    'Charles de Gaulle Airport (CDG), France',
    'Amsterdam Airport Schiphol (AMS), Netherlands',
    'Dubai International Airport (DXB), United Arab Emirates',
    'Singapore Changi Airport (SIN), Singapore',
    'Tokyo Haneda Airport (HND), Japan'
  ];

  const handleTestFill = (airport) => {
    setOrigin(airport);
  };

  return (
    <div className="max-w-4xl mx-auto p-8 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-2xl shadow-xl p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          Airport Input Field Demo
        </h1>
        
        <div className="space-y-6">
          {/* Test Buttons */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-3">Test with Long Airport Names:</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {testAirports.map((airport, index) => (
                <button
                  key={index}
                  onClick={() => handleTestFill(airport)}
                  className="text-left p-2 bg-white rounded border hover:bg-blue-100 transition-colors text-sm"
                >
                  {airport}
                </button>
              ))}
            </div>
          </div>

          {/* Demo Form */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            <div className="min-w-0 xl:min-w-[280px]">
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                <span className="mr-2">✈️</span>
                From
              </label>
              <AirportAutocomplete
                value={origin}
                onChange={setOrigin}
                placeholder="Select origin airport"
              />
            </div>

            <div className="min-w-0 xl:min-w-[280px]">
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                <span className="mr-2">🛬</span>
                To
              </label>
              <AirportAutocomplete
                value={destination}
                onChange={setDestination}
                placeholder="Select destination airport"
              />
            </div>

            <div className="min-w-0 md:col-span-2 xl:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                <span className="mr-2">📅</span>
                Date
              </label>
              <input
                type="date"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm hover:border-blue-400 transition-all duration-200 focus:scale-[1.02] focus:shadow-md min-h-[44px]"
              />
            </div>
          </div>

          {/* Features List */}
          <div className="bg-green-50 p-6 rounded-lg">
            <h3 className="font-semibold text-green-900 mb-4">✅ Improvements Implemented:</h3>
            <ul className="space-y-2 text-green-800">
              <li>• <strong>Wider Input Fields:</strong> Increased width by ~20% on desktop (280px minimum on XL screens)</li>
              <li>• <strong>Dynamic Font Sizing:</strong> Smaller font for longer airport names (&gt;30 chars)</li>
              <li>• <strong>Tooltip on Hover:</strong> Full airport name shown when text is truncated</li>
              <li>• <strong>Clean Design:</strong> No warning indicators or distracting visual elements</li>
              <li>• <strong>Clear Button:</strong> Easy-to-access ✕ button to reset field</li>
              <li>• <strong>Airplane Icon:</strong> Visual guide inside the field</li>
              <li>• <strong>Mobile Optimized:</strong> 44px minimum height, proper touch targets</li>
              <li>• <strong>Responsive Layout:</strong> Stacks vertically on mobile, 2-column on tablet, 3-column on desktop</li>
              <li>• <strong>Enhanced Dropdown:</strong> Better layout for long airport names with country flags</li>
              <li>• <strong>Smooth Animations:</strong> Focus scaling and hover effects</li>
            </ul>
          </div>

          {/* Current Values Display */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-700 mb-2">Current Values:</h4>
            <div className="space-y-1 text-sm text-gray-600">
              <div><strong>Origin:</strong> {origin || 'Not selected'}</div>
              <div><strong>Destination:</strong> {destination || 'Not selected'}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AirportInputDemo;
