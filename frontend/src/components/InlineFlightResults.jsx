import React from 'react';
import { motion } from 'framer-motion';
import FlightCard from './FlightCard';
import LoadingSpinner from './LoadingSpinner';
import FlightSummaryWithPassengerForm from './FlightSummaryWithPassengerForm';
import FlightMiniSummaryCard from './FlightMiniSummaryCard';

const InlineFlightResults = ({
  isLoading,
  searchError,
  tripType,
  oneWayFlights,
  outboundFlights,
  returnFlights,
  selectedOutboundFlight,
  selectedReturnFlight,
  onOneWayFlightSelect,
  onOutboundFlightSelect,
  onReturnFlightSelect,
  onContinue,
  searchData,
  onModifySearch,
  showPassengerDetails = false,
  passengers = [{ id: 1, firstName: '', lastName: '' }],
  email = '',
  onPassengerDetailsSubmit,
  isPassengerDetailsLoading = false,
  onEditFlight,
  enableInlinePayment = false
}) => {
  // Edit handlers for flight selection
  const handleEditOutbound = () => {
    if (onOutboundFlightSelect) {
      onOutboundFlightSelect(null);
    }
  };

  const handleEditReturn = () => {
    if (onReturnFlightSelect) {
      onReturnFlightSelect(null);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="card-elevated text-center"
      >
        <LoadingSpinner />
        <p className="text-neutral-600 mt-4">Searching for flights...</p>
      </motion.div>
    );
  }

  // Error state
  if (searchError) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-red-50 border border-red-200 rounded-2xl p-8 text-center"
      >
        <div className="text-6xl mb-4">❌</div>
        <h3 className="heading-tertiary text-red-800 mb-3">
          Search Error
        </h3>
        <p className="text-body text-red-600 mb-6 max-w-md mx-auto">
          {searchError}
        </p>
        <button
          onClick={() => window.location.reload()}
          className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-200 shadow-soft"
        >
          Try Again
        </button>
      </motion.div>
    );
  }

  // One way flight results
  if (tripType === 'oneWay' && Array.isArray(oneWayFlights) && oneWayFlights.length > 0) {
    // If flight is selected, show only the summary (hide flight results)
    if (selectedOutboundFlight) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-8"
        >
          {/* Modify Search Button */}
          {onModifySearch && (
            <div className="text-center">
              <button
                onClick={onModifySearch}
                className="btn-secondary inline-flex items-center justify-center gap-2 text-sm"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Modify Search Criteria
              </button>
            </div>
          )}

          {/* Flight Summary + Passenger Details - Shows when flight selected */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="mt-8"
          >
            <FlightSummaryWithPassengerForm
              tripType={tripType}
              selectedOutboundFlight={selectedOutboundFlight}
              selectedReturnFlight={null}
              searchData={searchData}
              onEditOutbound={handleEditOutbound}
              onEditReturn={null}
              passengers={passengers}
              email={email}
              onPassengerDetailsSubmit={onPassengerDetailsSubmit}
              isPassengerDetailsLoading={isPassengerDetailsLoading}
              enableInlinePayment={enableInlinePayment}
            />
          </motion.div>
        </motion.div>
      );
    }

    // Show flight results when no flight is selected
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-8"
      >
        {/* Modify Search Button */}
        {onModifySearch && (
          <div className="text-center">
            <button
              onClick={onModifySearch}
              className="inline-flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 shadow-sm"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Modify Search Criteria
            </button>
          </div>
        )}

        {/* Flight Results */}
        <div className="space-y-4">
          {oneWayFlights.map((flight, index) => {
            if (!flight || !flight.id) {
              console.warn('⚠️ Invalid one-way flight data:', flight);
              return null;
            }

            return (
              <motion.div
                key={flight.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <FlightCard
                  flight={flight}
                  onSelect={() => onOneWayFlightSelect(flight)}
                  isSelected={false}
                />
              </motion.div>
            );
          })}
        </div>
      </motion.div>
    );
  }

  // Return flight results
  if (tripType === 'return' && (
    (Array.isArray(outboundFlights) && outboundFlights.length > 0) ||
    (Array.isArray(returnFlights) && returnFlights.length > 0)
  )) {
    // If both flights are selected, show only the summary (hide flight results)
    if (selectedOutboundFlight && selectedReturnFlight) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-8"
        >
          {/* Modify Search Button */}
          {onModifySearch && (
            <div className="text-center">
              <button
                onClick={onModifySearch}
                className="inline-flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 shadow-sm"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Modify Search Criteria
              </button>
            </div>
          )}

          {/* Flight Summary + Passenger Details - Shows after both flights selected */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="mt-8"
          >
            <FlightSummaryWithPassengerForm
              tripType={tripType}
              selectedOutboundFlight={selectedOutboundFlight}
              selectedReturnFlight={selectedReturnFlight}
              searchData={searchData}
              onEditOutbound={handleEditOutbound}
              onEditReturn={handleEditReturn}
              passengers={passengers}
              email={email}
              onPassengerDetailsSubmit={onPassengerDetailsSubmit}
              isPassengerDetailsLoading={isPassengerDetailsLoading}
              enableInlinePayment={enableInlinePayment}
            />
          </motion.div>
        </motion.div>
      );
    }

    // Show flight results when flights are not fully selected
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-8"
      >
        {/* Modify Search Button */}
        {onModifySearch && (
          <div className="text-center">
            <button
              onClick={onModifySearch}
              className="inline-flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 shadow-sm"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Modify Search Criteria
            </button>
          </div>
        )}

        {/* Main Layout */}
        <div className="relative">
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Outbound Flights Section - Hide if already selected */}
            {outboundFlights && outboundFlights.length > 0 && !selectedOutboundFlight && (
              <div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-6"
                >
                  <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center justify-center">
                      <span className="mr-3">🛫</span>
                      Select Departure Flight
                    </h2>
                    <p className="text-gray-600">
                      {searchData.origin} → {searchData.destination} on {searchData.date ? new Date(searchData.date).toLocaleDateString() : 'Date'}
                    </p>
                  </div>

                  <div className="space-y-4">
                    {outboundFlights.map((flight, index) => {
                      if (!flight || !flight.id) {
                        console.warn('⚠️ Invalid outbound flight data:', flight);
                        return null;
                      }

                      return (
                        <motion.div
                          key={flight.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <FlightCard
                            flight={flight}
                            onSelect={() => onOutboundFlightSelect(flight)}
                            isSelected={false}
                          />
                        </motion.div>
                      );
                    })}
                  </div>
                </motion.div>
              </div>
            )}

            {/* Show selected outbound flight summary */}
            {selectedOutboundFlight && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
                className="mt-8"
              >
                <FlightMiniSummaryCard
                  flight={selectedOutboundFlight}
                  title="Departure Flight"
                  date={searchData.date}
                  onEdit={handleEditOutbound}
                  className="mb-4"
                />
              </motion.div>
            )}

            {/* Return Flights Section - Show only if outbound is selected and return is not */}
            {returnFlights && returnFlights.length > 0 && selectedOutboundFlight && !selectedReturnFlight && (
              <div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-6"
                >
                  <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center justify-center">
                      <span className="mr-3">🛬</span>
                      Select Return Flight
                    </h2>
                    <p className="text-gray-600">
                      {searchData.destination} → {searchData.origin} on {searchData.returnDate ? new Date(searchData.returnDate).toLocaleDateString() : 'Return Date'}
                    </p>
                  </div>

                  <div className="space-y-4">
                    {returnFlights.map((flight, index) => {
                      if (!flight || !flight.id) {
                        console.warn('⚠️ Invalid return flight data:', flight);
                        return null;
                      }

                      return (
                        <motion.div
                          key={flight.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <FlightCard
                            flight={flight}
                            onSelect={() => onReturnFlightSelect(flight)}
                            isSelected={false}
                          />
                        </motion.div>
                      );
                    })}
                  </div>
                </motion.div>
              </div>
            )}
          </div>
        </div>
      </motion.div>
    );
  }

  // No results
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center py-20"
    >
      <div className="text-6xl mb-4">✈️</div>
      <h3 className="text-2xl font-semibold text-gray-900 mb-2">
        No flights found
      </h3>
      <p className="text-gray-600 mb-6 max-w-md mx-auto">
        We couldn't find any flights for your search. Please try different airports or dates.
      </p>
    </motion.div>
  );
};

export default InlineFlightResults;
