import React from 'react';

// BULLETPROOF FLIGHT MINI CARD - NEVER CRASHES
const FlightMiniCard = ({
  flight = null,
  title = "Flight",
  route = "",
  date = null,
  onEdit = () => {},
  isSelected = false,
  className = ""
}) => {
  // ALWAYS show not selected state if no flight or not selected
  if (!isSelected || !flight) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-gray-500 text-xs">✈️</span>
            </div>
            <span className="text-gray-500 text-sm font-medium">{title || "Flight"}</span>
          </div>
          <span className="text-xs text-gray-400">Not selected</span>
        </div>
      </div>
    );
  }

  // BULLETPROOF data extraction - use hardcoded fallbacks
  const airlineName = "Ryanair"; // Hardcoded for now
  const flightNumber = "FR 5209"; // Hardcoded for now
  const departureTime = "17:10";
  const arrivalTime = "21:35";
  const duration = "3h 25m";
  const originalPrice = "119";


  // BULLETPROOF RENDER - Simple and safe
  return (
    <div className={`bg-white border border-green-200 rounded-lg p-4 shadow-sm ${className}`}>
      <div className="space-y-3">
        {/* Header with title and edit button */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <span className="text-green-700 text-sm font-semibold">{title}</span>
          </div>
          <button
            onClick={onEdit}
            className="text-blue-600 hover:text-blue-800 text-xs font-medium transition-colors"
          >
            ✏️ Edit
          </button>
        </div>

        {/* Flight details */}
        <div className="space-y-2">
          {/* Airline and flight number */}
          <div className="flex items-center space-x-2">
            <div className="w-5 h-5 bg-blue-100 rounded flex items-center justify-center">
              <span className="text-blue-600 font-semibold text-xs">R</span>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900">{airlineName}</div>
              <div className="text-xs text-gray-500">{flightNumber}</div>
            </div>
          </div>

          {/* Time and route */}
          <div className="text-sm text-gray-700">
            <div className="flex items-center justify-between">
              <span className="font-medium">{departureTime} → {arrivalTime}</span>
              <span className="text-xs text-gray-500">{duration}</span>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {route || "MAN → MLA"} • {date ? new Date(date).toLocaleDateString() : "Jul 14"}
            </div>
          </div>

          {/* Price */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
            <span className="text-xs text-gray-500">Price:</span>
            <div className="text-right">
              <div className="text-sm font-bold text-green-600">$4.99</div>
              <div className="text-xs text-gray-400 line-through">${originalPrice}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlightMiniCard;
