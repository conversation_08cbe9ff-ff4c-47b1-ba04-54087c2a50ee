import React from 'react';
import { motion } from 'framer-motion';

const FlightSummaryCard = ({ 
  selectedFlight, 
  selectedOutboundFlight, 
  selectedReturnFlight, 
  searchData, 
  onEditFlight,
  tripType 
}) => {
  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDuration = (duration) => {
    // Duration is in ISO 8601 format (PT6H15M)
    const match = duration.match(/PT(\d+H)?(\d+M)?/);
    if (!match) return duration;
    
    const hours = match[1] ? match[1].replace('H', 'h ') : '';
    const minutes = match[2] ? match[2].replace('M', 'm') : '';
    
    return `${hours}${minutes}`.trim();
  };

  const getStopsText = (stops) => {
    if (stops === 0) return 'Direct';
    if (stops === 1) return '1 Stop';
    return `${stops} Stops`;
  };

  const renderFlightDetails = (flight, title, route, date) => {
    // Safety checks for flight data
    if (!flight || typeof flight !== 'object') {
      console.warn('FlightSummaryCard: Invalid flight data', flight);
      return null;
    }

    const airline = flight.airline || {};
    const flightInfo = flight.flight || {};
    const departure = flight.departure || {};
    const arrival = flight.arrival || {};

    return (
      <div className="mb-6 last:mb-0">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-semibold text-gray-700 uppercase tracking-wide">
            {title}
          </h4>
          <span className="text-xs text-gray-500">
            {formatDate(date)}
          </span>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          {/* Airline Info */}
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center overflow-hidden shadow-sm">
              {airline.logo ? (
                <img
                  src={airline.logo}
                  alt={airline.name || 'Airline'}
                  className="w-6 h-6 object-contain"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'block';
                  }}
                />
              ) : null}
              <div className="text-xs font-bold text-gray-600" style={{ display: airline.logo ? 'none' : 'block' }}>
                ✈️
              </div>
            </div>
            <div>
              <div className="font-semibold text-gray-900 text-sm">{airline.name || 'Unknown Airline'}</div>
              <div className="text-xs text-gray-500">{flightInfo.number || '--'}</div>
              <div className="text-xs text-gray-500 mt-1">{formatDate(date)}</div>
            </div>
          </div>

          {/* Flight Route */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">
                  {departure.time ? formatTime(departure.time) : '--:--'}
                </div>
                <div className="text-xs text-gray-500">
                  {departure.airport || '--'}
                </div>
              </div>

              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1 h-0.5 bg-blue-200"></div>
                    <div className="text-xs text-gray-500 px-2">
                      {flight.duration ? formatDuration(flight.duration) : '--'}
                    </div>
                    <div className="flex-1 h-0.5 bg-blue-200"></div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {getStopsText(flight.stops || 0)}
                  </div>
                </div>
              </div>

              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">
                  {arrival.time ? formatTime(arrival.time) : '--:--'}
                </div>
                <div className="text-xs text-gray-500">
                  {arrival.airport || '--'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg shadow-md p-6"
    >
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Flight Summary
        </h3>
      </div>

      {/* Flight Details */}
      {tripType === 'oneWay' && selectedFlight && (
        renderFlightDetails(
          selectedFlight,
          'Departure Flight',
          `${searchData.origin} → ${searchData.destination}`,
          searchData.date
        )
      )}

      {tripType === 'return' && selectedOutboundFlight && selectedReturnFlight && (
        <>
          {renderFlightDetails(
            selectedOutboundFlight, 
            'Departure Flight', 
            `${searchData.origin} → ${searchData.destination}`,
            searchData.date
          )}
          {renderFlightDetails(
            selectedReturnFlight, 
            'Return Flight', 
            `${searchData.destination} → ${searchData.origin}`,
            searchData.returnDate
          )}
        </>
      )}

      {/* Price Summary */}
      <div className="border-t pt-4 mt-6">
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Total Price:</span>
          <span className="text-2xl font-bold text-green-600">
            ${tripType === 'return' && selectedOutboundFlight && selectedReturnFlight ? '9.98' : '4.99'}
          </span>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          Instant ticket download after payment
        </p>
      </div>
    </motion.div>
  );
};

export default FlightSummaryCard;
