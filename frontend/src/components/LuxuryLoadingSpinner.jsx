import React from 'react';
import { motion } from 'framer-motion';

const LuxuryLoadingSpinner = ({ 
  size = 'medium', 
  variant = 'primary', 
  message = 'Loading...',
  showMessage = true,
  fullScreen = false 
}) => {
  const sizeClasses = {
    small: 'w-8 h-8',
    medium: 'w-16 h-16',
    large: 'w-24 h-24',
    xl: 'w-32 h-32'
  };

  const variants = {
    primary: {
      gradient: 'from-brand-500 to-brand-600',
      glow: 'shadow-luxury-glow',
      accent: 'from-accent-500 to-accent-600'
    },
    accent: {
      gradient: 'from-accent-500 to-accent-600',
      glow: 'shadow-luxury-medium',
      accent: 'from-brand-500 to-brand-600'
    },
    luxury: {
      gradient: 'from-brand-500 via-blue-500 to-accent-500',
      glow: 'shadow-luxury-strong',
      accent: 'from-purple-500 to-pink-500'
    }
  };

  const currentVariant = variants[variant];

  const spinnerVariants = {
    animate: {
      rotate: 360,
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "linear"
      }
    }
  };

  const pulseVariants = {
    animate: {
      scale: [1, 1.2, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const orbitVariants = {
    animate: {
      rotate: 360,
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "linear"
      }
    }
  };

  const LoadingContent = () => (
    <div className="flex flex-col items-center justify-center space-y-6">
      {/* Main Spinner Container */}
      <div className="relative">
        {/* Outer Ring */}
        <motion.div
          className={`${sizeClasses[size]} rounded-full border-4 border-transparent bg-gradient-to-r ${currentVariant.gradient} ${currentVariant.glow}`}
          style={{
            background: `conic-gradient(from 0deg, transparent, rgba(14, 165, 233, 0.8), transparent)`
          }}
          variants={spinnerVariants}
          animate="animate"
        />
        
        {/* Inner Pulse */}
        <motion.div
          className={`absolute inset-2 rounded-full bg-gradient-to-r ${currentVariant.accent} opacity-60`}
          variants={pulseVariants}
          animate="animate"
        />
        
        {/* Center Dot */}
        <div className="absolute inset-1/2 w-2 h-2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-full shadow-lg" />
        
        {/* Orbiting Dots */}
        <motion.div
          className="absolute inset-0"
          variants={orbitVariants}
          animate="animate"
        >
          <div className="relative w-full h-full">
            <div className="absolute top-0 left-1/2 w-1.5 h-1.5 bg-brand-500 rounded-full -translate-x-1/2 shadow-lg" />
            <div className="absolute bottom-0 left-1/2 w-1.5 h-1.5 bg-accent-500 rounded-full -translate-x-1/2 shadow-lg" />
            <div className="absolute left-0 top-1/2 w-1.5 h-1.5 bg-blue-500 rounded-full -translate-y-1/2 shadow-lg" />
            <div className="absolute right-0 top-1/2 w-1.5 h-1.5 bg-purple-500 rounded-full -translate-y-1/2 shadow-lg" />
          </div>
        </motion.div>
      </div>

      {/* Loading Message */}
      {showMessage && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="text-center"
        >
          <p className="text-luxury-subtitle text-brand-700 font-semibold mb-2">
            {message}
          </p>
          <div className="flex items-center justify-center space-x-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-brand-500 rounded-full"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </div>
        </motion.div>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Luxury Background */}
        <div className="absolute inset-0 gradient-luxury-hero" />
        <div className="absolute inset-0 glass-luxury" />
        
        {/* Animated Background Elements */}
        <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-brand-400/20 to-brand-600/10 rounded-full blur-3xl animate-luxury-float" />
        <div className="absolute bottom-20 right-20 w-[32rem] h-[32rem] bg-gradient-to-br from-accent-400/20 to-accent-600/10 rounded-full blur-3xl animate-luxury-float" style={{animationDelay: '2s'}} />
        
        {/* Loading Content */}
        <div className="relative z-10">
          <LoadingContent />
        </div>
      </div>
    );
  }

  return <LoadingContent />;
};

// Skeleton Loading Components
export const LuxurySkeleton = ({ className = '', variant = 'default' }) => {
  const variants = {
    default: 'bg-gradient-to-r from-neutral-200 via-neutral-300 to-neutral-200',
    brand: 'bg-gradient-to-r from-brand-100 via-brand-200 to-brand-100',
    accent: 'bg-gradient-to-r from-accent-100 via-accent-200 to-accent-100'
  };

  return (
    <div 
      className={`animate-luxury-shimmer rounded-xl ${variants[variant]} ${className}`}
      style={{
        backgroundSize: '200% 100%'
      }}
    />
  );
};

export const LuxuryCardSkeleton = () => (
  <div className="card-luxury p-6 space-y-4">
    <LuxurySkeleton className="h-6 w-3/4" variant="brand" />
    <LuxurySkeleton className="h-4 w-full" />
    <LuxurySkeleton className="h-4 w-5/6" />
    <LuxurySkeleton className="h-4 w-4/5" />
    <div className="flex space-x-3 pt-4">
      <LuxurySkeleton className="h-10 w-24" variant="accent" />
      <LuxurySkeleton className="h-10 w-20" />
    </div>
  </div>
);

export const LuxuryListSkeleton = ({ items = 3 }) => (
  <div className="space-y-6">
    {Array.from({ length: items }).map((_, i) => (
      <div key={i} className="flex space-x-4">
        <LuxurySkeleton className="w-16 h-16 rounded-2xl" variant="brand" />
        <div className="flex-1 space-y-2">
          <LuxurySkeleton className="h-5 w-3/4" />
          <LuxurySkeleton className="h-4 w-full" />
          <LuxurySkeleton className="h-4 w-2/3" />
        </div>
      </div>
    ))}
  </div>
);

// Page Transition Component
export const LuxuryPageTransition = ({ children, className = '' }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        duration: 0.6,
        ease: [0.4, 0, 0.2, 1]
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

// Success/Error State Components
export const LuxurySuccessState = ({ message, onAction, actionText = 'Continue' }) => (
  <motion.div
    initial={{ scale: 0.8, opacity: 0 }}
    animate={{ scale: 1, opacity: 1 }}
    className="text-center p-8"
  >
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
      className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-luxury-glow"
    >
      <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
      </svg>
    </motion.div>
    <h3 className="text-luxury-title text-green-700 mb-4">{message}</h3>
    {onAction && (
      <button onClick={onAction} className="btn-luxury-primary">
        {actionText}
      </button>
    )}
  </motion.div>
);

export const LuxuryErrorState = ({ message, onRetry, retryText = 'Try Again' }) => (
  <motion.div
    initial={{ scale: 0.8, opacity: 0 }}
    animate={{ scale: 1, opacity: 1 }}
    className="text-center p-8"
  >
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
      className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-red-500 to-rose-500 rounded-full flex items-center justify-center shadow-luxury-medium"
    >
      <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M6 18L18 6M6 6l12 12" />
      </svg>
    </motion.div>
    <h3 className="text-luxury-title text-red-700 mb-4">{message}</h3>
    {onRetry && (
      <button onClick={onRetry} className="btn-luxury-secondary">
        {retryText}
      </button>
    )}
  </motion.div>
);

export default LuxuryLoadingSpinner;
