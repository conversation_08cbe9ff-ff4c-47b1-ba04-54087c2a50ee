import React from 'react';
import { useBooking } from '../context/BookingContext';

const ClearStorageButton = () => {
  const { clearAllData } = useBooking();

  const handleClearStorage = () => {
    if (window.confirm('Are you sure you want to clear all booking data? This will reset the form to empty state.')) {
      clearAllData();
      // Reload the page to see the changes
      window.location.reload();
    }
  };

  return (
    <button
      onClick={handleClearStorage}
      className="fixed top-4 right-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 text-sm z-50"
      title="Clear localStorage and reset form"
    >
      🧹 Clear Storage
    </button>
  );
};

export default ClearStorageButton;
