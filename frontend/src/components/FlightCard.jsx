import React from 'react';
import { motion } from 'framer-motion';

const FlightCard = ({ flight, onSelect, isSelected = false }) => {
  // Safety check for flight data
  if (!flight || typeof flight !== 'object') {
    console.error('FlightCard: Invalid flight data', flight);
    return null;
  }

  const formatTime = (dateString) => {
    if (!dateString) return '--:--';
    try {
      return new Date(dateString).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } catch (error) {
      console.warn('FlightCard: Invalid date string', dateString);
      return '--:--';
    }
  };

  const formatDuration = (duration) => {
    if (!duration) return '--';
    try {
      // Duration is in ISO 8601 format (PT6H15M)
      const match = duration.match(/PT(\d+H)?(\d+M)?/);
      if (!match) return duration;

      const hours = match[1] ? match[1].replace('H', 'h ') : '';
      const minutes = match[2] ? match[2].replace('M', 'm') : '';

      return `${hours}${minutes}`.trim();
    } catch (error) {
      console.warn('FlightCard: Invalid duration format', duration);
      return '--';
    }
  };

  const getStopsText = (stops) => {
    if (typeof stops !== 'number') return 'Direct';
    if (stops === 0) return 'Direct';
    if (stops === 1) return '1 Stop';
    return `${stops} Stops`;
  };

  // Extract flight data with fallbacks - using correct API structure
  const airline = flight.airline || {};
  const flightInfo = flight.flight || {};
  const departure = flightInfo.departure || {};
  const arrival = flightInfo.arrival || {};
  const price = flight.price || {};

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={`card-modern hover-glow hover-lift cursor-pointer transition-all duration-200 ${
        isSelected ? 'border-brand-500 bg-brand-50 shadow-medium' : 'border-neutral-200'
      }`}
      onClick={onSelect}
    >
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        {/* Flight Info */}
        <div className="flex-1">
          <div className="flex items-center justify-between mb-4">
            {/* Airline Info */}
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-neutral-100 rounded-xl flex items-center justify-center overflow-hidden">
                {airline.logo ? (
                  <img
                    src={airline.logo}
                    alt={airline.name || 'Airline'}
                    className="w-8 h-8 object-contain"
                    onError={(e) => {
                      console.warn('Failed to load airline logo:', airline.logo);
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'flex';
                    }}
                  />
                ) : null}
                <div
                  className="w-8 h-8 bg-brand-600 rounded flex items-center justify-center text-white text-xs font-bold"
                  style={{ display: airline.logo ? 'none' : 'flex' }}
                >
                  {airline.code || airline.name?.substring(0, 2).toUpperCase() || '✈️'}
                </div>
              </div>
              <div>
                <div className="font-semibold text-neutral-900">{airline.name || 'Unknown Airline'}</div>
                <div className="text-sm text-neutral-500">{flightInfo.number || 'N/A'}</div>
              </div>
            </div>

            {/* Price */}
            <div className="text-right">
              <div className="flex items-center gap-2 text-lg font-bold">
                <span className="text-accent-600">$4.99</span>
                {price.originalPrice && (
                  <span className="line-through text-neutral-400">
                    ${price.originalPrice}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Flight Route */}
          <div className="grid grid-cols-3 gap-4 items-center">
            {/* Departure */}
            <div className="text-center">
              <div className="text-2xl font-bold text-neutral-900">
                {formatTime(departure.time)}
              </div>
              <div className="text-lg font-semibold text-neutral-700">
                {departure.airport || 'N/A'}
              </div>
              {departure.terminal && (
                <div className="text-sm text-neutral-500">
                  Terminal {departure.terminal}
                </div>
              )}
            </div>

            {/* Flight Path */}
            <div className="text-center">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <div className="w-3 h-3 bg-brand-500 rounded-full"></div>
                <div className="flex-1 h-0.5 bg-brand-300 relative">
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-brand-500">
                    ✈️
                  </div>
                </div>
                <div className="w-3 h-3 bg-brand-500 rounded-full"></div>
              </div>
              <div className="text-sm text-neutral-600 font-medium">
                {formatDuration(flightInfo.duration)}
              </div>
              <div className="text-xs text-neutral-500">
                {getStopsText(flightInfo.stops)}
              </div>
            </div>

            {/* Arrival */}
            <div className="text-center">
              <div className="text-2xl font-bold text-neutral-900">
                {formatTime(arrival.time)}
              </div>
              <div className="text-lg font-semibold text-neutral-700">
                {arrival.airport || 'N/A'}
              </div>
              {arrival.terminal && (
                <div className="text-sm text-neutral-500">
                  Terminal {arrival.terminal}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Select Button */}
        <div className="mt-6 lg:mt-0 lg:ml-8">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={`w-full lg:w-auto px-8 py-3 rounded-xl font-bold transition-all duration-200 shadow-soft ${
              isSelected
                ? 'bg-accent-600 text-white hover:bg-accent-700'
                : 'bg-brand-500 text-white hover:bg-brand-600'
            }`}
            title={isSelected ? 'Click to unselect this flight' : 'Click to select this flight'}
            onClick={(e) => {
              e.stopPropagation();
              if (onSelect && typeof onSelect === 'function') {
                onSelect();
              }
            }}
          >
            {isSelected ? '✓ Selected' : 'Select Flight'}
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default FlightCard;
