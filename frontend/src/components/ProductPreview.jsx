import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const ProductPreview = () => {
  const [activeTab, setActiveTab] = useState('document');

  const tabs = [
    { id: 'document', label: 'Flight Document', icon: '📄' },
    { id: 'email', label: 'Email Confirmation', icon: '📧' },
    { id: 'mobile', label: 'Mobile View', icon: '📱' }
  ];

  return (
    <section className="section-padding bg-gradient-to-br from-white to-brand-50/30 relative overflow-hidden">
      {/* Premium background elements */}
      <div className="absolute top-20 right-10 w-96 h-96 bg-accent-400/10 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-20 left-10 w-72 h-72 bg-brand-400/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
      
      <div className="container-modern relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center bg-brand-100 text-brand-700 px-4 py-2 rounded-full text-sm font-semibold mb-6">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
            See Exactly What You Get
          </div>
          
          <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
            Professional 
            <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent block md:inline md:ml-2">
              Embassy-Ready Documents
            </span>
          </h2>
          
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed mb-12">
            No surprises, no guesswork. See exactly what your $4.99 gets you —
            <strong className="text-neutral-800">professional flight reservations that look identical to real airline bookings.</strong>
          </p>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex justify-center mb-12"
        >
          <div className="flex bg-white rounded-2xl p-2 shadow-soft border border-neutral-200">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'bg-brand-500 text-white shadow-glow'
                    : 'text-neutral-600 hover:text-brand-600 hover:bg-brand-50'
                }`}
              >
                <span className="text-lg mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </motion.div>

        {/* Preview Content */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="max-w-6xl mx-auto"
        >
          <AnimatePresence mode="wait">
            {activeTab === 'document' && (
              <motion.div
                key="document"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.3 }}
                className="premium-card p-8 relative overflow-hidden"
              >
                {/* Document Preview */}
                <div className="bg-white border-2 border-neutral-200 rounded-xl p-8 shadow-medium">
                  <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-brand-500 rounded-lg flex items-center justify-center mr-4">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-neutral-800">British Airways</h3>
                        <p className="text-sm text-neutral-600">Flight Reservation</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-neutral-600">Booking Reference</div>
                      <div className="text-lg font-bold text-brand-600">BA7X9K</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <div>
                      <h4 className="font-semibold text-neutral-800 mb-4">Passenger Details</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-neutral-600">Name:</span>
                          <span className="font-semibold">SMITH/JOHN MR</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600">Email:</span>
                          <span className="font-semibold"><EMAIL></span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-neutral-800 mb-4">Flight Details</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-neutral-600">Route:</span>
                          <span className="font-semibold">LHR → JFK</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600">Date:</span>
                          <span className="font-semibold">15 Aug 2024</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600">Time:</span>
                          <span className="font-semibold">14:30 - 17:45</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="border-t border-neutral-200 pt-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-accent-600">
                        <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span className="font-semibold">Embassy Approved Format</span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-neutral-600">Status</div>
                        <div className="font-bold text-accent-600">CONFIRMED</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Premium features callouts */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                  {[
                    { icon: '✅', text: 'Verifiable PNR Code' },
                    { icon: '🏛️', text: 'Embassy Accepted' },
                    { icon: '📄', text: 'Professional Document' }
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center justify-center bg-accent-50 text-accent-700 px-4 py-3 rounded-xl border border-accent-200">
                      <span className="text-lg mr-2">{feature.icon}</span>
                      <span className="font-semibold text-sm">{feature.text}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {activeTab === 'email' && (
              <motion.div
                key="email"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.3 }}
                className="premium-card p-8"
              >
                <div className="bg-white border-2 border-neutral-200 rounded-xl p-6 shadow-medium">
                  <div className="border-b border-neutral-200 pb-4 mb-6">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-bold text-neutral-800">Flight Reservation Confirmation</h3>
                      <span className="text-sm text-neutral-500">2 minutes ago</span>
                    </div>
                    <p className="text-sm text-neutral-600 mt-1">From: <EMAIL></p>
                  </div>
                  
                  <div className="space-y-4">
                    <p className="text-neutral-700">Dear John Smith,</p>
                    <p className="text-neutral-700">
                      Thank you for your reservation. Your flight booking has been confirmed and is ready for your visa application.
                    </p>
                    <div className="bg-brand-50 border border-brand-200 rounded-lg p-4">
                      <h4 className="font-semibold text-brand-800 mb-2">Booking Details</h4>
                      <div className="text-sm space-y-1">
                        <div><strong>Booking Reference:</strong> BA7X9K</div>
                        <div><strong>Route:</strong> London Heathrow (LHR) → New York JFK (JFK)</div>
                        <div><strong>Date:</strong> 15 August 2024</div>
                        <div><strong>Passenger:</strong> SMITH/JOHN MR</div>
                      </div>
                    </div>
                    <p className="text-neutral-700">
                      Your reservation document is attached and ready for embassy submission.
                    </p>
                  </div>
                </div>
              </motion.div>
            )}

            {activeTab === 'mobile' && (
              <motion.div
                key="mobile"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.3 }}
                className="flex justify-center"
              >
                <div className="w-80 bg-neutral-900 rounded-3xl p-2 shadow-luxury">
                  <div className="bg-white rounded-2xl p-6 h-96 overflow-y-auto">
                    <div className="text-center mb-4">
                      <div className="w-16 h-16 bg-brand-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                      </div>
                      <h3 className="font-bold text-neutral-800">Flight Reserved</h3>
                      <p className="text-sm text-neutral-600">Ready for visa application</p>
                    </div>
                    
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-neutral-600">Reference:</span>
                        <span className="font-semibold">BA7X9K</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-neutral-600">Route:</span>
                        <span className="font-semibold">LHR → JFK</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-neutral-600">Date:</span>
                        <span className="font-semibold">15 Aug 2024</span>
                      </div>
                    </div>
                    
                    <button className="w-full bg-brand-500 text-white py-3 rounded-xl font-semibold mt-6">
                      Instant Download
                    </button>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="inline-flex items-center bg-white/80 backdrop-blur-sm rounded-2xl px-8 py-6 shadow-soft border border-neutral-200">
            <div className="text-center">
              <p className="text-lg font-semibold text-neutral-800 mb-2">
                Get your professional flight reservation in 60 seconds
              </p>
              <p className="text-neutral-600 mb-4">
                No real ticket purchase required • Instant download • Embassy approved
              </p>
              <button className="premium-button">
                Start Your Reservation Now →
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ProductPreview;
