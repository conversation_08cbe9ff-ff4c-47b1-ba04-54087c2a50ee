import React from 'react';

// ULTRA SIMPLE SIDEBAR - CANNOT CRASH
const StickyFlightSidebar = (props) => {
  console.log('StickyFlightSidebar props:', props);

  const {
    tripType = 'oneWay',
    departureSelected = false,
    returnSelected = false,
    onContinue = () => {},
    onEditOutbound = () => {},
    onEditReturn = () => {},
    isMobile = false,
    className = "",
    selectedOutboundFlight = null,
    selectedReturnFlight = null,
    searchData = {}
  } = props || {};

  const canContinue = tripType === 'oneWay' ? departureSelected : (departureSelected && returnSelected);

  // Helper function to format time
  const formatTime = (dateString) => {
    if (!dateString) return '--:--';
    try {
      return new Date(dateString).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } catch (error) {
      return '--:--';
    }
  };

  // Helper function to format duration
  const formatDuration = (duration) => {
    if (!duration) return '--';
    try {
      const match = duration.match(/PT(\d+H)?(\d+M)?/);
      if (!match) return duration;
      const hours = match[1] ? match[1].replace('H', 'h ') : '';
      const minutes = match[2] ? match[2].replace('M', 'm') : '';
      return `${hours}${minutes}`.trim();
    } catch (error) {
      return '--';
    }
  };

  // Helper function to format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      return '';
    }
  };

  // ULTRA SIMPLE RENDER WITH DEBUG INFO
  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 p-6 ${className}`}>
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">✈️ Your Selection</h3>



        <div className="mb-4">
          <p className="text-sm text-gray-600">
            {tripType === 'oneWay'
              ? (departureSelected ? '1/1 flight selected' : '0/1 flight selected')
              : (departureSelected && returnSelected ? '2/2 flights selected' :
                 departureSelected ? '1/2 flights selected' :
                 '0/2 flights selected')
            }
          </p>
        </div>

        {/* Flight Selection Display */}
        {tripType === 'oneWay' && selectedOutboundFlight ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </div>
                <span className="text-green-700 text-sm font-semibold">Flight Selected</span>
              </div>
              <button
                onClick={onEditOutbound}
                className="text-blue-600 hover:text-blue-800 text-xs font-medium"
              >
                ✏️ Edit
              </button>
            </div>
            <div className="text-sm text-gray-700">
              <p><strong>{selectedOutboundFlight?.airline?.name || 'Airline'}</strong></p>
              <p>
                {formatTime(selectedOutboundFlight?.flight?.departure?.time)} → {formatTime(selectedOutboundFlight?.flight?.arrival?.time)}
                ({formatDuration(selectedOutboundFlight?.flight?.duration)})
              </p>
              <p>{searchData?.origin || 'Origin'} → {searchData?.destination || 'Destination'}</p>
              {searchData?.date && (
                <p className="text-xs text-blue-600 font-medium">📅 {formatDate(searchData.date)}</p>
              )}
            </div>
          </div>
        ) : tripType === 'return' && (selectedOutboundFlight || selectedReturnFlight) ? (
          <div className="space-y-3 mb-4">
            {selectedOutboundFlight && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-green-700 text-xs font-semibold">Departure Flight</span>
                  </div>
                  <button
                    onClick={onEditOutbound}
                    className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                  >
                    ✏️ Edit
                  </button>
                </div>
                <div className="text-xs text-gray-700">
                  <p><strong>{selectedOutboundFlight?.airline?.name || 'Airline'}</strong></p>
                  <p>
                    {formatTime(selectedOutboundFlight?.flight?.departure?.time)} → {formatTime(selectedOutboundFlight?.flight?.arrival?.time)}
                  </p>
                  <p>{searchData?.origin || 'Origin'} → {searchData?.destination || 'Destination'}</p>
                  {searchData?.date && (
                    <p className="text-xs text-blue-600 font-medium">📅 {formatDate(searchData.date)}</p>
                  )}
                </div>
              </div>
            )}
            {selectedReturnFlight && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-green-700 text-xs font-semibold">Return Flight</span>
                  </div>
                  <button
                    onClick={onEditReturn}
                    className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                  >
                    ✏️ Edit
                  </button>
                </div>
                <div className="text-xs text-gray-700">
                  <p><strong>{selectedReturnFlight?.airline?.name || 'Airline'}</strong></p>
                  <p>
                    {formatTime(selectedReturnFlight?.flight?.departure?.time)} → {formatTime(selectedReturnFlight?.flight?.arrival?.time)}
                  </p>
                  <p>{searchData?.destination || 'Destination'} → {searchData?.origin || 'Origin'}</p>
                  {searchData?.returnDate && (
                    <p className="text-xs text-blue-600 font-medium">📅 {formatDate(searchData.returnDate)}</p>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-6 mb-4">
            <div className="text-3xl mb-2">✈️</div>
            <h4 className="font-medium text-gray-700 mb-1">No flight selected</h4>
            <p className="text-xs text-gray-500">
              {tripType === 'oneWay'
                ? 'Choose a flight from the list to continue'
                : 'Select departure and return flights to continue'
              }
            </p>
          </div>
        )}

        <div className="mb-4">
          <div className="text-2xl font-bold text-green-600 mb-1">
            ${tripType === 'return' && selectedOutboundFlight && selectedReturnFlight ? '9.98' : '4.99'}
          </div>
          <div className="text-xs text-gray-500">Embassy-approved format</div>
        </div>

        <button
          onClick={onContinue}
          disabled={!canContinue}
          className={`w-full py-3 px-4 rounded-lg font-semibold text-sm ${
            canContinue
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
        >
          {canContinue
            ? 'Continue to Passenger Details'
            : tripType === 'oneWay'
              ? 'Select a flight'
              : 'Select flights'
          }
        </button>
      </div>
    </div>
  );
};

export default StickyFlightSidebar;
