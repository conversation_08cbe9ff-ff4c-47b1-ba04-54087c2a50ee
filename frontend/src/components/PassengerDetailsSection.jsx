import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PlusIcon, XMarkIcon, UserIcon, EnvelopeIcon } from '@heroicons/react/24/outline';

const PassengerDetailsSection = ({ 
  onSubmit, 
  isLoading = false,
  submitButtonText = "Continue to Payment",
  className = ""
}) => {
  // State management
  const [email, setEmail] = useState('');
  const [passengers, setPassengers] = useState([
    { id: 1, firstName: '', lastName: '' }
  ]);
  const [errors, setErrors] = useState({});

  // Validation functions
  const validateEmail = (email) => {
    if (!email.trim()) {
      return 'Email address is required';
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return 'Please enter a valid email address';
    }
    return null;
  };

  const validatePassenger = (passenger, index) => {
    const errors = {};
    const passengerNum = index + 1;
    
    if (!passenger.firstName.trim()) {
      errors[`passenger_${passenger.id}_firstName`] = `Passenger ${passengerNum} first name is required`;
    } else if (passenger.firstName.trim().length < 2) {
      errors[`passenger_${passenger.id}_firstName`] = `First name must be at least 2 characters`;
    }
    
    if (!passenger.lastName.trim()) {
      errors[`passenger_${passenger.id}_lastName`] = `Passenger ${passengerNum} last name is required`;
    } else if (passenger.lastName.trim().length < 2) {
      errors[`passenger_${passenger.id}_lastName`] = `Last name must be at least 2 characters`;
    }
    
    return errors;
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Validate email
    const emailError = validateEmail(email);
    if (emailError) {
      newErrors.email = emailError;
    }
    
    // Validate passengers
    passengers.forEach((passenger, index) => {
      const passengerErrors = validatePassenger(passenger, index);
      Object.assign(newErrors, passengerErrors);
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Event handlers
  const handleEmailChange = (e) => {
    const newEmail = e.target.value;
    setEmail(newEmail);
    
    // Clear email error when user starts typing
    if (errors.email) {
      setErrors(prev => ({ ...prev, email: null }));
    }
  };

  const handlePassengerChange = (id, field, value) => {
    setPassengers(prev => 
      prev.map(passenger => 
        passenger.id === id 
          ? { ...passenger, [field]: value }
          : passenger
      )
    );
    
    // Clear field error when user starts typing
    const errorKey = `passenger_${id}_${field}`;
    if (errors[errorKey]) {
      setErrors(prev => ({ ...prev, [errorKey]: null }));
    }
  };

  const addPassenger = () => {
    if (passengers.length < 2) {
      const newId = Math.max(...passengers.map(p => p.id)) + 1;
      setPassengers(prev => [...prev, { id: newId, firstName: '', lastName: '' }]);
    }
  };

  const removePassenger = (id) => {
    if (passengers.length > 1) {
      setPassengers(prev => prev.filter(passenger => passenger.id !== id));
      
      // Clear errors for removed passenger
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`passenger_${id}_firstName`];
        delete newErrors[`passenger_${id}_lastName`];
        return newErrors;
      });
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    // Format data for submission
    const formData = {
      email: email.trim(),
      passengers: passengers.map(p => ({
        firstName: p.firstName.trim(),
        lastName: p.lastName.trim()
      }))
    };
    
    onSubmit(formData);
  };

  return (
    <div className={`bg-white rounded-xl shadow-lg border border-gray-100 ${className}`}>
      <div className="p-6 sm:p-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Passenger Details
          </h2>
          <p className="text-gray-600">
            Please provide your contact information and passenger details
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Contact Information Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <EnvelopeIcon className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">
                Contact Information
              </h3>
            </div>
            
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email Address *
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={handleEmailChange}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                  errors.email ? 'border-red-500 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
              {errors.email && (
                <motion.p
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-2 text-sm text-red-600"
                >
                  {errors.email}
                </motion.p>
              )}
            </div>
          </div>

          {/* Passenger Information Section */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <UserIcon className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Passenger Information
                </h3>
              </div>
              
              {passengers.length < 2 && (
                <button
                  type="button"
                  onClick={addPassenger}
                  disabled={isLoading}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PlusIcon className="w-4 h-4" />
                  Add Passenger
                </button>
              )}
            </div>

            <div className="space-y-4">
              <AnimatePresence>
                {passengers.map((passenger, index) => (
                  <motion.div
                    key={passenger.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                    className="p-4 border border-gray-200 rounded-lg bg-gray-50"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-gray-900">
                        Passenger {index + 1}
                      </h4>
                      {passengers.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removePassenger(passenger.id)}
                          disabled={isLoading}
                          className="inline-flex items-center gap-1 px-3 py-1 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <XMarkIcon className="w-4 h-4" />
                          Remove
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label 
                          htmlFor={`firstName_${passenger.id}`}
                          className="block text-sm font-medium text-gray-700 mb-2"
                        >
                          First Name *
                        </label>
                        <input
                          type="text"
                          id={`firstName_${passenger.id}`}
                          value={passenger.firstName}
                          onChange={(e) => handlePassengerChange(passenger.id, 'firstName', e.target.value)}
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                            errors[`passenger_${passenger.id}_firstName`] ? 'border-red-500 bg-red-50' : 'border-gray-300'
                          }`}
                          placeholder="First Name"
                          disabled={isLoading}
                        />
                        {errors[`passenger_${passenger.id}_firstName`] && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="mt-2 text-sm text-red-600"
                          >
                            {errors[`passenger_${passenger.id}_firstName`]}
                          </motion.p>
                        )}
                      </div>

                      <div>
                        <label 
                          htmlFor={`lastName_${passenger.id}`}
                          className="block text-sm font-medium text-gray-700 mb-2"
                        >
                          Last Name *
                        </label>
                        <input
                          type="text"
                          id={`lastName_${passenger.id}`}
                          value={passenger.lastName}
                          onChange={(e) => handlePassengerChange(passenger.id, 'lastName', e.target.value)}
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                            errors[`passenger_${passenger.id}_lastName`] ? 'border-red-500 bg-red-50' : 'border-gray-300'
                          }`}
                          placeholder="Last Name"
                          disabled={isLoading}
                        />
                        {errors[`passenger_${passenger.id}_lastName`] && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="mt-2 text-sm text-red-600"
                          >
                            {errors[`passenger_${passenger.id}_lastName`]}
                          </motion.p>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {passengers.length >= 2 && (
              <div className="text-center">
                <p className="text-sm text-amber-600 font-medium">
                  Maximum of 2 passengers allowed
                </p>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="pt-6 border-t border-gray-200">
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-6 rounded-lg font-semibold text-lg hover:from-blue-700 hover:to-purple-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Processing...
                </div>
              ) : (
                submitButtonText
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PassengerDetailsSection;
