import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('🚨 ErrorBoundary caught an error:', error, errorInfo);

    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // 🔥 GOD MODE: Try to save user data before crash
    try {
      const bookingData = JSON.parse(localStorage.getItem('bookingData') || '{}');
      if (bookingData) {
        console.log('💾 Saving booking data before crash:', bookingData);
        localStorage.setItem('crashRecoveryData', JSON.stringify({
          ...bookingData,
          crashTime: new Date().toISOString(),
          error: error.toString()
        }));
      }
    } catch (saveError) {
      console.error('Failed to save crash recovery data:', saveError);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
            <div className="text-red-500 text-6xl mb-4">🚨</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Something went wrong
            </h2>
            <p className="text-gray-600 mb-6">
              Don't worry! Your payment data is safe. We'll help you recover your booking.
            </p>
            <div className="space-y-3">
              <button
                onClick={() => {
                  // 🔥 GOD MODE: Try to recover and continue
                  try {
                    const recoveryData = localStorage.getItem('crashRecoveryData');
                    if (recoveryData) {
                      localStorage.setItem('bookingData', recoveryData);
                    }
                  } catch (e) {
                    console.error('Recovery failed:', e);
                  }
                  window.location.href = '/success';
                }}
                className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
              >
                🔄 Recover My Booking
              </button>
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
              >
                🔄 Refresh Page
              </button>
              <button
                onClick={() => window.location.href = '/'}
                className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
              >
                🏠 Go to Home
              </button>
            </div>
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                💡 <strong>Need help?</strong> Contact support at{' '}
                <a href="mailto:<EMAIL>" className="underline">
                  <EMAIL>
                </a>
              </p>
            </div>
            {process.env.NODE_ENV === 'development' && (
              <details className="mt-6 text-left">
                <summary className="cursor-pointer text-sm text-gray-500">
                  🔧 Error Details (Development)
                </summary>
                <pre className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto max-h-32">
                  {this.state.error && this.state.error.toString()}
                  {this.state.errorInfo.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
