import React from 'react';
import PassengerDetailsSection from './PassengerDetailsSection';

/**
 * Wrapper component to integrate the new PassengerDetailsSection with the existing booking flow
 * This maintains compatibility with the current API while using the new component internally
 */
const PassengerDetailsSectionWrapper = ({
  initialPassengers = [{ id: 1, firstName: '', lastName: '' }],
  initialEmail = '',
  onSubmit,
  onPassengersChange,
  onEmailChange,
  isLoading = false,
  submitButtonText = 'Continue to Payment',
  disabled = false,
  className = ""
}) => {
  // Convert the new component's data format to match the existing API
  const handleNewComponentSubmit = (formData) => {
    console.log('🔄 PassengerDetailsSectionWrapper: Converting new format to legacy format');
    console.log('🔄 New format data:', formData);
    
    // The new component returns: { email: "...", passengers: [{ firstName: "...", lastName: "..." }] }
    // The existing system expects: { passengers: [...], email: "..." }
    // Add IDs to passengers to match existing format
    const passengersWithIds = formData.passengers.map((passenger, index) => ({
      id: index + 1,
      firstName: passenger.firstName,
      lastName: passenger.lastName
    }));
    
    const legacyFormatData = {
      passengers: passengersWithIds,
      email: formData.email
    };
    
    console.log('🔄 Legacy format data:', legacyFormatData);
    
    // Call the original onSubmit with the expected format
    if (onSubmit) {
      onSubmit(legacyFormatData);
    }
  };

  // Convert initial passengers from legacy format to new format
  const convertedInitialPassengers = initialPassengers.map(p => ({
    id: p.id || 1,
    firstName: p.firstName || '',
    lastName: p.lastName || ''
  }));

  // Handle real-time passenger changes (for controlled components)
  const handlePassengerChange = (newPassengers) => {
    console.log('🔄 PassengerDetailsSectionWrapper: Passenger change detected');
    
    if (onPassengersChange) {
      // Convert back to legacy format for the parent component
      const legacyPassengers = newPassengers.map(p => ({
        id: p.id,
        firstName: p.firstName,
        lastName: p.lastName
      }));
      onPassengersChange(legacyPassengers);
    }
  };

  // Handle real-time email changes
  const handleEmailChange = (newEmail) => {
    console.log('🔄 PassengerDetailsSectionWrapper: Email change detected');
    
    if (onEmailChange) {
      onEmailChange(newEmail);
    }
  };

  return (
    <PassengerDetailsSection
      onSubmit={handleNewComponentSubmit}
      isLoading={isLoading}
      submitButtonText={submitButtonText}
      className={className}
      // Note: The new component manages its own state internally,
      // so we don't pass initialPassengers and initialEmail
      // This ensures the form is always empty by default as requested
    />
  );
};

export default PassengerDetailsSectionWrapper;
