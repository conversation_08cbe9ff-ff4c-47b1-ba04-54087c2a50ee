import React, { useState } from 'react';
import InlinePaymentSection from './InlinePaymentSection';
import ErrorBoundary from './ErrorBoundary';

const PaymentDebugTest = () => {
  const [showPayment, setShowPayment] = useState(false);
  const [testData, setTestData] = useState({
    passengers: [{ firstName: 'John', lastName: 'Doe' }],
    email: '<EMAIL>'
  });

  const handlePaymentSuccess = (result) => {
    console.log('🎉 Payment success:', result);
    alert('Payment successful! Result: ' + JSON.stringify(result));
  };

  const handlePaymentError = (error) => {
    console.error('❌ Payment error:', error);
    alert('Payment error: ' + error);
  };

  const handleBack = () => {
    setShowPayment(false);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Payment Debug Test</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Data</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Email:</label>
              <input
                type="email"
                value={testData.email}
                onChange={(e) => setTestData({ ...testData, email: e.target.value })}
                className="w-full px-3 py-2 border rounded-lg"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Passenger Name:</label>
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder="First Name"
                  value={testData.passengers[0].firstName}
                  onChange={(e) => setTestData({
                    ...testData,
                    passengers: [{ ...testData.passengers[0], firstName: e.target.value }]
                  })}
                  className="flex-1 px-3 py-2 border rounded-lg"
                />
                <input
                  type="text"
                  placeholder="Last Name"
                  value={testData.passengers[0].lastName}
                  onChange={(e) => setTestData({
                    ...testData,
                    passengers: [{ ...testData.passengers[0], lastName: e.target.value }]
                  })}
                  className="flex-1 px-3 py-2 border rounded-lg"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Payment Test</h2>
          
          <div className="space-y-4">
            <button
              onClick={() => setShowPayment(!showPayment)}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700"
            >
              {showPayment ? 'Hide Payment' : 'Show Payment'}
            </button>
            
            <div className="text-sm text-gray-600">
              <p>Show Payment: {showPayment ? 'true' : 'false'}</p>
              <p>Test Data: {JSON.stringify(testData)}</p>
            </div>
          </div>

          {/* Payment Section */}
          <ErrorBoundary fallbackMessage="Payment system error in debug test">
            <InlinePaymentSection
              isVisible={showPayment}
              onBack={handleBack}
              onPaymentSuccess={handlePaymentSuccess}
              onPaymentError={handlePaymentError}
              totalAmount={4.99}
              tripType="oneWay"
              passengers={testData.passengers}
              email={testData.email}
            />
          </ErrorBoundary>
        </div>
      </div>
    </div>
  );
};

export default PaymentDebugTest;
