import React from "react";

/**
 * FlightTicket Component - Professional Airline Reservation Display
 *
 * Renders a realistic flight ticket that matches actual airline reservations
 * for visa applications and official documentation.
 *
 * @param {Object} props - Component props
 * @param {string} props.tripDates - Trip date range (e.g., "18 JUL 2021 › 19 JUL 2021")
 * @param {string} props.destination - Trip destination (e.g., "TRIP TO NEW YORK CITY")
 * @param {Array} props.passengers - Array of passenger objects with name property
 * @param {string} props.reservationCode - Main reservation code
 * @param {string} props.airlineReservationCode - Airline-specific reservation code
 * @param {Array} props.segments - Array of flight segment objects
 * @param {boolean} props.showNotice - Whether to show the disclaimer notice
 * @param {string} props.customNotice - Custom notice text
 *
 * Example usage:
 * <FlightTicket
 *   tripDates="18 JUL 2021 › 19 JUL 2021"
 *   destination="TRIP TO NEW YORK CITY"
 *   passengers={[{ name: "COOPER/JANE" }]}
 *   reservationCode="NHG8IQ"
 *   airlineReservationCode="NHG8IQ"
 *   segments={[...]}
 *   showNotice={true}
 * />
 */
const FlightTicket = ({
  tripDates,
  destination,
  passengers = [],
  reservationCode,
  airlineReservationCode,
  segments = [],
  showNotice = true,
  customNotice = "This is not a valid boarding pass. Please check with the airline before departure.",
}) => {
  return (
    <div className="bg-white max-w-4xl mx-auto p-8 text-black font-sans text-sm leading-tight print:shadow-none print:max-w-none print:p-6">
      {/* Header - Exact match to PDF */}
      <div className="border-b border-gray-400 pb-4 mb-6">
        <div className="text-base font-bold tracking-wide mb-1 uppercase">
          {tripDates} <span className="ml-4">{destination}</span>
        </div>
      </div>

      {/* Prepared For Section */}
      <div className="mb-6">
        <div className="font-bold text-sm mb-2">PREPARED FOR</div>
        {passengers.map((passenger, idx) => (
          <div key={idx} className="font-bold text-sm">
            {passenger.name}
          </div>
        ))}
      </div>

      {/* Reservation Codes */}
      <div className="mb-8 text-sm">
        <div className="mb-1">
          <span className="font-medium">RESERVATION CODE:</span> <span className="font-bold">{reservationCode}</span>
        </div>
        <div>
          <span className="font-medium">AIRLINE RESERVATION CODE:</span> <span className="font-bold">{airlineReservationCode}</span>
        </div>
      </div>

      {/* Flight Segments - Exact PDF Layout */}
      {segments.map((segment, segmentIndex) => (
        <div key={segmentIndex} className="mb-8">
          {/* Departure Header with Plane Icon */}
          <div className="border-b border-gray-400 pb-2 mb-4">
            <div className="flex items-center gap-2">
              <div className="text-lg">✈</div>
              <div className="font-bold text-sm uppercase">
                DEPARTURE: {segment.departureDay}
              </div>
            </div>
            <div className="text-xs text-gray-600 mt-1">
              Please verify flight times prior to departure
            </div>
          </div>

          {/* Flight Information Grid - Matches PDF Layout */}
          <div className="grid grid-cols-3 gap-6 mb-4">
            {/* Left Column - Airline & Flight Info */}
            <div className="bg-gray-100 p-4">
              <div className="font-bold text-sm mb-2">
                {segment.airline}
              </div>
              <div className="font-bold text-sm mb-3">
                {segment.flightNo}
              </div>
              <div className="text-xs mb-1">
                Duration: {segment.duration}
              </div>
              <div className="text-xs mb-1">
                Class: {segment.flightClass}
              </div>
              <div className="text-xs">
                Status: {segment.status}
              </div>
            </div>

            {/* Middle Column - Route Info */}
            <div className="space-y-4">
              <div>
                <div className="font-bold text-sm">{segment.from?.code}</div>
                <div className="text-xs">{segment.from?.city}</div>
              </div>
              <div>
                <div className="font-bold text-sm">{segment.to?.code}</div>
                <div className="text-xs">{segment.to?.city}</div>
              </div>
            </div>

            {/* Right Column - Aircraft & Details */}
            <div>
              <div className="text-xs mb-1">
                <span className="font-medium">Aircraft:</span>
              </div>
              <div className="text-xs mb-3">
                {segment.aircraft}
              </div>
              <div className="text-xs mb-1">
                <span className="font-medium">Distance (in miles):</span>
              </div>
              <div className="text-xs mb-1">
                {segment.distance}
              </div>
              <div className="text-xs mb-1">
                <span className="font-medium">Stop(s):</span> {segment.stops}
              </div>
              <div className="text-xs">
                <span className="font-medium">Meals:</span>
              </div>
              <div className="text-xs">
                {segment.meals}
              </div>
            </div>
          </div>

          {/* Time and Terminal Info */}
          <div className="grid grid-cols-2 gap-8 mb-4 text-xs">
            <div>
              <div className="font-medium mb-1">Departing At:</div>
              <div className="font-bold text-lg">{segment.from?.time}</div>
              <div className="mt-1">
                <span className="font-medium">Terminal:</span>
              </div>
              <div>{segment.from?.terminal}</div>
            </div>
            <div>
              <div className="font-medium mb-1">Arriving At:</div>
              <div className="font-bold text-lg">{segment.to?.time}</div>
              <div className="mt-1">
                <span className="font-medium">Terminal:</span>
              </div>
              <div>{segment.to?.terminal}</div>
            </div>
          </div>

          {/* Passenger Table - Exact PDF Match */}
          <div className="bg-gray-100 p-4">
            <table className="w-full text-xs border-collapse">
              <thead>
                <tr className="bg-gray-200">
                  <th className="py-2 px-3 border border-gray-400 font-bold text-left">
                    Passenger Name:
                  </th>
                  <th className="py-2 px-3 border border-gray-400 font-bold text-left">
                    Seats:
                  </th>
                  <th className="py-2 px-3 border border-gray-400 font-bold text-left">
                    Booking:
                  </th>
                </tr>
              </thead>
              <tbody>
                {passengers.map((passenger, passengerIndex) => (
                  <tr key={passengerIndex}>
                    <td className="py-2 px-3 border border-gray-400">
                      {passenger.name}
                    </td>
                    <td className="py-2 px-3 border border-gray-400">
                      Check-in required
                    </td>
                    <td className="py-2 px-3 border border-gray-400 font-bold">
                      CONFIRMED
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ))}

      {/* Notice Box - Professional Disclaimer */}
      {showNotice && (
        <div className="mt-8 mb-6 p-4 border-2 border-gray-400 bg-yellow-50">
          <div className="text-xs font-bold text-center">
            NOTICE: {customNotice}
          </div>
        </div>
      )}

      {/* Footer - Professional Branding */}
      <div className="mt-8 pt-4 border-t border-gray-400">
        <div className="text-xs text-gray-600 text-center">
          This flight reservation was generated by VerifiedOnward.com
          <br />
          For visa applications and travel documentation purposes
          <br />
          © 2025 VerifiedOnward.com - Professional Flight Reservations
        </div>
      </div>
    </div>
  );
};

export default FlightTicket;
