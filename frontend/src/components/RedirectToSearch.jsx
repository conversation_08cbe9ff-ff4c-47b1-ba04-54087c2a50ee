import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useBooking } from '../context/BookingContext';

const RedirectToSearch = () => {
  const navigate = useNavigate();
  const { searchData } = useBooking();

  useEffect(() => {
    // If there's search data, redirect to search results
    if (searchData) {
      navigate('/search', { replace: true });
    } else {
      // If no search data, redirect to home page
      navigate('/', { replace: true });
    }
  }, [navigate, searchData]);

  // Show a loading message while redirecting
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-gray-700 mb-2">Redirecting...</h2>
        <p className="text-gray-500">Taking you to the flight search page</p>
      </div>
    </div>
  );
};

export default RedirectToSearch;
