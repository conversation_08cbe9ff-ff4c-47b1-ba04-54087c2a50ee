import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LockClosedIcon, ArrowLeftIcon, ShieldCheckIcon, CreditCardIcon } from '@heroicons/react/24/outline';
import StripePayment from './StripePayment';
import PayPalPayment from './PayPalPayment';

const InlinePaymentSection = ({
  isVisible,
  onBack,
  onPaymentSuccess,
  onPaymentError,
  totalAmount,
  tripType,
  passengers,
  email
}) => {
  console.log('🚀 InlinePaymentSection: Component rendering with props:', {
    isVisible,
    totalAmount,
    tripType,
    passengers: passengers?.length || 0,
    email
  });

  const [paymentMethod, setPaymentMethod] = useState('stripe');
  const [isProcessing, setIsProcessing] = useState(false);
  const paymentRef = useRef(null);

  // Auto-scroll to payment section when it becomes visible
  useEffect(() => {
    if (isVisible && paymentRef.current) {
      setTimeout(() => {
        paymentRef.current.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start',
          inline: 'nearest'
        });
      }, 300); // Small delay to allow animation to start
    }
  }, [isVisible]);

  const handlePaymentSuccess = (paymentResult) => {
    if (!isProcessing) return; // Prevent duplicate calls
    setIsProcessing(false);
    onPaymentSuccess(paymentResult);
  };

  const handlePaymentError = (error) => {
    if (!isProcessing) return; // Prevent duplicate calls
    setIsProcessing(false);
    onPaymentError(error);
  };

  if (!isVisible) {
    console.log('🚀 InlinePaymentSection: Not visible, returning null');
    return null;
  }

  console.log('🚀 InlinePaymentSection: Visible, rendering payment section');

  return (
    <AnimatePresence>
      <motion.div
        ref={paymentRef}
        initial={{ opacity: 0, y: 30, height: 0 }}
        animate={{ opacity: 1, y: 0, height: 'auto' }}
        exit={{ opacity: 0, y: -30, height: 0 }}
        transition={{ 
          duration: 0.4, 
          ease: [0.4, 0, 0.2, 1],
          height: { duration: 0.3 }
        }}
        className="mt-6 overflow-hidden"
      >
        <div className="bg-white rounded-lg shadow-lg border border-gray-200 relative">
          {/* Processing Overlay */}
          {isProcessing && (
            <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50 rounded-lg">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-700 font-medium">Processing your payment...</p>
                <p className="text-sm text-gray-500 mt-1">Please do not close this window</p>
              </div>
            </div>
          )}
          {/* Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-full">
                  <LockClosedIcon className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Secure Payment
                  </h3>
                  <p className="text-sm text-gray-500">
                    Your payment information is encrypted and secure
                  </p>
                </div>
              </div>
              
              <button
                onClick={onBack}
                disabled={isProcessing}
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowLeftIcon className="w-4 h-4" />
                <span className="text-sm">Back to Details</span>
              </button>
            </div>
          </div>

          {/* Payment Summary */}
          <div className="p-6 bg-gray-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900">Order Summary</h4>
                <p className="text-sm text-gray-600 mt-1">
                  {tripType === 'return' ? 'Return Flight Ticket' : 'One-Way Flight Ticket'} 
                  {passengers.length > 1 && ` (${passengers.length} passengers)`}
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-600">
                  ${totalAmount.toFixed(2)}
                </div>
                <p className="text-xs text-gray-500">
                  Instant Ticket Download
                </p>
              </div>
            </div>
          </div>

          {/* Payment Method Selection */}
          <div className="p-6 border-b border-gray-200">
            <h4 className="font-medium text-gray-900 mb-4">Choose Payment Method</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <motion.button
                type="button"
                onClick={() => setPaymentMethod('stripe')}
                disabled={isProcessing}
                className={`p-4 border-2 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
                  paymentMethod === 'stripe'
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
                whileHover={{ scale: isProcessing ? 1 : 1.02 }}
                whileTap={{ scale: isProcessing ? 1 : 0.98 }}
              >
                <div className="flex items-center justify-center space-x-3">
                  <div className="text-2xl">💳</div>
                  <div className="text-left">
                    <div className="font-medium">Credit Card</div>
                    <div className="text-xs opacity-75">Visa, Mastercard, Amex</div>
                  </div>
                </div>
              </motion.button>

              <motion.button
                type="button"
                onClick={() => setPaymentMethod('paypal')}
                disabled={isProcessing}
                className={`p-4 border-2 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
                  paymentMethod === 'paypal'
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
                whileHover={{ scale: isProcessing ? 1 : 1.02 }}
                whileTap={{ scale: isProcessing ? 1 : 0.98 }}
              >
                <div className="flex items-center justify-center space-x-3">
                  <div className="text-2xl">🅿️</div>
                  <div className="text-left">
                    <div className="font-medium">PayPal</div>
                    <div className="text-xs opacity-75">Fast & secure</div>
                  </div>
                </div>
              </motion.button>
            </div>
          </div>

          {/* Payment Form */}
          <div className="p-6">
            <AnimatePresence mode="wait">
              {paymentMethod === 'stripe' && (
                <motion.div
                  key="stripe"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                >
                  <StripePayment
                    amount={totalAmount}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    isProcessing={isProcessing}
                    setIsProcessing={setIsProcessing}
                  />
                </motion.div>
              )}

              {paymentMethod === 'paypal' && (
                <motion.div
                  key="paypal"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <PayPalPayment
                    amount={totalAmount}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    isProcessing={isProcessing}
                    setIsProcessing={setIsProcessing}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Security Badges */}
          <div className="px-6 pb-6">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-4">
              <div className="flex items-center justify-center space-x-2 bg-green-50 text-green-700 px-3 py-2 rounded-full text-xs font-medium">
                <ShieldCheckIcon className="w-4 h-4" />
                <span>SSL Secured</span>
              </div>
              <div className="flex items-center justify-center space-x-2 bg-blue-50 text-blue-700 px-3 py-2 rounded-full text-xs font-medium">
                <CreditCardIcon className="w-4 h-4" />
                <span>PCI Compliant</span>
              </div>
              <div className="flex items-center justify-center space-x-2 bg-purple-50 text-purple-700 px-3 py-2 rounded-full text-xs font-medium">
                <LockClosedIcon className="w-4 h-4" />
                <span>Bank-Level Security</span>
              </div>
            </div>

            <div className="text-center">
              <p className="text-xs text-gray-500">
                Your payment information is encrypted and never stored on our servers
              </p>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default InlinePaymentSection;
