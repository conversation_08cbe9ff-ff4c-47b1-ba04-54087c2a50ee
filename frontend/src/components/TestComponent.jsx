import React from 'react';

// EMERGENCY TEST COMPONENT - ALWAYS RENDERS
const TestComponent = () => {
  console.log('🚨 TEST COMPONENT RENDERED!');

  return (
    <div className="p-8 bg-red-500 text-white text-center">
      <h1 className="text-4xl font-bold mb-4">🚨 EMERGENCY TEST COMPONENT</h1>
      <p className="text-xl">If you can see this, React is working!</p>
      <p className="text-lg mt-2">Component rendered successfully at {new Date().toLocaleTimeString()}</p>
    </div>
  );
};

export default TestComponent;
