import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useBooking } from '../context/BookingContext';

const PassengerDetails = () => {
  const navigate = useNavigate();
  const { setPassengers, setEmail } = useBooking();
  
  // State management
  const [emailInput, setEmailInput] = useState('');
  const [passengerList, setPassengerList] = useState([
    { firstName: '', lastName: '' }
  ]);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Add passenger handler
  const handleAddPassenger = () => {
    if (passengerList.length < 2) {
      setPassengerList([...passengerList, { firstName: '', lastName: '' }]);
    }
  };

  // Remove passenger handler
  const handleRemovePassenger = (indexToRemove) => {
    if (passengerList.length > 1) {
      const updatedPassengers = passengerList.filter((_, index) => index !== indexToRemove);
      setPassengerList(updatedPassengers);
      
      // Clear any errors for removed passenger
      const newErrors = { ...errors };
      delete newErrors[`firstName_${indexToRemove}`];
      delete newErrors[`lastName_${indexToRemove}`];
      setErrors(newErrors);
    }
  };

  // Update passenger data
  const handlePassengerChange = (index, field, value) => {
    const updatedPassengers = [...passengerList];
    updatedPassengers[index][field] = value;
    setPassengerList(updatedPassengers);
    
    // Clear error when user starts typing
    if (errors[`${field}_${index}`]) {
      const newErrors = { ...errors };
      delete newErrors[`${field}_${index}`];
      setErrors(newErrors);
    }
  };

  // Email change handler
  const handleEmailChange = (e) => {
    setEmailInput(e.target.value);
    
    // Clear email error when user starts typing
    if (errors.email) {
      const newErrors = { ...errors };
      delete newErrors.email;
      setErrors(newErrors);
    }
  };

  // Validation function
  const validateForm = () => {
    const newErrors = {};
    
    // Validate email
    if (!emailInput.trim()) {
      newErrors.email = 'Email address is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailInput)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    // Validate passengers
    passengerList.forEach((passenger, index) => {
      if (!passenger.firstName.trim()) {
        newErrors[`firstName_${index}`] = 'First name is required';
      }
      if (!passenger.lastName.trim()) {
        newErrors[`lastName_${index}`] = 'Last name is required';
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submit handler
  const handleContinueToPayment = async () => {
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Format data for context
      const formattedPassengers = passengerList.map((passenger, index) => ({
        id: index + 1,
        firstName: passenger.firstName.trim(),
        lastName: passenger.lastName.trim()
      }));
      
      // Save to booking context
      setPassengers(formattedPassengers);
      setEmail(emailInput.trim());
      
      console.log('✅ Passenger data saved:', {
        email: emailInput.trim(),
        passengers: formattedPassengers
      });
      
      // Navigate to checkout
      navigate('/checkout');
      
    } catch (error) {
      console.error('❌ Error saving passenger data:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Passenger Details
        </h2>
        <p className="text-gray-600">
          Please provide your contact information and passenger details
        </p>
      </div>

      {/* Contact Information Section */}
      <div className="mb-8">
        <h3 className="text-lg font-bold text-gray-900 mb-4">
          Contact Information
        </h3>
        
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            Email Address *
          </label>
          <input
            type="email"
            id="email"
            value={emailInput}
            onChange={handleEmailChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
              errors.email ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="<EMAIL>"
            disabled={isSubmitting}
          />
          {errors.email && (
            <p className="mt-2 text-sm text-red-600">{errors.email}</p>
          )}
        </div>
      </div>

      {/* Passenger Information Section */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-bold text-gray-900">
            Passenger Information
          </h3>
          
          {passengerList.length < 2 && (
            <button
              type="button"
              onClick={handleAddPassenger}
              disabled={isSubmitting}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Add Passenger
            </button>
          )}
        </div>

        {/* Passenger List */}
        <div className="space-y-6">
          {passengerList.map((passenger, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">
                  Passenger {index + 1}
                </h4>
                
                {index > 0 && (
                  <button
                    type="button"
                    onClick={() => handleRemovePassenger(index)}
                    disabled={isSubmitting}
                    className="text-red-600 hover:text-red-800 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Remove
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* First Name */}
                <div>
                  <label 
                    htmlFor={`firstName_${index}`}
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    First Name *
                  </label>
                  <input
                    type="text"
                    id={`firstName_${index}`}
                    value={passenger.firstName}
                    onChange={(e) => handlePassengerChange(index, 'firstName', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors[`firstName_${index}`] ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="First Name"
                    disabled={isSubmitting}
                  />
                  {errors[`firstName_${index}`] && (
                    <p className="mt-2 text-sm text-red-600">{errors[`firstName_${index}`]}</p>
                  )}
                </div>

                {/* Last Name */}
                <div>
                  <label 
                    htmlFor={`lastName_${index}`}
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Last Name *
                  </label>
                  <input
                    type="text"
                    id={`lastName_${index}`}
                    value={passenger.lastName}
                    onChange={(e) => handlePassengerChange(index, 'lastName', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors[`lastName_${index}`] ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Last Name"
                    disabled={isSubmitting}
                  />
                  {errors[`lastName_${index}`] && (
                    <p className="mt-2 text-sm text-red-600">{errors[`lastName_${index}`]}</p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Max passengers message */}
        {passengerList.length >= 2 && (
          <div className="mt-4 text-center">
            <p className="text-sm text-amber-600 font-medium">
              Maximum of 2 passengers allowed
            </p>
          </div>
        )}
      </div>

      {/* Continue to Payment Button */}
      <div className="pt-6 border-t border-gray-200">
        <button
          onClick={handleContinueToPayment}
          disabled={isSubmitting}
          className="w-full bg-blue-600 text-white py-4 px-6 rounded-lg font-semibold text-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? (
            <div className="flex items-center justify-center gap-2">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Processing...
            </div>
          ) : (
            'Continue to Payment'
          )}
        </button>
      </div>
    </div>
  );
};

export default PassengerDetails;
