import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState(null);

  const faqs = [
    {
      question: "What are onward flight reservations?",
      answer: "An onward flight reservation is a verifiable flight booking showing your travel itinerary, typically used for visa applications. It displays authentic airline data and meets embassy requirements by showing a confirmed travel plan without needing to purchase expensive, non-refundable tickets upfront."
    },
    {
      question: "What can I use onward flight reservations for?",
      answer: "Onward flight reservations can be used for: Visa applications requiring flight itinerary proof, demonstrating intent of onward or return travel, hotel check-ins requiring travel documentation, and any situation needing verifiable flight booking documentation without full payment."
    },
    {
      question: "How do I create my flight reservation?",
      answer: "It's simple and fast: 1. Use the search bar to select your origin, destination, and travel date. 2. Choose a flight from the real-time results. 3. Fill in passenger details (up to 2 travelers). 4. Pay the one-time fee of $4.99. 5. Instantly download your visa-ready flight document and receive a copy by email."
    },
    {
      question: "Does my flight reservation show a real flight itinerary?",
      answer: "Yes! Your flight reservation displays authentic flight data from global airlines, including airline name, flight number, times, and duration. The result is a professional, embassy-approved flight reservation that matches real airline routes and schedules — formatted for documentation purposes."
    },
    {
      question: "Does my flight reservation show a valid reservation code?",
      answer: "No. Flight reservations do not contain a valid PNR or confirmation number that can be checked on the airline's website. This is intentional: the reservation is created for embassy/documentation use only and is not meant for actual travel."
    },
    {
      question: "Can I add multiple passengers to my flight reservation?",
      answer: "Yes! You can add up to 2 travelers to a single flight reservation at no extra cost. Simply enter their names when creating your booking and all names will appear on one shared flight reservation file."
    },
    {
      question: "How will I receive my flight reservation?",
      answer: "Right after purchase, your flight reservation will be available for immediate download on the confirmation page. A backup copy will also be sent to your email. Please save the file, as the download link may expire later."
    },
    {
      question: "I didn't receive my flight reservation or I lost it. What can I do?",
      answer: "No worries! If you didn't receive your reservation file or accidentally deleted it, just email <NAME_EMAIL> with your order ID or payment receipt. We'll resend your reservation within hours."
    }
  ];

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const ChevronIcon = ({ isOpen }) => (
    <motion.svg
      className="w-5 h-5 text-blue-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      animate={{ rotate: isOpen ? 180 : 0 }}
      transition={{ duration: 0.2 }}
    >
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
    </motion.svg>
  );

  return (
    <section id="faq" className="py-16 bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-xl text-gray-600">
            Everything you need to know about our dummy ticket service
          </p>
        </motion.div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
            >
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full px-6 py-5 text-left flex justify-between items-center hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                aria-expanded={openIndex === index}
                aria-controls={`faq-answer-${index}`}
              >
                <h3 className="text-lg font-semibold text-gray-900 pr-4">
                  {faq.question}
                </h3>
                <ChevronIcon isOpen={openIndex === index} />
              </button>
              
              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    id={`faq-answer-${index}`}
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 pb-5 pt-2">
                      <p className="text-gray-700 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQ;
