import React from 'react';
import { motion } from 'framer-motion';

const GuaranteeSection = () => {
  const guarantees = [
    {
      icon: '🛡️',
      title: 'Quality Assurance',
      description: 'Professional documents that meet embassy standards and visa application requirements.',
      highlight: '100% Professional'
    },
    {
      icon: '⚡',
      title: 'Instant Download Promise',
      description: 'Get your professional PDF document within 60 seconds of payment completion.',
      highlight: '60 Second Delivery'
    },
    {
      icon: '🏛️',
      title: 'Embassy Acceptance Standards',
      description: '99.7% success rate. Professional documents that meet embassy requirements.',
      highlight: '99.7% Success Rate'
    },
    {
      icon: '🔒',
      title: 'Security & Privacy Promise',
      description: 'Bank-level SSL encryption. Your personal data is never stored or shared.',
      highlight: 'Bank-Level Security'
    }
  ];

  return (
    <section className="section-padding bg-gradient-to-br from-neutral-900 to-neutral-800 text-white relative overflow-hidden">
      {/* Premium background elements */}
      <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-brand-500/10 to-accent-500/10"></div>
      <div className="absolute top-20 right-10 w-96 h-96 bg-brand-400/10 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-20 left-10 w-72 h-72 bg-accent-400/10 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
      
      <div className="container-modern relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center bg-accent-500/20 text-accent-300 px-6 py-3 rounded-full text-sm font-semibold mb-6 border border-accent-500/30">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            Your Success is Our Priority
          </div>
          
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-white to-neutral-300 bg-clip-text text-transparent">
              Zero Risk,
            </span>
            <br />
            <span className="bg-gradient-to-r from-brand-400 to-accent-400 bg-clip-text text-transparent">
              Maximum Confidence
            </span>
          </h2>
          
          <p className="text-xl text-neutral-300 max-w-3xl mx-auto leading-relaxed">
            We're so confident in our service that we provide industry-leading quality standards.
            <strong className="text-white"> Your visa approval is our mission.</strong>
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {guarantees.map((guarantee, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-8 hover:bg-white/15 transition-all duration-300 group relative overflow-hidden"
            >
              {/* Premium gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-brand-500/10 to-accent-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              
              <div className="relative z-10">
                {/* Icon and highlight */}
                <div className="flex items-center justify-between mb-6">
                  <div className="text-5xl group-hover:animate-bounce-subtle">
                    {guarantee.icon}
                  </div>
                  <div className="bg-gradient-to-r from-accent-400 to-accent-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                    {guarantee.highlight}
                  </div>
                </div>
                
                {/* Content */}
                <h3 className="text-xl font-bold text-white mb-4 group-hover:text-brand-300 transition-colors">
                  {guarantee.title}
                </h3>
                <p className="text-neutral-300 leading-relaxed">
                  {guarantee.description}
                </p>
              </div>

              {/* Premium shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            </motion.div>
          ))}
        </div>

        {/* Final CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center mt-16"
        >
          <div className="inline-flex flex-col items-center bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl px-12 py-8 relative overflow-hidden group">
            {/* Background effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-brand-500/20 to-accent-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            
            <div className="relative z-10">
              <h3 className="text-2xl font-bold text-white mb-4">
                Ready to Get Your Visa Approved?
              </h3>
              <p className="text-neutral-300 mb-6 max-w-md">
                Join 75,000+ travelers who trusted us with their visa applications
              </p>
              
              <button className="bg-gradient-to-r from-brand-500 to-accent-500 hover:from-brand-600 hover:to-accent-600 text-white font-bold px-12 py-4 rounded-2xl transition-all duration-300 shadow-glow hover:shadow-luxury transform hover:scale-105 relative overflow-hidden group">
                {/* Button shimmer effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                
                <div className="relative z-10 flex items-center">
                  <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                  <span>Start Your $4.99 Reservation Now</span>
                  <svg className="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </div>
              </button>
              
              <div className="flex items-center justify-center mt-4 text-sm text-neutral-400">
                <svg className="w-4 h-4 mr-2 text-accent-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                Secure payment • Instant download • Professional quality
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default GuaranteeSection;
