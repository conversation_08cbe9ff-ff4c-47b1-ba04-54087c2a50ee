import React, { createContext, useContext, useReducer } from 'react';

// Initial state
const initialState = {
  searchData: null,
  selectedFlight: null,
  selectedOutboundFlight: null,
  selectedReturnFlight: null,
  passengers: [{ id: 1, firstName: '', lastName: '' }], // Always start with Passenger 1
  email: '',
  paymentMethod: 'stripe',
  bookingReference: null,
  paymentId: null,
  step: 'search', // search, results, passengers, checkout, success
  tripType: 'oneWay', // oneWay, return
  isLoaded: false // Track if data has been loaded from localStorage
};

// Action types
const ACTIONS = {
  SET_SEARCH_DATA: 'SET_SEARCH_DATA',
  SET_SELECTED_FLIGHT: 'SET_SELECTED_FLIGHT',
  SET_SELECTED_OUTBOUND_FLIGHT: 'SET_SELECTED_OUTBOUND_FLIGHT',
  SET_SELECTED_RETURN_FLIGHT: 'SET_SELECTED_RETURN_FLIGHT',
  SET_PASSENGERS: 'SET_PASSENGERS',
  SET_EMAIL: 'SET_EMAIL',
  SET_PAYMENT_METHOD: 'SET_PAYMENT_METHOD',
  SET_BOOKING_REFERENCE: 'SET_BOOKING_REFERENCE',
  SET_PAYMENT_ID: 'SET_PAYMENT_ID',
  SET_STEP: 'SET_STEP',
  SET_TRIP_TYPE: 'SET_TRIP_TYPE',
  RESET_BOOKING: 'RESET_BOOKING',
  SET_LOADED: 'SET_LOADED'
};

// Reducer function
function bookingReducer(state, action) {
  switch (action.type) {
    case ACTIONS.SET_SEARCH_DATA:
      return {
        ...state,
        searchData: action.payload,
        step: 'results'
      };
    
    case ACTIONS.SET_SELECTED_FLIGHT:
      return {
        ...state,
        selectedFlight: action.payload,
        step: 'passengers'
      };

    case ACTIONS.SET_SELECTED_OUTBOUND_FLIGHT:
      return {
        ...state,
        selectedOutboundFlight: action.payload
      };

    case ACTIONS.SET_SELECTED_RETURN_FLIGHT:
      return {
        ...state,
        selectedReturnFlight: action.payload
      };
    
    case ACTIONS.SET_PASSENGERS:
      return {
        ...state,
        passengers: action.payload
      };
    
    case ACTIONS.SET_EMAIL:
      return {
        ...state,
        email: action.payload
      };
    
    case ACTIONS.SET_PAYMENT_METHOD:
      return {
        ...state,
        paymentMethod: action.payload
      };
    
    case ACTIONS.SET_BOOKING_REFERENCE:
      return {
        ...state,
        bookingReference: action.payload
      };
    
    case ACTIONS.SET_PAYMENT_ID:
      return {
        ...state,
        paymentId: action.payload
      };
    
    case ACTIONS.SET_STEP:
      return {
        ...state,
        step: action.payload
      };

    case ACTIONS.SET_TRIP_TYPE:
      return {
        ...state,
        tripType: action.payload
      };

    case ACTIONS.RESET_BOOKING:
      return initialState;

    case ACTIONS.SET_LOADED:
      return {
        ...state,
        isLoaded: true
      };

    default:
      return state;
  }
}

// Create context
const BookingContext = createContext();

// Provider component
export const BookingProvider = ({ children }) => {
  const [state, dispatch] = useReducer(bookingReducer, initialState);

  // Load data from localStorage on mount
  React.useEffect(() => {
    console.log('BookingProvider: Loading data from localStorage...');
    try {
      const searchData = localStorage.getItem('bookingSearchData');
      const selectedFlight = localStorage.getItem('bookingSelectedFlight');
      const selectedOutboundFlight = localStorage.getItem('bookingSelectedOutboundFlight');
      const selectedReturnFlight = localStorage.getItem('bookingSelectedReturnFlight');
      const passengers = localStorage.getItem('bookingPassengers');
      const email = localStorage.getItem('bookingEmail');
      const tripType = localStorage.getItem('bookingTripType');

      console.log('localStorage data:', {
        searchData: searchData ? 'present' : 'missing',
        selectedFlight: selectedFlight ? 'present' : 'missing',
        selectedOutboundFlight: selectedOutboundFlight ? 'present' : 'missing',
        selectedReturnFlight: selectedReturnFlight ? 'present' : 'missing',
        passengers: passengers ? 'present' : 'missing',
        email: email ? 'present' : 'missing',
        tripType: tripType ? 'present' : 'missing'
      });

      if (searchData) {
        dispatch({ type: ACTIONS.SET_SEARCH_DATA, payload: JSON.parse(searchData) });
      }
      if (selectedFlight) {
        dispatch({ type: ACTIONS.SET_SELECTED_FLIGHT, payload: JSON.parse(selectedFlight) });
      }
      if (selectedOutboundFlight) {
        dispatch({ type: ACTIONS.SET_SELECTED_OUTBOUND_FLIGHT, payload: JSON.parse(selectedOutboundFlight) });
      }
      if (selectedReturnFlight) {
        dispatch({ type: ACTIONS.SET_SELECTED_RETURN_FLIGHT, payload: JSON.parse(selectedReturnFlight) });
      }
      if (passengers) {
        const parsedPassengers = JSON.parse(passengers);
        // Only load passengers if they don't contain test data
        const hasTestData = parsedPassengers.some(p =>
          p.firstName === 'Test' ||
          p.lastName === 'User' ||
          p.firstName === 'John' ||
          p.lastName === 'Doe' ||
          p.firstName === 'Jane' ||
          p.lastName === 'Smith'
        );
        if (!hasTestData) {
          // Ensure we always have at least one passenger
          const passengersToSet = parsedPassengers.length > 0
            ? parsedPassengers
            : [{ id: 1, firstName: '', lastName: '' }];
          dispatch({ type: ACTIONS.SET_PASSENGERS, payload: passengersToSet });
        }
      }
      if (email) {
        // Only load email if it's not test data
        const isTestEmail = email === '<EMAIL>' ||
                           email === '<EMAIL>' ||
                           email === '<EMAIL>';
        if (!isTestEmail) {
          dispatch({ type: ACTIONS.SET_EMAIL, payload: email });
        }
      }
      if (tripType) {
        dispatch({ type: ACTIONS.SET_TRIP_TYPE, payload: tripType });
      }

      // Mark as loaded
      dispatch({ type: ACTIONS.SET_LOADED });
      console.log('BookingProvider: Data loading complete');
    } catch (error) {
      console.error('Error loading booking data from storage:', error);
      // Still mark as loaded even if there was an error
      dispatch({ type: ACTIONS.SET_LOADED });
    }
  }, []);

  // Development helper - expose clearTestData to window for debugging
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      window.clearTestData = () => {
        const passengers = localStorage.getItem('bookingPassengers');
        const email = localStorage.getItem('bookingEmail');

        if (passengers) {
          const parsedPassengers = JSON.parse(passengers);
          const hasTestData = parsedPassengers.some(p =>
            p.firstName === 'Test' ||
            p.lastName === 'User' ||
            p.firstName === 'John' ||
            p.lastName === 'Doe' ||
            p.firstName === 'Jane' ||
            p.lastName === 'Smith'
          );
          if (hasTestData) {
            localStorage.removeItem('bookingPassengers');
            dispatch({ type: ACTIONS.SET_PASSENGERS, payload: [{ id: 1, firstName: '', lastName: '' }] });
            console.log('🧹 Test passenger data cleared');
          }
        }

        if (email && (email === '<EMAIL>' || email === '<EMAIL>' || email === '<EMAIL>')) {
          localStorage.removeItem('bookingEmail');
          dispatch({ type: ACTIONS.SET_EMAIL, payload: '' });
          console.log('🧹 Test email data cleared');
        }

        console.log('✅ Test data cleanup complete');
      };
      console.log('🛠️ Development mode: Use window.clearTestData() to clear test data');
    }
  }, []);

  // Action creators
  const actions = {
    setSearchData: (data) => {
      dispatch({ type: ACTIONS.SET_SEARCH_DATA, payload: data });
      // Also save to localStorage for persistence
      localStorage.setItem('bookingSearchData', JSON.stringify(data));
    },

    setSelectedFlight: (flight) => {
      dispatch({ type: ACTIONS.SET_SELECTED_FLIGHT, payload: flight });
      localStorage.setItem('bookingSelectedFlight', JSON.stringify(flight));
    },

    setSelectedOutboundFlight: (flight) => {
      dispatch({ type: ACTIONS.SET_SELECTED_OUTBOUND_FLIGHT, payload: flight });
      localStorage.setItem('bookingSelectedOutboundFlight', JSON.stringify(flight));
    },

    setSelectedReturnFlight: (flight) => {
      dispatch({ type: ACTIONS.SET_SELECTED_RETURN_FLIGHT, payload: flight });
      localStorage.setItem('bookingSelectedReturnFlight', JSON.stringify(flight));
    },

    setPassengers: (passengers) => {
      console.log('BookingContext: Setting passengers:', passengers);
      dispatch({ type: ACTIONS.SET_PASSENGERS, payload: passengers });
      localStorage.setItem('bookingPassengers', JSON.stringify(passengers));
      console.log('BookingContext: Passengers saved to localStorage');
    },

    setEmail: (email) => {
      console.log('BookingContext: Setting email:', email);
      dispatch({ type: ACTIONS.SET_EMAIL, payload: email });
      localStorage.setItem('bookingEmail', email);
      console.log('BookingContext: Email saved to localStorage');
    },

    setPaymentMethod: (method) => {
      dispatch({ type: ACTIONS.SET_PAYMENT_METHOD, payload: method });
    },

    setBookingReference: (reference) => {
      dispatch({ type: ACTIONS.SET_BOOKING_REFERENCE, payload: reference });
    },

    setPaymentId: (paymentId) => {
      dispatch({ type: ACTIONS.SET_PAYMENT_ID, payload: paymentId });
    },

    setStep: (step) => {
      dispatch({ type: ACTIONS.SET_STEP, payload: step });
    },

    setTripType: (tripType) => {
      dispatch({ type: ACTIONS.SET_TRIP_TYPE, payload: tripType });
      localStorage.setItem('bookingTripType', tripType);
    },

    resetBooking: () => {
      dispatch({ type: ACTIONS.RESET_BOOKING });
      // Clear localStorage
      localStorage.removeItem('bookingSearchData');
      localStorage.removeItem('bookingSelectedFlight');
      localStorage.removeItem('bookingSelectedOutboundFlight');
      localStorage.removeItem('bookingSelectedReturnFlight');
      localStorage.removeItem('bookingPassengers');
      localStorage.removeItem('bookingEmail');
      localStorage.removeItem('bookingTripType');
    },

    // Clear all localStorage data (for debugging/testing)
    clearAllData: () => {
      console.log('🧹 Clearing all booking data from localStorage...');
      localStorage.removeItem('bookingSearchData');
      localStorage.removeItem('bookingSelectedFlight');
      localStorage.removeItem('bookingSelectedOutboundFlight');
      localStorage.removeItem('bookingSelectedReturnFlight');
      localStorage.removeItem('bookingPassengers');
      localStorage.removeItem('bookingEmail');
      localStorage.removeItem('bookingTripType');

      // Reset state to initial values
      dispatch({ type: ACTIONS.RESET_BOOKING });
      console.log('✅ All booking data cleared');
    },

    // Clear test data from localStorage (development only)
    clearTestData: () => {
      if (process.env.NODE_ENV === 'development') {
        const passengers = localStorage.getItem('bookingPassengers');
        const email = localStorage.getItem('bookingEmail');

        if (passengers) {
          const parsedPassengers = JSON.parse(passengers);
          const hasTestData = parsedPassengers.some(p =>
            p.firstName === 'Test' ||
            p.lastName === 'User' ||
            p.firstName === 'John' ||
            p.lastName === 'Doe' ||
            p.firstName === 'Jane' ||
            p.lastName === 'Smith'
          );
          if (hasTestData) {
            localStorage.removeItem('bookingPassengers');
            dispatch({ type: ACTIONS.SET_PASSENGERS, payload: [{ id: 1, firstName: '', lastName: '' }] });
          }
        }

        if (email && (email === '<EMAIL>')) {
          localStorage.removeItem('bookingEmail');
          dispatch({ type: ACTIONS.SET_EMAIL, payload: '' });
        }

        console.log('🧹 Test data cleared from localStorage');
      }
    },

    // Load data from localStorage
    loadFromStorage: () => {
      try {
        const searchData = localStorage.getItem('bookingSearchData');
        const selectedFlight = localStorage.getItem('bookingSelectedFlight');
        const selectedOutboundFlight = localStorage.getItem('bookingSelectedOutboundFlight');
        const selectedReturnFlight = localStorage.getItem('bookingSelectedReturnFlight');
        const passengers = localStorage.getItem('bookingPassengers');
        const email = localStorage.getItem('bookingEmail');
        const tripType = localStorage.getItem('bookingTripType');

        if (searchData) {
          dispatch({ type: ACTIONS.SET_SEARCH_DATA, payload: JSON.parse(searchData) });
        }
        if (selectedFlight) {
          dispatch({ type: ACTIONS.SET_SELECTED_FLIGHT, payload: JSON.parse(selectedFlight) });
        }
        if (selectedOutboundFlight) {
          dispatch({ type: ACTIONS.SET_SELECTED_OUTBOUND_FLIGHT, payload: JSON.parse(selectedOutboundFlight) });
        }
        if (selectedReturnFlight) {
          dispatch({ type: ACTIONS.SET_SELECTED_RETURN_FLIGHT, payload: JSON.parse(selectedReturnFlight) });
        }
        if (passengers) {
          const parsedPassengers = JSON.parse(passengers);
          // Only load passengers if they don't contain test data
          const hasTestData = parsedPassengers.some(p =>
            p.firstName === 'Test' ||
            p.lastName === 'User' ||
            p.firstName === 'John' ||
            p.lastName === 'Doe' ||
            p.firstName === 'Jane' ||
            p.lastName === 'Smith'
          );
          if (!hasTestData) {
            dispatch({ type: ACTIONS.SET_PASSENGERS, payload: parsedPassengers });
          }
        }
        if (email) {
          // Only load email if it's not test data
          const isTestEmail = email === '<EMAIL>' ||
                             email === '<EMAIL>' ||
                             email === '<EMAIL>';
          if (!isTestEmail) {
            dispatch({ type: ACTIONS.SET_EMAIL, payload: email });
          }
        }
        if (tripType) {
          dispatch({ type: ACTIONS.SET_TRIP_TYPE, payload: tripType });
        }
      } catch (error) {
        console.error('Error loading booking data from storage:', error);
      }
    }
  };

  const value = {
    ...state,
    ...actions
  };

  return (
    <BookingContext.Provider value={value}>
      {children}
    </BookingContext.Provider>
  );
};

// Custom hook to use the booking context
export const useBooking = () => {
  const context = useContext(BookingContext);
  if (!context) {
    throw new Error('useBooking must be used within a BookingProvider');
  }
  return context;
};

export default BookingContext;
