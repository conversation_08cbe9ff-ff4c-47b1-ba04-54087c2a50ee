<!DOCTYPE html>
<html>
<head>
    <title>Clear <PERSON> Test Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        button { margin: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        .success { background: #28a745; }
        .success:hover { background: #218838; }
        .info { background: #17a2b8; color: white; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #ffc107; color: black; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { background: #dc3545; color: white; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Clear John <PERSON> Test Data</h1>
        <p>This tool will clear any <PERSON>e test data from localStorage to ensure passenger fields are empty by default.</p>
        
        <div class="warning">
            <strong>⚠️ Warning:</strong> This will clear test data including "John", "Doe", "<EMAIL>", etc.
        </div>
        
        <button onclick="showCurrentData()">Show Current Data</button>
        <button onclick="clearJohnDoeData()" class="danger">Clear John Doe Data</button>
        <button onclick="clearAllBookingData()" class="danger">Clear All Booking Data</button>
        <button onclick="refreshPage()" class="success">Refresh Page</button>
        
        <div id="result"></div>
        
        <h3>Current localStorage Data:</h3>
        <div id="currentData"></div>
    </div>

    <script>
        function showCurrentData() {
            const data = {};
            const bookingKeys = [
                'bookingPassengers',
                'bookingEmail',
                'bookingSearchData',
                'bookingSelectedFlight',
                'bookingSelectedOutboundFlight',
                'bookingSelectedReturnFlight'
            ];
            
            bookingKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    try {
                        data[key] = JSON.parse(value);
                    } catch (e) {
                        data[key] = value;
                    }
                }
            });
            
            document.getElementById('currentData').innerHTML = 
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }
        
        function clearJohnDoeData() {
            let cleared = [];
            
            // Check and clear John/Doe passenger data
            const passengers = localStorage.getItem('bookingPassengers');
            if (passengers) {
                try {
                    const parsedPassengers = JSON.parse(passengers);
                    const hasJohnDoeData = parsedPassengers.some(p => 
                        p.firstName === 'John' || 
                        p.lastName === 'Doe' ||
                        p.firstName === 'Jane' ||
                        p.lastName === 'Smith'
                    );
                    if (hasJohnDoeData) {
                        localStorage.removeItem('bookingPassengers');
                        cleared.push('John/Doe passenger data');
                    }
                } catch (e) {
                    console.error('Error parsing passengers:', e);
                }
            }
            
            // Check <NAME_EMAIL> email
            const email = localStorage.getItem('bookingEmail');
            if (email && (email === '<EMAIL>' || email === '<EMAIL>')) {
                localStorage.removeItem('bookingEmail');
                cleared.push('John Doe email data');
            }
            
            if (cleared.length > 0) {
                document.getElementById('result').innerHTML = 
                    '<div class="info">✅ Cleared: ' + cleared.join(', ') + '</div>' +
                    '<div class="warning">🔄 Please refresh the page or navigate to the booking flow to see empty fields</div>';
            } else {
                document.getElementById('result').innerHTML = 
                    '<div class="info">ℹ️ No John Doe test data found to clear</div>';
            }
            
            showCurrentData();
        }
        
        function clearAllBookingData() {
            const allBookingKeys = [
                'bookingSearchData',
                'bookingSelectedFlight', 
                'bookingSelectedOutboundFlight',
                'bookingSelectedReturnFlight',
                'bookingPassengers',
                'bookingEmail',
                'bookingPaymentMethod',
                'bookingReference',
                'bookingPaymentId',
                'bookingStep',
                'bookingTripType'
            ];
            
            let cleared = [];
            allBookingKeys.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    cleared.push(key);
                }
            });
            
            if (cleared.length > 0) {
                document.getElementById('result').innerHTML = 
                    '<div class="info">✅ Cleared all booking data: ' + cleared.length + ' items</div>' +
                    '<div class="warning">🔄 Please refresh the page to see clean state</div>';
            } else {
                document.getElementById('result').innerHTML = 
                    '<div class="info">ℹ️ No booking data found to clear</div>';
            }
            
            showCurrentData();
        }
        
        function refreshPage() {
            window.location.reload();
        }
        
        // Show data on load
        showCurrentData();
    </script>
</body>
</html>
