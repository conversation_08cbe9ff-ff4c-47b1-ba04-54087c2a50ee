const axios = require('axios');

async function testDebugOutput() {
  console.log('🔍 Testing debug output...');
  
  const bookingData = {
    bookingReference: 'DEBUGTEST',
    passengers: [
      { firstName: '<PERSON>', lastName: '<PERSON>' },
      { firstName: '<PERSON>', lastName: 'Doe' }
    ],
    outboundFlight: {
      airline: 'RYANAIR',
      flightNumber: 'FR 1234',
      departure: {
        code: 'LHR',
        city: 'London, United Kingdom',
        time: '2025-07-20T10:30:00Z',
        terminal: '2'
      },
      arrival: {
        code: 'BCN',
        city: 'Barcelona, Spain',
        time: '2025-07-20T13:45:00Z',
        terminal: '1'
      },
      duration: '2h 15m'
    },
    returnFlight: {
      airline: 'EASYJET',
      flightNumber: 'U2 5678',
      departure: {
        code: 'BCN',
        city: 'Barcelona, Spain',
        time: '2025-07-27T15:20:00Z',
        terminal: '1'
      },
      arrival: {
        code: 'LHR',
        city: 'London, United Kingdom',
        time: '2025-07-27T16:35:00Z',
        terminal: '2'
      },
      duration: '2h 15m'
    },
    totalPrice: 9.98
  };

  try {
    console.log('📤 Sending request to trigger debug output...');
    const response = await axios.post('http://localhost:5001/api/tickets/generate', bookingData);
    
    if (response.data.success) {
      console.log('✅ Request successful');
      console.log('📋 Check backend logs for debug output');
      console.log('📋 Expected to see aircraft assignments and reservation codes');
    } else {
      console.log('❌ Request failed:', response.data.error);
    }
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

testDebugOutput().catch(console.error);
