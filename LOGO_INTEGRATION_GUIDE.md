# 🎨 Company Logo Integration Guide

## 📁 **Step 1: Upload Your Logo File**

### **Where to Place Your Logo:**
1. Navigate to: `frontend/public/` folder
2. Place your logo file there with the name: `company-logo.png`
3. Supported formats: PNG, JPG, JPEG, SVG, WebP

### **Recommended Logo Specifications:**
- **Format**: PNG with transparent background (preferred)
- **Size**: 200-400px width, 50-80px height
- **Resolution**: High-DPI ready (2x resolution recommended)
- **File size**: Under 100KB for optimal loading

### **File Naming Options:**
- `company-logo.png` (default)
- `company-logo.svg` (vector format - best for scaling)
- `company-logo.jpg` (if no transparency needed)

## 🔧 **Step 2: Configure Your Logo**

### **Edit the Header Component:**
The logo configuration is in `frontend/src/components/Header.jsx` at lines 10-16:

```javascript
const logoConfig = {
  src: "/company-logo.png", // Change this to your filename
  alt: "Your Company Name - Description",
  fallbackText: "Your Company", // Text shown if logo fails
  height: "h-10 sm:h-12", // Responsive height
  maxWidth: "max-w-[200px]", // Maximum width
};
```

### **Customization Options:**

#### **1. Change Logo File:**
```javascript
src: "/your-logo-name.png"
```

#### **2. Update Alt Text:**
```javascript
alt: "Your Company - Professional Flight Booking"
```

#### **3. Adjust Size (Tailwind CSS classes):**
```javascript
height: "h-8 sm:h-10 md:h-12", // Small to large screens
maxWidth: "max-w-[150px]", // Prevent oversized logos
```

#### **4. Set Fallback Text:**
```javascript
fallbackText: "YourBrand", // Shows if image fails to load
```

## 📱 **Step 3: Responsive Design**

### **Current Responsive Behavior:**
- **Mobile**: Logo height = 40px (h-10)
- **Desktop**: Logo height = 48px (h-12)
- **Max width**: 200px to prevent oversized logos
- **Auto width**: Maintains aspect ratio

### **Custom Responsive Sizes:**
```javascript
// Small logo for mobile-first design
height: "h-8 sm:h-10 lg:h-14"

// Medium logo
height: "h-10 sm:h-12 lg:h-16"

// Large logo for prominent branding
height: "h-12 sm:h-14 lg:h-18"
```

## 🎯 **Step 4: Logo Placement Options**

### **Current Implementation:**
- ✅ Left side of header
- ✅ Clickable (links to homepage)
- ✅ Hover effects
- ✅ Mobile responsive
- ✅ Fallback to text if image fails

### **Alternative Configurations:**

#### **Logo + Text Combination:**
```javascript
<Link to="/" className="flex items-center space-x-3">
  <img src={logoConfig.src} className="h-10 w-auto" />
  <span className="text-xl font-bold text-blue-600">
    VerifiedOnward
  </span>
</Link>
```

#### **Logo Only (No Text):**
```javascript
<Link to="/" className="flex items-center">
  <img 
    src={logoConfig.src}
    alt={logoConfig.alt}
    className="h-12 w-auto"
  />
</Link>
```

## 🚀 **Step 5: Testing Your Logo**

### **Test Checklist:**
- [ ] Logo displays correctly on desktop
- [ ] Logo displays correctly on mobile
- [ ] Logo is clickable and links to homepage
- [ ] Logo maintains aspect ratio
- [ ] Logo doesn't overflow header height
- [ ] Fallback text works if image fails
- [ ] Logo loads quickly (under 1 second)

### **Test URLs:**
- Homepage: `http://localhost:5174/`
- Other pages: `http://localhost:5174/how-it-works`

## 🎨 **Step 6: Advanced Styling**

### **Add Logo Animations:**
```javascript
<motion.img
  src={logoConfig.src}
  alt={logoConfig.alt}
  className="h-12 w-auto"
  whileHover={{ scale: 1.05 }}
  transition={{ duration: 0.2 }}
/>
```

### **Dark Mode Support:**
```javascript
<img
  src={logoConfig.src}
  alt={logoConfig.alt}
  className="h-12 w-auto dark:invert" // Inverts colors in dark mode
/>
```

### **Multiple Logo Versions:**
```javascript
const logoConfig = {
  light: "/logo-light.png", // For light backgrounds
  dark: "/logo-dark.png",   // For dark backgrounds
  mobile: "/logo-mobile.png", // Simplified mobile version
};
```

## 📋 **Quick Setup Checklist**

1. **Upload logo** to `frontend/public/company-logo.png`
2. **Update filename** in Header.jsx if different
3. **Customize alt text** and fallback text
4. **Adjust size** if needed
5. **Test on different screen sizes**
6. **Verify clickability** and homepage redirect

## 🔧 **Troubleshooting**

### **Logo Not Showing:**
- Check file path: Must be in `/public` folder
- Verify filename matches code
- Check file permissions
- Try hard refresh (Ctrl+F5)

### **Logo Too Large/Small:**
- Adjust `height` classes in logoConfig
- Modify `maxWidth` to constrain size
- Use responsive classes: `h-8 sm:h-10 lg:h-12`

### **Logo Quality Issues:**
- Use PNG with transparent background
- Provide 2x resolution for retina displays
- Optimize file size (use tools like TinyPNG)
- Consider SVG for perfect scaling

## 📞 **Need Help?**

If you encounter any issues:
1. Check browser console for errors
2. Verify file path and naming
3. Test with a simple PNG file first
4. Ensure logo dimensions are reasonable

Your logo will be professionally integrated with hover effects, responsive design, and fallback handling!
