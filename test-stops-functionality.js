const axios = require('axios');
const fs = require('fs');

async function testStopsFunctionality() {
  console.log('🛑 TESTING FLIGHT STOPS FUNCTIONALITY');
  console.log('====================================');
  console.log('Testing various stop scenarios to ensure PDF displays correct stop information\n');

  const testScenarios = [
    {
      name: 'Direct Flight (0 stops)',
      filename: 'direct-flight-test.pdf',
      data: {
        bookingReference: 'DIRECT001',
        passengers: [{ firstName: 'JOHN', lastName: 'SMITH' }],
        outboundFlight: {
          airline: 'BRITISH AIRWAYS',
          flightNumber: 'BA 117',
          duration: '8h 45m',
          stops: 0,
          layovers: [],
          departure: {
            code: 'LHR',
            city: 'London Heathrow',
            time: '2025-07-21T10:30:00Z',
            terminal: '5'
          },
          arrival: {
            code: 'JFK',
            city: 'New York JFK',
            time: '2025-07-21T15:15:00Z',
            terminal: '7'
          }
        },
        totalPrice: 4.99
      }
    },
    {
      name: 'One Stop Flight',
      filename: 'one-stop-flight-test.pdf',
      data: {
        bookingReference: 'ONESTOP001',
        passengers: [{ firstName: 'JANE', lastName: 'DOE' }],
        outboundFlight: {
          airline: 'LUFTHANSA',
          flightNumber: 'LH 441',
          duration: '12h 15m',
          stops: 1,
          layovers: [
            {
              airport: 'FRA',
              city: 'Frankfurt',
              duration: '2h 30m'
            }
          ],
          departure: {
            code: 'MAN',
            city: 'Manchester',
            time: '2025-07-21T08:00:00Z',
            terminal: '1'
          },
          arrival: {
            code: 'DXB',
            city: 'Dubai',
            time: '2025-07-21T20:15:00Z',
            terminal: '3'
          }
        },
        totalPrice: 4.99
      }
    },
    {
      name: 'Two Stops Flight',
      filename: 'two-stops-flight-test.pdf',
      data: {
        bookingReference: 'TWOSTOPS001',
        passengers: [{ firstName: 'MIKE', lastName: 'JOHNSON' }],
        outboundFlight: {
          airline: 'EMIRATES',
          flightNumber: 'EK 123',
          duration: '18h 45m',
          stops: 2,
          layovers: [
            {
              airport: 'DXB',
              city: 'Dubai',
              duration: '3h 15m'
            },
            {
              airport: 'BKK',
              city: 'Bangkok',
              duration: '1h 45m'
            }
          ],
          departure: {
            code: 'LHR',
            city: 'London Heathrow',
            time: '2025-07-21T14:20:00Z',
            terminal: '3'
          },
          arrival: {
            code: 'SYD',
            city: 'Sydney',
            time: '2025-07-22T09:05:00Z',
            terminal: '1'
          }
        },
        totalPrice: 4.99
      }
    },
    {
      name: 'Round Trip with Different Stops',
      filename: 'round-trip-stops-test.pdf',
      data: {
        bookingReference: 'ROUNDTRIP001',
        passengers: [
          { firstName: 'SARAH', lastName: 'WILSON' },
          { firstName: 'DAVID', lastName: 'WILSON' }
        ],
        outboundFlight: {
          airline: 'QATAR AIRWAYS',
          flightNumber: 'QR 001',
          duration: '14h 30m',
          stops: 1,
          layovers: [
            {
              airport: 'DOH',
              city: 'Doha',
              duration: '2h 45m'
            }
          ],
          departure: {
            code: 'LHR',
            city: 'London Heathrow',
            time: '2025-07-21T22:15:00Z',
            terminal: '4'
          },
          arrival: {
            code: 'SIN',
            city: 'Singapore',
            time: '2025-07-22T12:45:00Z',
            terminal: '1'
          }
        },
        returnFlight: {
          airline: 'SINGAPORE AIRLINES',
          flightNumber: 'SQ 321',
          duration: '13h 20m',
          stops: 0,
          layovers: [],
          departure: {
            code: 'SIN',
            city: 'Singapore',
            time: '2025-07-28T01:30:00Z',
            terminal: '3'
          },
          arrival: {
            code: 'LHR',
            city: 'London Heathrow',
            time: '2025-07-28T07:50:00Z',
            terminal: '2'
          }
        },
        totalPrice: 9.98
      }
    }
  ];

  console.log(`🧪 Running ${testScenarios.length} test scenarios...\n`);

  for (const scenario of testScenarios) {
    try {
      console.log(`📋 Testing: ${scenario.name}`);
      console.log(`   📄 Expected PDF: ${scenario.filename}`);
      
      // Generate PDF
      const response = await axios.post('http://localhost:5001/api/tickets/generate', scenario.data);
      
      if (response.data.success) {
        console.log(`   ✅ Booking generated: ${response.data.reservationCode}`);
        
        // Download PDF
        const downloadResponse = await axios.get(`http://localhost:5001${response.data.downloadUrl}`, {
          responseType: 'arraybuffer'
        });
        
        fs.writeFileSync(scenario.filename, downloadResponse.data);
        console.log(`   📄 PDF saved: ${scenario.filename}`);
        
        // Log expected stops information
        const outboundStops = scenario.data.outboundFlight.stops;
        const returnStops = scenario.data.returnFlight?.stops;
        
        console.log(`   🛑 Expected Outbound Stops: ${outboundStops}`);
        if (scenario.data.outboundFlight.layovers?.length > 0) {
          const layoverCodes = scenario.data.outboundFlight.layovers.map(l => l.airport).join(', ');
          console.log(`   ✈️  Outbound Layovers: ${layoverCodes}`);
        }
        
        if (returnStops !== undefined) {
          console.log(`   🛑 Expected Return Stops: ${returnStops}`);
          if (scenario.data.returnFlight.layovers?.length > 0) {
            const returnLayoverCodes = scenario.data.returnFlight.layovers.map(l => l.airport).join(', ');
            console.log(`   ✈️  Return Layovers: ${returnLayoverCodes}`);
          }
        }
        
      } else {
        console.log(`   ❌ Failed: ${response.data.error}`);
      }
      
      console.log(''); // Empty line for readability
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      console.log(''); // Empty line for readability
    }
  }

  console.log('🔍 VERIFICATION CHECKLIST');
  console.log('=========================');
  console.log('For each generated PDF, verify:');
  console.log('');
  console.log('1. ROUTE SUMMARY LINE:');
  console.log('   ✅ Shows correct stop count: "Stop(s): 0", "Stop(s): 1", "Stop(s): 2"');
  console.log('   ✅ Shows layover airports when available: "Stop(s): 1 (via FRA)"');
  console.log('   ✅ Multiple layovers: "Stop(s): 2 (via DXB, BKK)"');
  console.log('');
  console.log('2. FLIGHT DETAILS TABLE:');
  console.log('   ✅ STOP(S) field shows correct number');
  console.log('   ✅ Shows layover details with durations when available');
  console.log('   ✅ Format: "1 (via FRA (2h 30m))" or "2 (via DXB (3h 15m), BKK (1h 45m))"');
  console.log('');
  console.log('3. ROUND TRIP FLIGHTS:');
  console.log('   ✅ Outbound and return flights show different stop counts correctly');
  console.log('   ✅ Each segment displays its own stop information independently');
  console.log('');
  console.log('4. VISUAL CONSISTENCY:');
  console.log('   ✅ Maintains authentic airline reservation aesthetic');
  console.log('   ✅ No regression in previously fixed verification text positioning');
  console.log('   ✅ No regression in Important Information section removal');
  console.log('');
  console.log('📄 Generated test files:');
  testScenarios.forEach(scenario => {
    console.log(`   • ${scenario.filename} - ${scenario.name}`);
  });
  console.log('');
  console.log('🎯 Open each PDF to verify stops are displayed correctly!');
}

testStopsFunctionality().catch(console.error);
