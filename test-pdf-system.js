/**
 * Test Script for VerifiedOnward.com PDF Generation System
 * Tests the complete flight ticket PDF generation workflow
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3001'; // Backend server
const FRONTEND_URL = 'http://localhost:5175'; // Frontend server

// Test data matching the PDF sample
const testTicketData = {
  tripDates: "18 JUL 2021 › 19 JUL 2021",
  destination: "TRIP TO NEW YORK CITY",
  passengers: [
    { name: "COOPER/JANE MR." },
    { name: "WILSON/JENNY MR." }
  ],
  reservationCode: "NHG8IQ",
  airlineReservationCode: "NHG8IQ",
  segments: [
    {
      departureDay: "SUNDAY 18 JUL",
      airline: "CATHAY PACIFIC",
      flightNo: "CX 784",
      duration: "05hr(s) 00min(s)",
      flightClass: "Economy Class (M)",
      status: "Confirmed",
      from: { code: "DPS", city: "Denpasar-Bali, Indonesia", time: "16:05", terminal: "I" },
      to: { code: "HKG", city: "Hong Kong, Hong Kong", time: "21:05", terminal: "1" },
      aircraft: "AIRBUS INDUSTRIE A330-300",
      stops: "0",
      meals: "Not Available",
      distance: "Not Available"
    },
    {
      departureDay: "MONDAY 19 JUL",
      airline: "CATHAY PACIFIC",
      flightNo: "CX 844",
      duration: "15hr(s) 55min(s)",
      flightClass: "Economy Class (M)",
      status: "Confirmed",
      from: { code: "HKG", city: "Hong Kong, Hong Kong", time: "02:05", terminal: "1" },
      to: { code: "JFK", city: "New York, United States Of America", time: "06:00", terminal: "8" },
      aircraft: "BOEING 777-300ER",
      stops: "0",
      meals: "Not Available",
      distance: "Not Available"
    }
  ],
  showNotice: true,
  customNotice: "This is not a valid boarding pass. Please check with the airline before departure."
};

// Test booking data for full workflow
const testBookingData = {
  bookingReference: "TEST123",
  passengers: [
    { firstName: "Jane", lastName: "Cooper", title: "Mr" },
    { firstName: "Jenny", lastName: "Wilson", title: "Mr" }
  ],
  outboundFlight: {
    departure: {
      code: "DPS",
      city: "Denpasar-Bali, Indonesia",
      time: "2021-07-18T16:05:00Z",
      terminal: "I"
    },
    arrival: {
      code: "HKG",
      city: "Hong Kong, Hong Kong",
      time: "2021-07-18T21:05:00Z",
      terminal: "1"
    },
    airline: "CATHAY PACIFIC",
    flightNumber: "CX 784",
    duration: "05hr(s) 00min(s)",
    aircraft: "AIRBUS INDUSTRIE A330-300"
  },
  returnFlight: {
    departure: {
      code: "HKG",
      city: "Hong Kong, Hong Kong",
      time: "2021-07-19T02:05:00Z",
      terminal: "1"
    },
    arrival: {
      code: "JFK",
      city: "New York, United States Of America",
      time: "2021-07-19T06:00:00Z",
      terminal: "8"
    },
    airline: "CATHAY PACIFIC",
    flightNumber: "CX 844",
    duration: "15hr(s) 55min(s)",
    aircraft: "BOEING 777-300ER"
  },
  totalPrice: 299
};

async function runTests() {
  console.log('🚀 Starting VerifiedOnward PDF System Tests...\n');

  try {
    // Test 1: Health Check
    console.log('1️⃣ Testing Backend Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/api/tickets/health`);
    console.log('✅ Backend is running:', healthResponse.data.message);

    // Test 2: Direct PDF Generation
    console.log('\n2️⃣ Testing Direct PDF Generation...');
    const pdfResponse = await axios.post(`${BASE_URL}/api/tickets/generate-pdf`, testTicketData, {
      responseType: 'arraybuffer'
    });
    
    if (pdfResponse.status === 200) {
      const pdfPath = path.join(__dirname, 'test-output-direct.pdf');
      fs.writeFileSync(pdfPath, pdfResponse.data);
      console.log('✅ Direct PDF generated successfully:', pdfPath);
    }

    // Test 3: Full Booking Workflow
    console.log('\n3️⃣ Testing Full Booking Workflow...');
    const bookingResponse = await axios.post(`${BASE_URL}/api/tickets/generate`, testBookingData);
    console.log('✅ Booking created:', bookingResponse.data);

    // Test 4: PDF Download
    if (bookingResponse.data.downloadUrl) {
      console.log('\n4️⃣ Testing PDF Download...');
      const downloadResponse = await axios.get(`${BASE_URL}${bookingResponse.data.downloadUrl}`, {
        responseType: 'arraybuffer'
      });
      
      if (downloadResponse.status === 200) {
        const downloadPath = path.join(__dirname, 'test-output-download.pdf');
        fs.writeFileSync(downloadPath, downloadResponse.data);
        console.log('✅ PDF download successful:', downloadPath);
      }
    }

    // Test 5: Verification Endpoint
    console.log('\n5️⃣ Testing Verification Endpoint...');
    const verifyResponse = await axios.get(`${BASE_URL}/api/tickets/verify/${testBookingData.bookingReference}`);
    console.log('✅ Verification successful:', verifyResponse.data);

    console.log('\n🎉 All tests passed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('- ✅ Backend health check');
    console.log('- ✅ Direct PDF generation');
    console.log('- ✅ Full booking workflow');
    console.log('- ✅ PDF download functionality');
    console.log('- ✅ QR code verification');
    console.log('\n📄 Generated PDFs:');
    console.log('- test-output-direct.pdf');
    console.log('- test-output-download.pdf');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests, testTicketData, testBookingData };
