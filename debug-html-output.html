
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight Reservation - HTMLDEBUG</title>
    <style>
        /* Authentic airline reservation system typography */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 12px;
            background: white;
            color: #000;
            line-height: 1.1;
            font-size: 11pt;
        }

        /* Authentic airline font hierarchy - ALL CAPS and bold */
        .header-large {
            font-size: 14pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .header-medium {
            font-size: 12pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
        }
        .subheader {
            font-size: 11pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
        }
        .body-text {
            font-size: 10pt;
            color: #000;
            font-weight: normal;
        }
        .small-text {
            font-size: 9pt;
            color: #000;
        }
        .tiny-text {
            font-size: 8pt;
            color: #000;
        }

        /* Layout structure - tighter spacing */
        .ticket-container {
            max-width: 750px;
            margin: 0 auto;
            background: white;
        }

        /* Sharp rectangular flight segment boxes - authentic airline style */
        .flight-segment {
            border: 3px solid #000;
            margin: 5px 0;
            background: white;
            display: table;
            width: 100%;
            border-collapse: separate;
            border-bottom: 3px solid #000;
            margin-bottom: 5px;
        }

        /* Left-side airline branding panel - more prominent */
        .airline-branding-panel {
            display: table-cell;
            width: 160px;
            background: #f0f0f0;
            border-right: 3px solid #000;
            padding: 12px 8px;
            vertical-align: middle;
            text-align: left;
        }

        .airline-branding-panel .airline-name-block {
            text-align: center;
            margin-bottom: 8px;
        }

        .airline-branding-panel .airline-name {
            font-size: 12pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
            margin-bottom: 2px;
            line-height: 1.0;
        }

        .airline-branding-panel .flight-number {
            font-size: 12pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
            line-height: 1.0;
        }

        .airline-branding-panel .flight-details {
            margin-top: 8px;
            font-size: 9pt;
            color: #000;
        }

        /* Flight details grid - authentic airline system layout */
        .flight-details-panel {
            display: table-cell;
            padding: 0;
            vertical-align: top;
        }

        .flight-info-grid {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .flight-info-grid, .flight-info-grid th, .flight-info-grid td {
            border: 1px solid #ccc;
            border-collapse: collapse;
        }

        .flight-info-grid td {
            padding: 4px 8px;
            vertical-align: middle;
            font-size: 13px;
            font-weight: 400;
            color: #000;
        }

        .flight-info-grid .info-label {
            background: #f5f5f5;
            font-weight: 600;
            color: #555;
            text-transform: uppercase;
            font-size: 13px;
            width: 25%;
        }

        .flight-info-grid .info-value {
            background: white;
            color: #000;
            font-weight: 400;
        }

        .flight-info-grid .time-large {
            font-size: 14pt;
            font-weight: bold;
            color: #000;
        }

        /* Additional spacing and density adjustments */
        .flight-info-grid th {
            padding: 4px 8px;
            background: #f5f5f5;
            font-weight: 600;
            color: #555;
            text-transform: uppercase;
            font-size: 13px;
            vertical-align: middle;
        }

        /* Ensure consistent table cell heights */
        .flight-info-grid td,
        .flight-info-grid th {
            height: 24px;
            line-height: 1.2;
        }

        /* Remove any rounded corners or modern styling */
        * {
            border-radius: 0 !important;
        }

        /* Monochrome status styling - no colors */
        .status-confirmed {
            color: #000;
            font-weight: 400;
            background: white;
            padding: 0;
            text-transform: uppercase;
            font-size: 13px;
        }

        /* Tiny disclaimer text at bottom */
        .disclaimer-text {
            font-size: 7pt;
            color: #666;
            text-align: center;
            margin-top: 20px;
            line-height: 1.2;
        }

        /* Route summary - authentic airline style */
        .route-summary {
            font-size: 11pt;
            font-weight: bold;
            color: #000;
            margin: 6px 0 5px 0;
            padding: 5px 0;
            background: #f0f0f0;
            border: 3px solid #000;
            border-left: 4px solid #000;
            text-transform: uppercase;
            padding-left: 8px;
        }

        /* Departure header - stark official style */
        .departure-header {
            font-size: 12pt;
            font-weight: bold;
            color: #000;
            margin: 6px 0 5px 0;
            padding: 5px 8px;
            background: #f0f0f0;
            border-bottom: 3px solid #000;
            text-transform: uppercase;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .departure-header .left-content {
            display: flex;
            align-items: center;
        }

        .departure-header .airplane {
            font-size: 14pt;
            margin-right: 8px;
        }

        .departure-header .verify-text {
            font-size: 9pt;
            font-weight: normal;
            color: #888888;
            text-transform: none;
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
        }



        @media print {
            body { margin: 0; padding: 15px; }
            .ticket-container { max-width: none; }
        }
    </style>
</head>
<body>
    <div class="ticket-container">
        <!-- Header - Authentic airline style -->
        <div class="header-large" style="text-align: left; margin-bottom: 12px; padding-bottom: 6px; border-bottom: 3px solid #000;">
            undefined &nbsp;&nbsp;&nbsp; MLA
        </div>

        <!-- Prepared For Section -->
        <div style="margin: 10px 0;">
            <div class="subheader" style="margin-bottom: 6px;">PREPARED FOR</div>
            
                <div class="subheader" style="margin: 2px 0;">undefined</div>
            
                <div class="subheader" style="margin: 2px 0;">undefined</div>
            
        </div>

        <!-- Reservation Codes -->
        <div style="margin: 10px 0;">
            <div class="body-text" style="margin: 2px 0;"><strong>STATUS:</strong> Confirmed</div>
            <div class="body-text" style="margin: 2px 0;"><strong>RESERVATION CODE:</strong> HTMLDEBUG</div>
            <div class="body-text" style="margin: 2px 0;"><strong>AIRLINE RESERVATION CODE:</strong> RYFLRF</div>
        </div>

        <!-- Flight Segments -->
        
            <!-- Route Summary -->
            <div class="route-summary">
                ROUTE MAN – MLA &nbsp;&nbsp;&nbsp; Duration: 3h 25m &nbsp;&nbsp;&nbsp; Stop(s): 0
            </div>

            <!-- Departure Header -->
            <div class="departure-header">
                <div class="left-content">
                    <span class="airplane">✈</span> DEPARTURE: MONDAY, JUL 21
                </div>
                <div class="verify-text">
                    Please verify flight times prior to departure
                </div>
            </div>

            <!-- Flight Segment Box with Left-Side Airline Branding -->
            <div class="flight-segment">
                <!-- Left-Side Airline Branding Panel -->
                <div class="airline-branding-panel">
                    <div style="text-align: center; margin-bottom: 8px;">
                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGCAMAAABG8BK2AAAAM1BMVEUAPY8uT4NvdGqNhl1QYnYYRompl0//yw7xwiG4oEh+fWTiuizUsTebjlZganE/WX3GqT+z2LLKAAACEUlEQVR4AezSi6rjMAwE0PFznMiJ/f9fu0UDXFg2rbcpz9vDI4EgMZGEFV9fXyGmFHN5pzLnDCmV0rY9YJGYl0Xv0unEjnM91aCoy9/6aqhIUZd/sBoHZKSICxvdxOi81ueDsV22mXTxbHxpBlxJpugLTTKeqRQtJ7tUOynSU8BzGyl26o4geT/mbOSc6dXmdXFywFW2AECXvCo2UuLPAQRgerNS5w6gbDPhmk5FKqSTVc2G1gg9Bq7tFLIVuKFXIw3I9MfgQ8G1TCEj5CCTwkSg61H9PLEQxyAwD9NJUzMDCtXtGcoBGf6aVWd6JHV7qtOdP+mG/4UpaQdKI5nw3EG3QTYvJBlVntfCIFOqNmHcPYapfFMYhX1qUvrQqIL/6OnlLSysSTKF3SfcgeB1uyYSqKt+qZKSgczoDbIOUF+ZsKA0SgWGjkaXy1NhOpZEUgIcGX2DU2E4sKZTNMnM5itjVpiERZmive48NGaFsYJVleIliQOICqPWyw7KASBpQeYdPdM6jVkRTj/KXQMaWCTDyIemMl3zSW5YJmVTH19MYNfIMv7bqTvsAcjcgUJOvCFMUj+WqW1FvCWpT8gGYGPDm3LjQ/f3xgPvCp0PUbc98LbSqXNO7LihmOJURtwxGmnAZMEtJ8kTNnHTQW7oEXd1MgzcNsgd92nbH2Ac+IDMhE+Yhk8IDR9R8PXr/dmgBQB6Hxy8I15ihQAAAABJRU5ErkJggg==" alt="RYANAIR" style="width: 50px; height: 30px; display: block; margin: 0 auto 4px auto;" />
                    </div>
                    <div class="airline-name-block">
                        <div class="airline-name">RYANAIR</div>
                        <div class="flight-number">FLIGHT</div>
                    </div>
                    <div class="flight-details">
                        <div>DURATION: 3h 25m</div>
                        <div>CLASS: Economy Class (M)</div>
                        <div>STATUS: Confirmed</div>
                    </div>
                </div>

                <!-- Flight Details Panel -->
                <div class="flight-details-panel">
                    <!-- Airport and Time Information Grid -->
                    <table class="flight-info-grid">
                        <tr>
                            <td class="info-label">MAN</td>
                            <td class="info-value"><strong>Manchester Airport</strong></td>
                            <td class="info-label">MLA</td>
                            <td class="info-value"><strong>Malta International Airport</strong></td>
                        </tr>
                        <tr>
                            <td class="info-label">DEPARTING AT:</td>
                            <td class="info-value">
                                <div class="time-large">TIME</div>
                                <div class="small-text">TERMINAL: 1</div>
                            </td>
                            <td class="info-label">ARRIVING AT:</td>
                            <td class="info-value">
                                <div class="time-large">TIME</div>
                                <div class="small-text">TERMINAL: 1</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="info-label">AIRCRAFT:</td>
                            <td class="info-value">BOEING 737-800</td>
                            <td class="info-label">DISTANCE (IN MILES):</td>
                            <td class="info-value">Not Available</td>
                        </tr>
                        <tr>
                            <td class="info-label">STOP(S):</td>
                            <td class="info-value">0</td>
                            <td class="info-label">MEALS:</td>
                            <td class="info-value">Not Available</td>
                        </tr>
                    </table>

                    <!-- Passenger Information -->
                    <div style="margin-top: 6px; border-top: 1px solid #222; padding-top: 6px;">
                        <table class="flight-info-grid">
                            <thead>
                                <tr style="background: #f0f0f0;">
                                    <th class="info-label" style="font-weight: bold;">PASSENGER NAME:</th>
                                    <th class="info-label" style="font-weight: bold;">SEATS:</th>
                                    <th class="info-label" style="font-weight: bold;">BOOKING:</th>
                                </tr>
                            </thead>
                            <tbody>
                                
                                    <tr>
                                        <td class="info-value"><strong>undefined</strong></td>
                                        <td class="info-value">Check-in required</td>
                                        <td class="info-value"><span class="status-confirmed">CONFIRMED</span></td>
                                    </tr>
                                
                                    <tr>
                                        <td class="info-value"><strong>undefined</strong></td>
                                        <td class="info-value">Check-in required</td>
                                        <td class="info-value"><span class="status-confirmed">CONFIRMED</span></td>
                                    </tr>
                                
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Route Summary -->
            <div class="route-summary">
                ROUTE MLA – MAN &nbsp;&nbsp;&nbsp; Duration: 3h 35m &nbsp;&nbsp;&nbsp; Stop(s): 0
            </div>

            <!-- Departure Header -->
            <div class="departure-header">
                <div class="left-content">
                    <span class="airplane">✈</span> RETURN: MONDAY, JUL 28
                </div>
                <div class="verify-text">
                    Please verify flight times prior to departure
                </div>
            </div>

            <!-- Flight Segment Box with Left-Side Airline Branding -->
            <div class="flight-segment">
                <!-- Left-Side Airline Branding Panel -->
                <div class="airline-branding-panel">
                    <div style="text-align: center; margin-bottom: 8px;">
                        <img src="data:image/png;base64,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" alt="EASYJET" style="width: 50px; height: 30px; display: block; margin: 0 auto 4px auto;" />
                    </div>
                    <div class="airline-name-block">
                        <div class="airline-name">EASYJET</div>
                        <div class="flight-number">FLIGHT</div>
                    </div>
                    <div class="flight-details">
                        <div>DURATION: 3h 35m</div>
                        <div>CLASS: Economy Class (M)</div>
                        <div>STATUS: Confirmed</div>
                    </div>
                </div>

                <!-- Flight Details Panel -->
                <div class="flight-details-panel">
                    <!-- Airport and Time Information Grid -->
                    <table class="flight-info-grid">
                        <tr>
                            <td class="info-label">MLA</td>
                            <td class="info-value"><strong>Malta International Airport</strong></td>
                            <td class="info-label">MAN</td>
                            <td class="info-value"><strong>Manchester Airport</strong></td>
                        </tr>
                        <tr>
                            <td class="info-label">DEPARTING AT:</td>
                            <td class="info-value">
                                <div class="time-large">TIME</div>
                                <div class="small-text">TERMINAL: 1</div>
                            </td>
                            <td class="info-label">ARRIVING AT:</td>
                            <td class="info-value">
                                <div class="time-large">TIME</div>
                                <div class="small-text">TERMINAL: 1</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="info-label">AIRCRAFT:</td>
                            <td class="info-value">AIRBUS A320</td>
                            <td class="info-label">DISTANCE (IN MILES):</td>
                            <td class="info-value">Not Available</td>
                        </tr>
                        <tr>
                            <td class="info-label">STOP(S):</td>
                            <td class="info-value">0</td>
                            <td class="info-label">MEALS:</td>
                            <td class="info-value">Not Available</td>
                        </tr>
                    </table>

                    <!-- Passenger Information -->
                    <div style="margin-top: 6px; border-top: 1px solid #222; padding-top: 6px;">
                        <table class="flight-info-grid">
                            <thead>
                                <tr style="background: #f0f0f0;">
                                    <th class="info-label" style="font-weight: bold;">PASSENGER NAME:</th>
                                    <th class="info-label" style="font-weight: bold;">SEATS:</th>
                                    <th class="info-label" style="font-weight: bold;">BOOKING:</th>
                                </tr>
                            </thead>
                            <tbody>
                                
                                    <tr>
                                        <td class="info-value"><strong>undefined</strong></td>
                                        <td class="info-value">Check-in required</td>
                                        <td class="info-value"><span class="status-confirmed">CONFIRMED</span></td>
                                    </tr>
                                
                                    <tr>
                                        <td class="info-value"><strong>undefined</strong></td>
                                        <td class="info-value">Check-in required</td>
                                        <td class="info-value"><span class="status-confirmed">CONFIRMED</span></td>
                                    </tr>
                                
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            

        <!-- Tiny disclaimer text at bottom -->
        


    </div>
</body>
</html>