# VerifiedOnward.com PDF System Deployment Guide

## 🚀 Production Deployment

### Prerequisites
- Node.js 18+ 
- PM2 for process management
- <PERSON>in<PERSON> for reverse proxy
- SSL certificate
- Domain name

### 1. Server Setup

#### Install Dependencies
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2

# Install Nginx
sudo apt install nginx -y

# Install Chromium for Puppeteer
sudo apt-get install -y chromium-browser
```

#### Clone Repository
```bash
git clone https://github.com/your-org/verifiedonward.com.git
cd verifiedonward.com
```

### 2. Backend Deployment

#### Install Dependencies
```bash
cd backend
npm ci --production
```

#### Environment Configuration
Create `.env` file:
```bash
NODE_ENV=production
PORT=3001
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
```

#### PM2 Configuration
Create `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'verifiedonward-backend',
    script: 'server.js',
    cwd: '/path/to/verifiedonward.com/backend',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: '/var/log/verifiedonward/backend-error.log',
    out_file: '/var/log/verifiedonward/backend-out.log',
    log_file: '/var/log/verifiedonward/backend.log'
  }]
};
```

#### Start Backend
```bash
# Create log directory
sudo mkdir -p /var/log/verifiedonward
sudo chown $USER:$USER /var/log/verifiedonward

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 3. Frontend Deployment

#### Build Frontend
```bash
cd frontend
npm ci
npm run build
```

#### Nginx Configuration
Create `/etc/nginx/sites-available/verifiedonward.com`:
```nginx
server {
    listen 80;
    server_name verifiedonward.com www.verifiedonward.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name verifiedonward.com www.verifiedonward.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    # Frontend static files
    location / {
        root /path/to/verifiedonward.com/frontend/dist;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Backend API proxy
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Increase timeout for PDF generation
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

#### Enable Site
```bash
sudo ln -s /etc/nginx/sites-available/verifiedonward.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 4. SSL Certificate

#### Using Certbot (Let's Encrypt)
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d verifiedonward.com -d www.verifiedonward.com
```

### 5. Monitoring & Logging

#### PM2 Monitoring
```bash
# View logs
pm2 logs verifiedonward-backend

# Monitor processes
pm2 monit

# Restart application
pm2 restart verifiedonward-backend
```

#### Nginx Logs
```bash
# Access logs
sudo tail -f /var/log/nginx/access.log

# Error logs
sudo tail -f /var/log/nginx/error.log
```

### 6. Performance Optimization

#### Puppeteer Optimization
Add to backend `.env`:
```bash
# Puppeteer optimizations
PUPPETEER_ARGS=--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage,--disable-accelerated-2d-canvas,--no-first-run,--no-zygote,--disable-gpu
```

#### Memory Management
```bash
# Increase Node.js memory limit
NODE_OPTIONS=--max-old-space-size=2048
```

### 7. Backup Strategy

#### Database Backup (if using)
```bash
# Create backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --out /backups/mongodb_$DATE
```

#### File Backup
```bash
# Backup generated PDFs
rsync -av /path/to/backend/temp/ /backups/pdfs/
```

### 8. Health Checks

#### Create Health Check Script
```bash
#!/bin/bash
# health-check.sh

# Check backend
BACKEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/api/tickets/health)
if [ $BACKEND_STATUS -ne 200 ]; then
    echo "Backend health check failed: $BACKEND_STATUS"
    pm2 restart verifiedonward-backend
fi

# Check frontend
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://verifiedonward.com)
if [ $FRONTEND_STATUS -ne 200 ]; then
    echo "Frontend health check failed: $FRONTEND_STATUS"
    sudo systemctl reload nginx
fi
```

#### Setup Cron Job
```bash
# Add to crontab
*/5 * * * * /path/to/health-check.sh >> /var/log/health-check.log 2>&1
```

### 9. Security Hardening

#### Firewall Setup
```bash
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

#### Fail2Ban
```bash
sudo apt install fail2ban
sudo systemctl enable fail2ban
```

### 10. CDN Setup (Optional)

#### CloudFlare Configuration
1. Add domain to CloudFlare
2. Update DNS records
3. Enable SSL/TLS encryption
4. Configure caching rules for static assets

### 11. Troubleshooting

#### Common Issues

**Puppeteer Crashes**
```bash
# Check Chrome installation
which chromium-browser

# Increase memory limits
echo 'vm.max_map_count=262144' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

**PDF Generation Timeouts**
```bash
# Increase Nginx timeout
proxy_read_timeout 600s;
proxy_connect_timeout 600s;
```

**High Memory Usage**
```bash
# Monitor memory
htop
free -h

# Restart PM2 processes
pm2 restart all
```

### 12. Maintenance

#### Regular Updates
```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Update Node.js dependencies
cd backend && npm audit fix
cd frontend && npm audit fix

# Restart services
pm2 restart all
sudo systemctl reload nginx
```

#### Log Rotation
```bash
# Setup logrotate for application logs
sudo nano /etc/logrotate.d/verifiedonward
```

```
/var/log/verifiedonward/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reloadLogs
    endscript
}
```

---

## 🚨 Emergency Procedures

### Quick Rollback
```bash
# Stop current version
pm2 stop verifiedonward-backend

# Deploy previous version
git checkout previous-stable-tag
cd backend && npm ci --production
pm2 start ecosystem.config.js
```

### Service Recovery
```bash
# Full service restart
pm2 restart all
sudo systemctl restart nginx
sudo systemctl restart fail2ban
```

---

**For support during deployment: <EMAIL>**
