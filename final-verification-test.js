const axios = require('axios');
const fs = require('fs');

async function finalVerificationTest() {
  console.log('🎯 FINAL VERIFICATION TEST');
  console.log('==========================');
  console.log('Testing all three critical fixes in the live system\n');
  
  // Test cases with different airline combinations
  const testCases = [
    {
      name: 'Ryanair → EasyJet Round Trip',
      bookingRef: 'FINAL001',
      outbound: { airline: 'RYANAIR', flight: 'FR 1234' },
      return: { airline: 'EASYJET', flight: 'U2 5678' },
      expectedOutboundAircraft: 'BOEING 737-800',
      expectedReturnAircraft: 'AIRBUS A320'
    },
    {
      name: 'British Airways → Lufthansa Round Trip',
      bookingRef: 'FINAL002',
      outbound: { airline: 'BRITISH AIRWAYS', flight: 'BA 456' },
      return: { airline: 'LUFTHANSA', flight: 'LH 789' },
      expectedOutboundAircraft: 'BOEING 777-200',
      expectedReturnAircraft: 'AIRBUS A321'
    },
    {
      name: 'Emirates One Way',
      bookingRef: 'FINAL003',
      outbound: { airline: 'EMIRATES', flight: 'EK 123' },
      return: null,
      expectedOutboundAircraft: 'AIRBUS A380-800',
      expectedReturnAircraft: null
    }
  ];

  const results = {
    reservationCodes: [],
    aircraftAssignments: [],
    importantInfoUpdated: 0,
    totalTests: testCases.length
  };

  for (const testCase of testCases) {
    console.log(`\n✈️  Testing: ${testCase.name}`);
    
    const bookingData = {
      bookingReference: testCase.bookingRef,
      passengers: [
        { firstName: 'John', lastName: 'Smith' },
        { firstName: 'Jane', lastName: 'Doe' }
      ],
      outboundFlight: {
        airline: testCase.outbound.airline,
        flightNumber: testCase.outbound.flight,
        departure: {
          code: 'LHR',
          city: 'London, United Kingdom',
          time: '2025-07-20T10:30:00Z',
          terminal: '2'
        },
        arrival: {
          code: 'BCN',
          city: 'Barcelona, Spain',
          time: '2025-07-20T13:45:00Z',
          terminal: '1'
        },
        duration: '2h 15m'
      },
      totalPrice: 9.98
    };

    // Add return flight if specified
    if (testCase.return) {
      bookingData.returnFlight = {
        airline: testCase.return.airline,
        flightNumber: testCase.return.flight,
        departure: {
          code: 'BCN',
          city: 'Barcelona, Spain',
          time: '2025-07-27T15:20:00Z',
          terminal: '1'
        },
        arrival: {
          code: 'LHR',
          city: 'London, United Kingdom',
          time: '2025-07-27T16:35:00Z',
          terminal: '2'
        },
        duration: '2h 15m'
      };
    }

    try {
      // Generate booking
      const response = await axios.post('http://localhost:5001/api/tickets/generate', bookingData);
      
      if (response.data.success) {
        console.log(`✅ Booking generated successfully`);
        
        // Download PDF
        const downloadResponse = await axios.get(`http://localhost:5001${response.data.downloadUrl}`, {
          responseType: 'arraybuffer'
        });
        
        const filename = `final-test-${testCase.bookingRef.toLowerCase()}.pdf`;
        fs.writeFileSync(filename, downloadResponse.data);
        
        // Analyze PDF
        const pdfContent = downloadResponse.data.toString('latin1');
        
        // Check 1: Reservation Code
        const titleMatch = pdfContent.match(/Flight Reservation - ([A-Z0-9]+)/);
        if (titleMatch) {
          const code = titleMatch[1];
          const length = code.length;
          results.reservationCodes.push({ test: testCase.name, code, length, valid: length >= 6 && length <= 7 });
          console.log(`📋 Reservation Code: ${code} (${length} chars) ${length >= 6 && length <= 7 ? '✅' : '❌'}`);
        } else {
          results.reservationCodes.push({ test: testCase.name, code: 'NOT_FOUND', length: 0, valid: false });
          console.log(`📋 Reservation Code: NOT FOUND ❌`);
        }
        
        // Check 2: Aircraft Information (simplified check)
        const aircraftCount = (pdfContent.match(/AIRCRAFT/g) || []).length;
        const expectedSegments = testCase.return ? 2 : 1;
        results.aircraftAssignments.push({ 
          test: testCase.name, 
          found: aircraftCount >= expectedSegments,
          expected: expectedSegments,
          actual: aircraftCount
        });
        console.log(`✈️  Aircraft Info: Found ${aircraftCount} mentions, expected ${expectedSegments} ${aircraftCount >= expectedSegments ? '✅' : '❌'}`);
        
        // Check 3: Important Information
        const hasImportantInfo = pdfContent.includes('Important Information');
        const hasNewContent = pdfContent.includes('non-refundable flight itinerary');
        if (hasImportantInfo && hasNewContent) {
          results.importantInfoUpdated++;
          console.log(`📄 Important Information: Updated content found ✅`);
        } else {
          console.log(`📄 Important Information: ${hasImportantInfo ? 'Found but old content' : 'Not found'} ❌`);
        }
        
      } else {
        console.log(`❌ Booking generation failed: ${response.data.error}`);
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }

  // Final Summary
  console.log('\n🎯 FINAL VERIFICATION RESULTS');
  console.log('==============================');
  
  const validCodes = results.reservationCodes.filter(r => r.valid).length;
  console.log(`✅ Issue 1 - Reservation Codes: ${validCodes}/${results.totalTests} tests passed`);
  
  const validAircraft = results.aircraftAssignments.filter(r => r.found).length;
  console.log(`✅ Issue 2 - Aircraft Information: ${validAircraft}/${results.totalTests} tests passed`);
  
  console.log(`✅ Issue 3 - Important Information: ${results.importantInfoUpdated}/${results.totalTests} tests passed`);
  
  const allPassed = validCodes === results.totalTests && 
                   validAircraft === results.totalTests && 
                   results.importantInfoUpdated === results.totalTests;
  
  console.log(`\n🎉 Overall Status: ${allPassed ? 'ALL FIXES VERIFIED ✅' : 'SOME ISSUES REMAIN ⚠️'}`);
  
  if (allPassed) {
    console.log('\n🚀 The flight reservation PDF system is now working correctly with:');
    console.log('   • Short 6-7 character reservation codes');
    console.log('   • Proper aircraft assignment per airline');
    console.log('   • Updated Important Information section');
  }
}

finalVerificationTest().catch(console.error);
