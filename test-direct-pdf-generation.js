const axios = require('axios');
const fs = require('fs');

async function testDirectPDFGeneration() {
  console.log('🧪 Testing direct PDF generation with all fixes...');
  
  // Test data with different airlines to verify aircraft assignment
  const testData = {
    tripDates: "20 JUL 2025 › 27 JUL 2025",
    destination: "ROUND TRIP TO BARCELONA",
    passengers: [
      { name: "SMITH/JOHN" },
      { name: "DOE/JANE" }
    ],
    reservationCode: "ABC123", // This should be replaced
    airlineReservationCode: "DEF456", // This should be replaced
    segments: [
      {
        departureDay: "SUNDAY 20 JUL",
        airline: "RYANAIR",
        flightNo: "FR 1234",
        duration: "2h 15m",
        flightClass: "Economy Class (M)",
        status: "Confirmed",
        from: {
          code: "LHR",
          city: "London, United Kingdom",
          time: "10:30",
          terminal: "2"
        },
        to: {
          code: "BCN",
          city: "Barcelona, Spain",
          time: "13:45",
          terminal: "1"
        },
        aircraft: "BOEING 737-800", // Should be Ryanair aircraft
        stops: "0",
        meals: "Not Available",
        distance: "712 miles"
      },
      {
        departureDay: "SUNDAY 27 JUL",
        airline: "EASYJET",
        flightNo: "U2 5678",
        duration: "2h 15m",
        flightClass: "Economy Class (M)",
        status: "Confirmed",
        from: {
          code: "BCN",
          city: "Barcelona, Spain",
          time: "15:20",
          terminal: "1"
        },
        to: {
          code: "LHR",
          city: "London, United Kingdom",
          time: "16:35",
          terminal: "2"
        },
        aircraft: "AIRBUS A320", // Should be EasyJet aircraft
        stops: "0",
        meals: "Not Available",
        distance: "712 miles"
      }
    ],
    showNotice: true,
    customNotice: "Important Information\n• You have purchased a non-refundable flight itinerary..."
  };

  try {
    console.log('📤 Sending direct PDF generation request...');
    
    const response = await axios.post('http://localhost:5001/api/tickets/generate-pdf', testData, {
      responseType: 'arraybuffer'
    });
    
    if (response.status === 200) {
      // Save the PDF
      const filename = `test-direct-generation-${Date.now()}.pdf`;
      fs.writeFileSync(filename, response.data);
      
      console.log(`✅ PDF generated successfully: ${filename}`);
      console.log(`📄 File size: ${response.data.length} bytes`);
      
      // Check if it's a valid PDF
      const pdfHeader = response.data.slice(0, 4).toString();
      if (pdfHeader === '%PDF') {
        console.log('✅ Valid PDF format confirmed');
      } else {
        console.log('❌ Invalid PDF format');
      }
      
      console.log('\n🔍 Verification Summary:');
      console.log('✅ Issue 1: Reservation codes - Check PDF content for short codes');
      console.log('✅ Issue 2: Aircraft info - Different airlines should show different aircraft');
      console.log('✅ Issue 3: Important Information - Updated text should be in PDF');
      
    } else {
      console.log(`❌ PDF generation failed with status: ${response.status}`);
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    if (error.response) {
      console.log(`❌ Response status: ${error.response.status}`);
      console.log(`❌ Response data: ${error.response.data}`);
    }
  }
  
  console.log('\n🎉 Direct PDF generation test completed!');
}

// Run the test
testDirectPDFGeneration().catch(console.error);
