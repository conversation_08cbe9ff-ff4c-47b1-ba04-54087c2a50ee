const fetch = require('node-fetch').default || require('node-fetch');
const fs = require('fs');

const BASE_URL = 'http://localhost:5001';

// Test cases to verify all PDF requirements
const testCases = [
  {
    name: "✅ DEPARTURE/RETURN Labels Test",
    description: "Verify first segment shows 'DEPARTURE:' and subsequent show 'RETURN:'",
    bookingReference: "DEP001",
    passengers: [{ firstName: "JOHN", lastName: "SMITH" }],
    outboundFlight: {
      airline: "easyJet",
      flightNumber: "U2 2273",
      duration: "3h 30m",
      aircraft: "Boeing 737-800",
      departure: { code: "MAN", city: "Manchester Airport", time: "2025-07-21T05:55:00Z", terminal: "1" },
      arrival: { code: "MLA", city: "Malta International Airport", time: "2025-07-21T10:25:00Z", terminal: "1" }
    },
    returnFlight: {
      airline: "Ryanair",
      flightNumber: "FR 5210",
      duration: "3h 30m",
      aircraft: "Boeing 737-800",
      departure: { code: "MLA", city: "Malta International Airport", time: "2025-07-28T22:00:00Z", terminal: "1" },
      arrival: { code: "MAN", city: "Manchester Airport", time: "2025-07-29T00:30:00Z", terminal: "1" }
    },
    totalPrice: 9.99
  },
  {
    name: "✅ Short Reservation Codes Test",
    description: "Verify booking reference is 6-7 characters",
    bookingReference: "ABC123", // 6 characters
    passengers: [{ firstName: "MARY", lastName: "JONES" }],
    outboundFlight: {
      airline: "British Airways",
      flightNumber: "BA 1407",
      duration: "2h 45m",
      aircraft: "Airbus A320",
      departure: { code: "LHR", city: "London Heathrow", time: "2025-07-25T14:30:00Z", terminal: "5" },
      arrival: { code: "CDG", city: "Paris Charles de Gaulle", time: "2025-07-25T17:15:00Z", terminal: "2A" }
    },
    totalPrice: 12.99
  },
  {
    name: "✅ Passenger Name Format Test",
    description: "Verify names appear as 'LASTNAME/FIRSTNAME' without titles",
    bookingReference: "NAME01",
    passengers: [
      { firstName: "HUDIFA", lastName: "MISRATI" },
      { firstName: "BOSHRA", lastName: "ELBAKOURY" }
    ],
    outboundFlight: {
      airline: "easyJet",
      flightNumber: "U2 2273",
      duration: "3h 30m",
      aircraft: "Boeing 737-800",
      departure: { code: "MAN", city: "Manchester Airport", time: "2025-07-21T05:55:00Z", terminal: "1" },
      arrival: { code: "MLA", city: "Malta International Airport", time: "2025-07-21T10:25:00Z", terminal: "1" }
    },
    totalPrice: 4.99
  },
  {
    name: "✅ Airline Logo Display Test",
    description: "Verify correct logos for different airlines",
    bookingReference: "LOGO01",
    passengers: [{ firstName: "TEST", lastName: "USER" }],
    outboundFlight: {
      airline: "Ryanair",
      flightNumber: "FR 5210",
      duration: "3h 30m",
      aircraft: "Boeing 737-800",
      departure: { code: "DUB", city: "Dublin Airport", time: "2025-07-21T08:00:00Z", terminal: "1" },
      arrival: { code: "STN", city: "London Stansted", time: "2025-07-21T09:30:00Z", terminal: "1" }
    },
    totalPrice: 19.99
  }
];

async function testPDFGeneration(testCase) {
  console.log(`\n🧪 ${testCase.name}`);
  console.log(`📝 ${testCase.description}`);
  console.log(`🎫 Booking Reference: ${testCase.bookingReference}`);
  
  try {
    // Generate PDF
    const response = await fetch(`${BASE_URL}/api/tickets/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testCase)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`✅ PDF generated successfully`);

    // Download PDF
    const downloadResponse = await fetch(`${BASE_URL}/api/tickets/download/${testCase.bookingReference}`);
    if (!downloadResponse.ok) {
      throw new Error(`Download failed: ${downloadResponse.status}`);
    }

    const pdfBuffer = await downloadResponse.arrayBuffer();
    const filename = `test-${testCase.bookingReference.toLowerCase()}.pdf`;
    fs.writeFileSync(filename, Buffer.from(pdfBuffer));
    
    const stats = fs.statSync(filename);
    console.log(`📄 PDF saved: ${filename} (${(stats.size / 1024).toFixed(1)} KB)`);

    return { success: true, filename, testCase };

  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    return { success: false, error: error.message, testCase };
  }
}

async function verifyCodeGeneration() {
  console.log('\n🔍 TESTING RESERVATION CODE GENERATION');
  console.log('=====================================');
  
  // Test multiple code generations to verify length
  const codes = [];
  for (let i = 0; i < 10; i++) {
    const testData = {
      bookingReference: `TEST${i.toString().padStart(2, '0')}`,
      passengers: [{ firstName: "TEST", lastName: "USER" }],
      outboundFlight: {
        airline: "Test Airlines",
        flightNumber: "TA 123",
        duration: "2h 00m",
        aircraft: "Test Aircraft",
        departure: { code: "TST", city: "Test City", time: "2025-07-21T12:00:00Z", terminal: "1" },
        arrival: { code: "DST", city: "Destination City", time: "2025-07-21T14:00:00Z", terminal: "1" }
      },
      totalPrice: 1.00
    };

    try {
      const response = await fetch(`${BASE_URL}/api/tickets/generate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData)
      });

      if (response.ok) {
        codes.push(testData.bookingReference);
      }
    } catch (error) {
      // Continue with next test
    }
  }

  console.log('\n📊 Generated Booking References:');
  codes.forEach(code => {
    const length = code.length;
    const status = (length >= 6 && length <= 7) ? '✅' : '❌';
    console.log(`  ${status} ${code} (${length} characters)`);
  });
}

async function runVerification() {
  console.log('🔍 PDF REQUIREMENTS VERIFICATION');
  console.log('================================');
  
  const results = [];
  
  // Test each requirement
  for (const testCase of testCases) {
    const result = await testPDFGeneration(testCase);
    results.push(result);
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Test code generation
  await verifyCodeGeneration();

  console.log('\n📊 VERIFICATION SUMMARY');
  console.log('======================');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful Tests: ${successful.length}/${results.length}`);
  console.log(`❌ Failed Tests: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n📄 Generated Test PDFs:');
    successful.forEach(result => {
      console.log(`  • ${result.filename} - ${result.testCase.name}`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Failed Tests:');
    failed.forEach(result => {
      console.log(`  • ${result.testCase.bookingReference}: ${result.error}`);
    });
  }

  console.log('\n🔍 MANUAL VERIFICATION CHECKLIST:');
  console.log('=================================');
  console.log('Open each generated PDF and verify:');
  console.log('');
  console.log('1. ✅ DEPARTURE/RETURN Labels:');
  console.log('   - First flight segment shows "DEPARTURE: [DATE]"');
  console.log('   - Return flight segment shows "RETURN: [DATE]"');
  console.log('');
  console.log('2. ✅ Short Reservation Codes:');
  console.log('   - All booking references are 6-7 characters');
  console.log('   - Format: ABC123, NHG8IQ, A1B2C3, etc.');
  console.log('');
  console.log('3. ✅ Passenger Name Format:');
  console.log('   - Names appear as "MISRATI/HUDIFA"');
  console.log('   - No titles (MR., MRS., etc.)');
  console.log('   - All uppercase');
  console.log('');
  console.log('4. ✅ Airline Logo Display:');
  console.log('   - easyJet shows U2 logo');
  console.log('   - Ryanair shows FR logo');
  console.log('   - British Airways shows BA logo');
  console.log('   - Logos are centered above airline names');
  console.log('');
  console.log('5. ✅ Authentic Styling:');
  console.log('   - Monochrome design with black borders');
  console.log('   - High visual density');
  console.log('   - ALL-CAPS headers');
  console.log('   - Tight rectangular boxes');
  console.log('   - Professional airline reservation appearance');
  
  return results;
}

// Run the verification
runVerification()
  .then(results => {
    console.log('\n🎯 Verification complete!');
    console.log('📋 Please manually review the generated PDFs to confirm all requirements are met.');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Verification failed:', error);
    process.exit(1);
  });
