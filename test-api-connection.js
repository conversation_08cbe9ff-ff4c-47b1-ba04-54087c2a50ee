// Simple test script to verify API connection
const axios = require('axios');

async function testAPIConnection() {
  console.log('🔗 Testing API connection...');
  
  try {
    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get('http://localhost:5001/api/health');
    console.log('✅ Health check:', healthResponse.data);
    
    // Test airport search
    console.log('2. Testing airport search...');
    const airportResponse = await axios.get('http://localhost:5001/api/flights/airports', {
      params: { query: 'London' }
    });
    console.log('✅ Airport search:', airportResponse.data.success ? 'Success' : 'Failed');
    
    // Test flight search
    console.log('3. Testing flight search...');
    const flightResponse = await axios.post('http://localhost:5001/api/flights/search', {
      origin: 'LHR',
      destination: 'JFK',
      date: '2025-07-14',
      tripType: 'oneWay'
    });
    console.log('✅ Flight search:', flightResponse.data.success ? 'Success' : 'Failed');
    console.log('📊 Flight count:', flightResponse.data.data?.flights?.length || 0);
    
    console.log('🎉 All API tests passed!');
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error('📋 Response status:', error.response.status);
      console.error('📋 Response data:', error.response.data);
    }
  }
}

testAPIConnection();
