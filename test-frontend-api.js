// Test frontend API connectivity
const testFrontendAPI = async () => {
  console.log('🧪 Testing frontend API connectivity...');
  
  try {
    // Test direct API call (as frontend would make)
    console.log('\n🔗 Step 1: Testing direct API call...');
    const response = await fetch('http://localhost:5173/api/flights/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        origin: 'LHR',
        destination: 'JFK',
        date: '2025-08-15',
        tripType: 'oneWay'
      })
    });
    
    const data = await response.json();
    console.log('✅ Frontend API proxy:', data.success ? 'SUCCESS' : 'FAILED');
    
    if (data.success && data.data.flights.length > 0) {
      console.log('✅ Found', data.data.flights.length, 'flights via frontend proxy');
      console.log('✅ First flight:', data.data.flights[0].airline.name, data.data.flights[0].flight.number);
    }
    
    // Test payment intent creation via frontend
    console.log('\n💳 Step 2: Testing payment intent via frontend...');
    const paymentResponse = await fetch('http://localhost:5173/api/payments/stripe/create-intent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: 4.99,
        currency: 'usd',
        metadata: {
          description: 'Test payment'
        }
      })
    });
    
    const paymentData = await paymentResponse.json();
    console.log('✅ Payment intent via frontend:', paymentData.success ? 'SUCCESS' : 'FAILED');
    
    console.log('\n🎉 Frontend API connectivity test: SUCCESS');
    console.log('📋 Summary:');
    console.log('  ✅ Vite proxy working correctly');
    console.log('  ✅ Flight search API accessible from frontend');
    console.log('  ✅ Payment API accessible from frontend');
    console.log('  ✅ Ready for user testing');
    
    return true;

  } catch (error) {
    console.error('❌ Frontend API test failed:', error);
    return false;
  }
};

// Run the test
testFrontendAPI().then(success => {
  if (success) {
    console.log('\n🎯 Frontend is ready! You can now test the complete checkout flow at http://localhost:5173');
  } else {
    console.log('\n🚨 Frontend API issues detected. Please check the logs above.');
  }
  process.exit(success ? 0 : 1);
});
