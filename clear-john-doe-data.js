// Clear John <PERSON> test data from localStorage
console.log('🧹 Clearing John Doe test data from localStorage...');

// Check current data
const passengers = localStorage.getItem('bookingPassengers');
const email = localStorage.getItem('bookingEmail');

console.log('Current passengers:', passengers);
console.log('Current email:', email);

let cleared = [];

// Check and clear John/Doe passenger data
if (passengers) {
  try {
    const parsedPassengers = JSON.parse(passengers);
    const hasJohnDoeData = parsedPassengers.some(p => 
      p.firstName === 'John' || 
      p.lastName === 'Doe' ||
      p.firstName === 'Jane' ||
      p.lastName === 'Smith'
    );
    if (hasJohnDoeData) {
      localStorage.removeItem('bookingPassengers');
      cleared.push('John/Doe passenger data');
      console.log('✅ Removed John/Doe passenger data');
    }
  } catch (e) {
    console.error('Error parsing passengers:', e);
  }
}

// Check <NAME_EMAIL> email
if (email && (email === '<EMAIL>' || email === '<EMAIL>')) {
  localStorage.removeItem('bookingEmail');
  cleared.push('<PERSON> Doe email data');
  console.log('✅ Removed John Doe email data');
}

if (cleared.length > 0) {
  console.log('🎉 Cleared:', cleared.join(', '));
  console.log('🔄 Please refresh the page to see empty fields');
} else {
  console.log('ℹ️ No John Doe test data found to clear');
}

// Also clear any other booking data to ensure clean state
const allBookingKeys = [
  'bookingSearchData',
  'bookingSelectedFlight', 
  'bookingSelectedOutboundFlight',
  'bookingSelectedReturnFlight',
  'bookingPaymentMethod',
  'bookingReference',
  'bookingPaymentId',
  'bookingStep',
  'bookingTripType'
];

console.log('🧹 Clearing all booking data for clean state...');
allBookingKeys.forEach(key => {
  if (localStorage.getItem(key)) {
    localStorage.removeItem(key);
    console.log(`✅ Cleared ${key}`);
  }
});

console.log('✨ All test data cleared! Refresh the page to see empty fields.');
