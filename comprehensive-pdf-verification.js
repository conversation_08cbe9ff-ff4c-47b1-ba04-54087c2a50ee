const fetch = require('node-fetch').default || require('node-fetch');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5001';

// Test data for comprehensive verification
const testCases = [
  {
    name: "One-way EasyJet Flight",
    bookingReference: "NHG8IQ",
    passengers: [
      { firstName: "HUDIFA", lastName: "MISRATI" },
      { firstName: "BOSHRA", lastName: "ELBAKOURY" }
    ],
    outboundFlight: {
      airline: "easyJet",
      flightNumber: "U2 2273",
      duration: "3h 30m",
      aircraft: "Boeing 737-800",
      departure: {
        code: "MAN",
        city: "Manchester Airport",
        time: "2025-07-21T05:55:00Z",
        terminal: "1"
      },
      arrival: {
        code: "MLA",
        city: "Malta International Airport",
        time: "2025-07-21T10:25:00Z",
        terminal: "1"
      }
    },
    totalPrice: 4.99
  },
  {
    name: "Round-trip Ryanair Flight",
    bookingReference: "A1B2C3",
    passengers: [
      { firstName: "HUDIFA", lastName: "MISRATI" },
      { firstName: "BOSHRA", lastName: "ELBAKOURY" }
    ],
    outboundFlight: {
      airline: "Ryanair",
      flightNumber: "FR 5210",
      duration: "3h 30m",
      aircraft: "Boeing 737-800",
      departure: {
        code: "MLA",
        city: "Malta International Airport",
        time: "2025-07-28T22:00:00Z",
        terminal: "1"
      },
      arrival: {
        code: "MAN",
        city: "Manchester Airport",
        time: "2025-07-29T00:30:00Z",
        terminal: "1"
      }
    },
    returnFlight: {
      airline: "Ryanair",
      flightNumber: "FR 5211",
      duration: "3h 30m",
      aircraft: "Boeing 737-800",
      departure: {
        code: "MAN",
        city: "Manchester Airport",
        time: "2025-08-05T06:00:00Z",
        terminal: "1"
      },
      arrival: {
        code: "MLA",
        city: "Malta International Airport",
        time: "2025-08-05T09:30:00Z",
        terminal: "1"
      }
    },
    totalPrice: 9.99
  },
  {
    name: "British Airways Flight",
    bookingReference: "BA7890",
    passengers: [
      { firstName: "JOHN", lastName: "SMITH" }
    ],
    outboundFlight: {
      airline: "British Airways",
      flightNumber: "BA 1407",
      duration: "2h 45m",
      aircraft: "Airbus A320",
      departure: {
        code: "LHR",
        city: "London Heathrow",
        time: "2025-07-25T14:30:00Z",
        terminal: "5"
      },
      arrival: {
        code: "CDG",
        city: "Paris Charles de Gaulle",
        time: "2025-07-25T17:15:00Z",
        terminal: "2A"
      }
    },
    totalPrice: 12.99
  }
];

async function generateAndVerifyPDF(testCase) {
  console.log(`\n🎯 Testing: ${testCase.name}`);
  console.log(`📋 Booking Reference: ${testCase.bookingReference}`);
  
  try {
    // Generate PDF
    const response = await fetch(`${BASE_URL}/api/tickets/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testCase)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`✅ PDF generated successfully`);

    // Download PDF
    const downloadResponse = await fetch(`${BASE_URL}/api/tickets/download/${testCase.bookingReference}`);
    if (!downloadResponse.ok) {
      throw new Error(`Download failed: ${downloadResponse.status}`);
    }

    const pdfBuffer = await downloadResponse.buffer();
    const filename = `verification-${testCase.bookingReference.toLowerCase()}.pdf`;
    fs.writeFileSync(filename, pdfBuffer);
    
    const stats = fs.statSync(filename);
    console.log(`📄 PDF saved: ${filename} (${(stats.size / 1024).toFixed(1)} KB)`);

    return {
      success: true,
      filename,
      size: stats.size,
      bookingReference: testCase.bookingReference
    };

  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    return {
      success: false,
      error: error.message,
      bookingReference: testCase.bookingReference
    };
  }
}

async function runComprehensiveVerification() {
  console.log('🔍 COMPREHENSIVE PDF VERIFICATION TEST');
  console.log('=====================================');
  
  const results = [];
  
  for (const testCase of testCases) {
    const result = await generateAndVerifyPDF(testCase);
    results.push(result);
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n📊 VERIFICATION SUMMARY');
  console.log('======================');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n📄 Generated PDFs:');
    successful.forEach(result => {
      console.log(`  • ${result.filename} (${result.bookingReference}) - ${(result.size / 1024).toFixed(1)} KB`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Failed Tests:');
    failed.forEach(result => {
      console.log(`  • ${result.bookingReference}: ${result.error}`);
    });
  }

  console.log('\n🔍 MANUAL VERIFICATION CHECKLIST:');
  console.log('=================================');
  console.log('[ ] DEPARTURE/RETURN Labels: First segment shows "DEPARTURE:", subsequent show "RETURN:"');
  console.log('[ ] Short Reservation Codes: All booking references are 6-7 characters (NHG8IQ, A1B2C3, etc.)');
  console.log('[ ] Passenger Name Format: Names appear as "SMITH/JOHN" without titles');
  console.log('[ ] Airline Logo Display: Correct logos for each airline (easyJet=U2, Ryanair=FR, BA=BA)');
  console.log('[ ] Authentic Styling: Monochrome design, tight borders, high density, ALL-CAPS headers');
  console.log('[ ] No Important Information: Section removed or minimized');
  console.log('[ ] Proper Branding: Airline logos centered above airline names');
  
  console.log('\n📋 Next Steps:');
  console.log('1. Open each generated PDF file');
  console.log('2. Compare against provided sample format');
  console.log('3. Verify all checklist items are implemented');
  console.log('4. Test end-to-end user flow if needed');
  
  return results;
}

// Run the verification
runComprehensiveVerification()
  .then(results => {
    console.log('\n🎯 Verification complete!');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Verification failed:', error);
    process.exit(1);
  });
