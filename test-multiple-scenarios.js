const axios = require('axios');
const fs = require('fs');

async function testMultipleScenarios() {
  console.log('🧪 TESTING MULTIPLE SCENARIOS');
  console.log('==============================');
  console.log('Testing both fixes across different flight configurations\n');
  
  const testCases = [
    {
      name: 'One Way Flight',
      filename: 'one-way-fixes-test.pdf',
      data: {
        bookingReference: 'ONEWAY',
        passengers: [{ firstName: 'John', lastName: 'Smith' }],
        outboundFlight: {
          airline: 'BRITISH AIRWAYS',
          flightNumber: 'BA 456',
          departure: { code: 'LHR', city: 'London Heathrow', time: '2025-07-21T08:00:00Z', terminal: '5' },
          arrival: { code: 'JFK', city: 'New York JFK', time: '2025-07-21T16:30:00Z', terminal: '7' },
          duration: '8h 30m'
        },
        totalPrice: 599.99
      }
    },
    {
      name: 'Round Trip - Long Route Names',
      filename: 'round-trip-long-names-test.pdf',
      data: {
        bookingReference: 'LONGNAMES',
        passengers: [{ firstName: 'Jane', lastName: 'Doe' }],
        outboundFlight: {
          airline: 'EMIRATES',
          flightNumber: 'EK 123',
          departure: { code: 'DXB', city: 'Dubai International Airport', time: '2025-07-22T14:20:00Z', terminal: '3' },
          arrival: { code: 'LAX', city: 'Los Angeles International Airport', time: '2025-07-22T18:45:00Z', terminal: 'B' },
          duration: '16h 25m'
        },
        returnFlight: {
          airline: 'AMERICAN AIRLINES',
          flightNumber: 'AA 789',
          departure: { code: 'LAX', city: 'Los Angeles International Airport', time: '2025-07-30T22:15:00Z', terminal: '4' },
          arrival: { code: 'DXB', city: 'Dubai International Airport', time: '2025-07-31T19:30:00Z', terminal: '3' },
          duration: '15h 15m'
        },
        totalPrice: 1299.99
      }
    },
    {
      name: 'Budget Airlines - Short Names',
      filename: 'budget-short-names-test.pdf',
      data: {
        bookingReference: 'BUDGET',
        passengers: [{ firstName: 'Mike', lastName: 'Johnson' }],
        outboundFlight: {
          airline: 'RYANAIR',
          flightNumber: 'FR 1234',
          departure: { code: 'STN', city: 'London Stansted', time: '2025-07-23T06:30:00Z', terminal: '1' },
          arrival: { code: 'BCN', city: 'Barcelona', time: '2025-07-23T09:45:00Z', terminal: '2' },
          duration: '2h 15m'
        },
        returnFlight: {
          airline: 'EASYJET',
          flightNumber: 'U2 5678',
          departure: { code: 'BCN', city: 'Barcelona', time: '2025-07-30T20:10:00Z', terminal: '1' },
          arrival: { code: 'LGW', city: 'London Gatwick', time: '2025-07-30T21:25:00Z', terminal: 'S' },
          duration: '2h 15m'
        },
        totalPrice: 89.98
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n✈️  Testing: ${testCase.name}`);
    
    try {
      const response = await axios.post('http://localhost:5001/api/tickets/generate', testCase.data);
      
      if (response.data.success) {
        console.log('✅ Booking generated successfully');
        
        // Download the PDF
        const downloadResponse = await axios.get(`http://localhost:5001${response.data.downloadUrl}`, {
          responseType: 'arraybuffer'
        });
        
        fs.writeFileSync(testCase.filename, downloadResponse.data);
        console.log(`📄 PDF saved: ${testCase.filename}`);
        
        // Check if it's round trip or one way
        const hasReturn = testCase.data.returnFlight !== undefined;
        console.log(`📋 Flight type: ${hasReturn ? 'Round Trip' : 'One Way'}`);
        console.log(`📋 Expected verification text instances: ${hasReturn ? '2' : '1'}`);
        console.log(`📋 Expected Important Information section: NONE (removed)`);
        
      } else {
        console.log(`❌ Failed: ${response.data.error}`);
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }

  console.log(`\n🎯 COMPREHENSIVE VERIFICATION SUMMARY`);
  console.log(`=====================================`);
  console.log(`📋 Generated ${testCases.length} test PDFs with different configurations`);
  console.log(`📋 Each PDF should demonstrate BOTH fixes:`);
  
  console.log(`\n✅ FIX 1 - Verification Text Positioning:`);
  console.log(`   • Text positioned on the FAR RIGHT side of departure headers`);
  console.log(`   • Horizontally aligned with "DEPARTURE:" and "RETURN:" headers`);
  console.log(`   • Light grey color (#888888) for authentic airline look`);
  console.log(`   • Consistent across all flight segments and route name lengths`);
  
  console.log(`\n✅ FIX 2 - Important Information Section Removal:`);
  console.log(`   • NO "Important Information" section at the bottom of any PDF`);
  console.log(`   • Clean ending after flight details`);
  console.log(`   • No bullet points about refunds, extras, check-in, etc.`);
  console.log(`   • Professional, authentic airline reservation appearance`);
  
  console.log(`\n📄 Files to review:`);
  testCases.forEach(testCase => {
    console.log(`   • ${testCase.filename} - ${testCase.name}`);
  });
  
  console.log(`\n🎨 All PDFs should now match the authentic airline reservation aesthetic`);
  console.log(`   from your sample images with proper positioning and clean endings!`);
}

testMultipleScenarios().catch(console.error);
