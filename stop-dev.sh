#!/bin/bash

# VerifiedOnward Development Environment Stop Script
# This script stops both backend and frontend servers

echo "🛑 Stopping VerifiedOnward Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to kill process by PID file
kill_by_pid_file() {
    local pid_file=$1
    local service_name=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            print_status "Stopping $service_name (PID: $pid)..."
            kill "$pid"
            sleep 2
            if kill -0 "$pid" 2>/dev/null; then
                print_warning "Force killing $service_name..."
                kill -9 "$pid"
            fi
            print_success "$service_name stopped"
        else
            print_warning "$service_name was not running"
        fi
        rm -f "$pid_file"
    else
        print_warning "No PID file found for $service_name"
    fi
}

# Function to kill processes on a port
kill_port() {
    local port=$1
    local service_name=$2
    
    print_status "Checking for processes on port $port ($service_name)..."
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    
    if [ -n "$pids" ]; then
        print_status "Killing processes on port $port..."
        echo "$pids" | xargs kill -9 2>/dev/null || true
        print_success "Processes on port $port killed"
    else
        print_success "No processes found on port $port"
    fi
}

# Create logs directory if it doesn't exist
mkdir -p logs

# Stop servers using PID files
kill_by_pid_file "logs/backend.pid" "Backend Server"
kill_by_pid_file "logs/frontend.pid" "Frontend Server"

# Also kill any processes on the ports (backup method)
kill_port 5001 "Backend"
kill_port 5173 "Frontend"

# Clean up any remaining Node.js processes that might be related
print_status "Cleaning up any remaining Node.js processes..."
pkill -f "node server.js" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true
pkill -f "vite" 2>/dev/null || true

# Wait a moment for cleanup
sleep 2

# Final verification
print_status "Verifying servers are stopped..."
if lsof -Pi :5001 -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_warning "Something is still running on port 5001"
else
    print_success "Port 5001 is free"
fi

if lsof -Pi :5173 -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_warning "Something is still running on port 5173"
else
    print_success "Port 5173 is free"
fi

echo ""
print_success "VerifiedOnward Development Environment stopped!"
echo ""
echo "📝 To restart:"
echo "   ./start-dev.sh"
echo ""
echo "📊 To check status:"
echo "   ./health-check.sh"
echo ""
