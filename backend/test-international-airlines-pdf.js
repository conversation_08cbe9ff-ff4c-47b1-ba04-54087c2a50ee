const pdfService = require('./services/pdfService');

/**
 * Generate comprehensive test PDF with international airlines
 * Including airlines from the screenshot (AIR FRANCE, THAI) and major global carriers
 */
async function generateInternationalAirlinesPDF() {
  console.log('🌍 GENERATING INTERNATIONAL AIRLINES TEST PDF');
  console.log('='.repeat(60));

  // Comprehensive test data with airlines from all regions
  const testData = {
    tripDates: '21 JUL 2025 › 28 JUL 2025',
    destination: 'INTERNATIONAL AIRLINES LOGO VERIFICATION TEST',
    passengers: [
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
      { name: 'INTERNATION<PERSON>', surname: '<PERSON>ERIFIC<PERSON><PERSON>' }
    ],
    reservationCode: 'INTL001',
    airlineReservationCode: 'GLOBAL01',
    segments: [
      // Screenshot Airlines (from provided image)
      {
        airline: 'AIR FRANCE',
        flightNo: 'AF 1669',
        route: 'MAN – BKK',
        duration: '15H 25M',
        stops: 1,
        departure: {
          date: 'MONDAY, JUL 21',
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '11:50',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'BKK',
          airportName: 'Suvarnabhumi Airport',
          time: '09:15',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'AIRBUS A321',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      {
        airline: 'THAI',
        flightNo: 'TG 920',
        route: 'BKK – MAN',
        duration: '14H 40M',
        stops: 1,
        departure: {
          date: 'MONDAY, JUL 28',
          airport: 'BKK',
          airportName: 'Suvarnabhumi Airport',
          time: '23:45',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '08:25',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'AIRBUS A320',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      
      // Asian Airlines
      {
        airline: 'KOREAN AIR',
        flightNo: 'KE 123',
        route: 'ICN – LAX',
        duration: '11H 30M',
        stops: 0,
        departure: {
          date: 'TUESDAY, JUL 22',
          airport: 'ICN',
          airportName: 'Incheon International Airport',
          time: '14:20',
          terminal: 'TERMINAL 2'
        },
        arrival: {
          airport: 'LAX',
          airportName: 'Los Angeles International Airport',
          time: '08:50',
          terminal: 'TERMINAL B'
        },
        aircraft: 'BOEING 777-300ER',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      {
        airline: 'MALAYSIA AIRLINES',
        flightNo: 'MH 370',
        route: 'KUL – LHR',
        duration: '13H 15M',
        stops: 0,
        departure: {
          date: 'WEDNESDAY, JUL 23',
          airport: 'KUL',
          airportName: 'Kuala Lumpur International Airport',
          time: '23:35',
          terminal: 'TERMINAL M'
        },
        arrival: {
          airport: 'LHR',
          airportName: 'London Heathrow Airport',
          time: '06:50',
          terminal: 'TERMINAL 4'
        },
        aircraft: 'AIRBUS A350-900',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      
      // Middle Eastern Airlines
      {
        airline: 'ETIHAD AIRWAYS',
        flightNo: 'EY 25',
        route: 'AUH – JFK',
        duration: '14H 20M',
        stops: 0,
        departure: {
          date: 'THURSDAY, JUL 24',
          airport: 'AUH',
          airportName: 'Abu Dhabi International Airport',
          time: '02:25',
          terminal: 'TERMINAL 3'
        },
        arrival: {
          airport: 'JFK',
          airportName: 'John F. Kennedy International Airport',
          time: '09:45',
          terminal: 'TERMINAL 4'
        },
        aircraft: 'BOEING 787-9',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      
      // African Airlines
      {
        airline: 'ETHIOPIAN AIRLINES',
        flightNo: 'ET 500',
        route: 'ADD – DUB',
        duration: '7H 45M',
        stops: 0,
        departure: {
          date: 'FRIDAY, JUL 25',
          airport: 'ADD',
          airportName: 'Addis Ababa Bole International Airport',
          time: '23:30',
          terminal: 'TERMINAL 2'
        },
        arrival: {
          airport: 'DUB',
          airportName: 'Dublin Airport',
          time: '05:15',
          terminal: 'TERMINAL 2'
        },
        aircraft: 'BOEING 787-8',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      
      // South American Airlines
      {
        airline: 'LATAM',
        flightNo: 'LA 800',
        route: 'SCL – MAD',
        duration: '12H 30M',
        stops: 0,
        departure: {
          date: 'SATURDAY, JUL 26',
          airport: 'SCL',
          airportName: 'Santiago International Airport',
          time: '22:15',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'MAD',
          airportName: 'Madrid-Barajas Airport',
          time: '16:45',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'BOEING 787-9',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      
      // Oceania Airlines
      {
        airline: 'QANTAS',
        flightNo: 'QF 1',
        route: 'SYD – LHR',
        duration: '21H 25M',
        stops: 1,
        departure: {
          date: 'SUNDAY, JUL 27',
          airport: 'SYD',
          airportName: 'Sydney Kingsford Smith Airport',
          time: '15:10',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'LHR',
          airportName: 'London Heathrow Airport',
          time: '05:35',
          terminal: 'TERMINAL 3'
        },
        aircraft: 'AIRBUS A380-800',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      }
    ],
    showNotice: true,
    customNotice: "INTERNATIONAL AIRLINES TEST: Verifying authentic brand logos for global carriers from Asia, Middle East, Africa, South America, and Oceania. This includes airlines from the provided screenshot (AIR FRANCE, THAI)."
  };

  console.log('📋 Airlines being tested in PDF:');
  testData.segments.forEach((segment, index) => {
    const airlineCode = pdfService.getAirlineCode(segment.airline);
    console.log(`   ${index + 1}. ${segment.airline} ${segment.flightNo} (${segment.route}) → ${airlineCode}`);
  });

  console.log('\n📄 Generating comprehensive international airlines PDF...');
  try {
    const outputPath = './test-outputs/international-airlines-comprehensive-test.pdf';
    await pdfService.generatePDF(testData, outputPath);
    console.log(`   ✅ PDF generated: ${outputPath}`);

    console.log('\n🎯 VISUAL VERIFICATION CHECKLIST:');
    console.log('   Please open the generated PDF and verify:');
    console.log('   □ AIR FRANCE shows recognizable blue/red Air France logo');
    console.log('   □ THAI shows recognizable purple Thai Airways logo');
    console.log('   □ KOREAN AIR shows recognizable blue Korean Air logo');
    console.log('   □ MALAYSIA AIRLINES shows recognizable red Malaysia Airlines logo');
    console.log('   □ ETIHAD AIRWAYS shows recognizable gold/black Etihad logo');
    console.log('   □ ETHIOPIAN AIRLINES shows recognizable green Ethiopian logo');
    console.log('   □ LATAM shows recognizable purple LATAM logo');
    console.log('   □ QANTAS shows recognizable red Qantas kangaroo logo');
    console.log('   □ All logos are clear, properly sized, and professional');
    console.log('   □ No text placeholders like "AIR FRANCE AF 1669" appear instead of logos');
    console.log('   □ No broken image icons or generic symbols');

    return outputPath;

  } catch (error) {
    console.error(`   ❌ PDF generation failed: ${error.message}`);
    throw error;
  }
}

// Run the test
generateInternationalAirlinesPDF()
  .then(outputPath => {
    console.log(`\n🎉 INTERNATIONAL AIRLINES PDF TEST COMPLETE!`);
    console.log(`📁 Generated PDF: ${outputPath}`);
    console.log('\n🌍 GLOBAL COVERAGE ACHIEVED:');
    console.log('   ✅ Screenshot Airlines (AIR FRANCE, THAI)');
    console.log('   ✅ Asian Airlines (Korean Air, Malaysia Airlines)');
    console.log('   ✅ Middle Eastern Airlines (Etihad Airways)');
    console.log('   ✅ African Airlines (Ethiopian Airlines)');
    console.log('   ✅ South American Airlines (LATAM)');
    console.log('   ✅ Oceania Airlines (Qantas)');
    console.log('\n🎯 All international airlines should display authentic brand logos!');
  })
  .catch(error => {
    console.error('\n❌ International airlines PDF test failed:', error);
    process.exit(1);
  });
