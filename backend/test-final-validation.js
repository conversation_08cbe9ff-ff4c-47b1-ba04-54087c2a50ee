const pdfService = require('./services/pdfService');
const airlineLogoService = require('./services/airlineLogoService');
const path = require('path');
const fs = require('fs').promises;

/**
 * Final validation test to confirm both critical issues are resolved:
 * 1. Header text alignment (left-aligned instead of centered)
 * 2. Airline logo integration (real logos displayed in left branding panel)
 */
async function validateFinalFixes() {
  console.log('🎯 Final Validation: Critical Layout Fixes\n');

  // Test case specifically designed to validate both fixes
  const validationTest = {
    tripDates: '15 DEC 2025',
    destination: 'TRIP TO LONDON HEATHROW AIRPORT',
    passengers: [
      { name: 'VALIDATION/TEST MR.' }
    ],
    reservationCode: 'VALID123456',
    airlineReservationCode: 'VALID123456',
    segments: [
      {
        airline: 'BRITISH AIRWAYS',
        flightNo: 'BA 177',
        from: { code: 'JFK', city: 'New York JFK', time: '22:30', terminal: '7' },
        to: { code: 'LHR', city: 'London Heathrow', time: '09:45', terminal: '5' },
        departureDay: 'SUNDAY, DEC 15',
        duration: '6h 15m',
        flightClass: 'Economy Class (M)',
        aircraft: 'Boeing 777-200ER',
        distance: '3,459 miles',
        stops: '0',
        meals: 'Available'
      }
    ],
    showNotice: false
  };

  try {
    console.log('📋 Validation Test Details:');
    console.log(`  ✈️  Airline: ${validationTest.segments[0].airline}`);
    console.log(`  🎫 Flight: ${validationTest.segments[0].flightNo}`);
    console.log(`  📅 Header: "${validationTest.tripDates} ${validationTest.destination}"`);
    
    // Pre-validate logo fetching
    console.log('\n🔍 Pre-validation: Logo Fetching');
    const airlineCode = airlineLogoService.getAirlineCode(validationTest.segments[0].airline);
    console.log(`  🏷️  Airline Code: ${airlineCode}`);
    
    const logoResult = await airlineLogoService.getLogoForPDF(airlineCode, validationTest.segments[0].airline);
    const logoStatus = logoResult && logoResult.startsWith('data:') ? 'SUCCESS' : 'FAILED';
    console.log(`  🖼️  Logo Status: ${logoStatus}`);
    
    if (logoStatus === 'SUCCESS') {
      console.log(`  📏 Logo Length: ${logoResult.length} characters`);
      console.log(`  📄 Logo Format: ${logoResult.includes('png') ? 'PNG' : logoResult.includes('svg') ? 'SVG' : 'Unknown'}`);
    }

    // Generate validation PDF
    console.log('\n📄 Generating Validation PDF...');
    const outputDir = path.join(__dirname, 'test-outputs');
    await fs.mkdir(outputDir, { recursive: true });
    
    const filename = 'final-validation-test.pdf';
    const outputPath = path.join(outputDir, filename);
    
    const pdfBuffer = await pdfService.generatePDF(validationTest, outputPath);
    
    console.log(`  ✅ Generated: ${outputPath}`);
    console.log(`  📊 Size: ${(pdfBuffer.length / 1024).toFixed(1)} KB`);

    // Validation checklist
    console.log('\n✅ VALIDATION CHECKLIST - MANUAL VERIFICATION REQUIRED:');
    console.log('');
    console.log('🎯 CRITICAL ISSUE #1 - Header Text Alignment:');
    console.log('  [ ] Header text "15 DEC 2025 TRIP TO LONDON HEATHROW AIRPORT" is LEFT-ALIGNED');
    console.log('  [ ] Header text is NOT centered');
    console.log('  [ ] Header matches authentic airline reservation format');
    console.log('');
    console.log('🎯 CRITICAL ISSUE #2 - Airline Logo Integration:');
    console.log('  [ ] British Airways logo appears as actual image (not text/placeholder)');
    console.log('  [ ] Logo is positioned directly ABOVE the airline name "BRITISH AIRWAYS"');
    console.log('  [ ] Logo is centered horizontally in the left branding panel');
    console.log('  [ ] Logo size is approximately 50px width × 30px height');
    console.log('  [ ] Logo displays the actual British Airways branding/colors');
    console.log('');
    console.log('🎯 ADDITIONAL VALIDATION:');
    console.log('  [ ] All text labels are UPPERCASE');
    console.log('  [ ] Table borders are thick (2px) and black');
    console.log('  [ ] "CONFIRMED" status uses normal font weight');
    console.log('  [ ] Important Information section is removed');
    console.log('  [ ] Overall layout matches Onwardticket samples');

    console.log('\n📋 Next Steps:');
    console.log('1. Open the generated PDF: final-validation-test.pdf');
    console.log('2. Verify header text is left-aligned (not centered)');
    console.log('3. Confirm British Airways logo displays as actual image');
    console.log('4. Check logo positioning above airline name');
    console.log('5. Compare against Onwardticket reference samples');

    return {
      success: true,
      pdfPath: outputPath,
      logoStatus: logoStatus,
      fileSize: pdfBuffer.length
    };

  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run the validation
if (require.main === module) {
  validateFinalFixes()
    .then(result => {
      if (result.success) {
        console.log('\n🎉 Validation test completed successfully!');
      } else {
        console.log('\n❌ Validation test failed!');
      }
    })
    .catch(console.error);
}

module.exports = { validateFinalFixes };
