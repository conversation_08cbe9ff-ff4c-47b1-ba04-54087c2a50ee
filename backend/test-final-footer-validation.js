const pdfService = require('./services/pdfService');
const path = require('path');
const fs = require('fs').promises;

/**
 * Final comprehensive validation test for footer removal
 * Ensures all PDFs have clean, authentic airline appearance without any branding
 */
async function finalFooterValidation() {
  console.log('🎯 Final Footer Removal Validation\n');
  console.log('Testing complete elimination of VerifiedOnward.com branding from all PDFs\n');

  const testCases = [
    {
      name: 'Cathay Pacific - Hong Kong to New York',
      airline: 'CATHAY PACIFIC',
      flightNo: 'CX 841',
      from: { code: 'HKG', city: 'Hong Kong International', time: '23:45', terminal: '1' },
      to: { code: 'JFK', city: 'New York JFK', time: '05:30', terminal: '4' },
      filename: 'final-cathay-pacific-clean.pdf'
    },
    {
      name: 'EasyJet - Manchester to Malta',
      airline: 'EASYJET',
      flightNo: 'U2 2273',
      from: { code: 'MAN', city: 'Manchester Airport', time: '05:55', terminal: '1' },
      to: { code: 'MLA', city: 'Malta International Airport', time: '10:25', terminal: '1' },
      filename: 'final-easyjet-clean.pdf'
    },
    {
      name: 'British Airways - London to Dubai',
      airline: 'BRITISH AIRWAYS',
      flightNo: 'BA 177',
      from: { code: 'LHR', city: 'London Heathrow', time: '22:30', terminal: '5' },
      to: { code: 'DXB', city: 'Dubai International', time: '08:45', terminal: '3' },
      filename: 'final-british-airways-clean.pdf'
    },
    {
      name: 'Lufthansa - Frankfurt to Tokyo',
      airline: 'LUFTHANSA',
      flightNo: 'LH 716',
      from: { code: 'FRA', city: 'Frankfurt Airport', time: '13:40', terminal: '1' },
      to: { code: 'NRT', city: 'Tokyo Narita', time: '08:05', terminal: '1' },
      filename: 'final-lufthansa-clean.pdf'
    }
  ];

  const results = [];
  const outputDir = path.join(__dirname, 'test-outputs');
  await fs.mkdir(outputDir, { recursive: true });

  console.log('🧪 Generating Clean Airline PDFs...\n');

  for (const testCase of testCases) {
    console.log(`✈️  Testing ${testCase.name}...`);
    
    const ticketData = {
      tripDates: '15 JAN 2025 • 22 JAN 2025',
      destination: 'INTERNATIONAL BUSINESS TRAVEL',
      passengers: [
        { name: 'BUSINESS/TRAVELER MR.' },
        { name: 'CORPORATE/CLIENT MS.' }
      ],
      reservationCode: `CLEAN${Date.now().toString().slice(-6)}`,
      airlineReservationCode: `CLEAN${Date.now().toString().slice(-6)}`,
      segments: [
        {
          airline: testCase.airline,
          flightNo: testCase.flightNo,
          from: testCase.from,
          to: testCase.to,
          departureDay: 'MONDAY, JAN 15',
          duration: '8h 25m',
          flightClass: 'Business Class (C)',
          aircraft: 'Boeing 777-300ER',
          distance: '6,847 miles',
          stops: '0',
          meals: 'Available'
        }
      ],
      showNotice: false
    };

    try {
      const outputPath = path.join(outputDir, testCase.filename);
      const pdfBuffer = await pdfService.generatePDF(ticketData, outputPath);
      
      // Comprehensive footer detection
      const htmlContent = await pdfService.generateTicketHTML(ticketData);
      const footerTerms = [
        'VerifiedOnward.com',
        'Professional Flight Reservations',
        'Embassy-Approved',
        'Instant Download',
        '24/7 Support',
        'VerifiedOnward',
        'verifiedonward',
        'Professional Flight',
        'Embassy Approved'
      ];
      
      const foundTerms = footerTerms.filter(term => 
        htmlContent.toLowerCase().includes(term.toLowerCase())
      );
      
      const isClean = foundTerms.length === 0;
      
      results.push({
        airline: testCase.name,
        filename: testCase.filename,
        success: isClean,
        fileSize: `${(pdfBuffer.length / 1024).toFixed(1)} KB`,
        foundTerms: foundTerms,
        htmlLength: htmlContent.length
      });

      console.log(`  📄 Generated: ${testCase.filename}`);
      console.log(`  📊 Size: ${(pdfBuffer.length / 1024).toFixed(1)} KB`);
      console.log(`  🧹 Clean Status: ${isClean ? '✅ CLEAN' : '❌ CONTAINS BRANDING'}`);
      if (foundTerms.length > 0) {
        console.log(`  ⚠️  Found terms: ${foundTerms.join(', ')}`);
      }
      
    } catch (error) {
      console.error(`  ❌ Error generating ${testCase.name} PDF:`, error.message);
      results.push({
        airline: testCase.name,
        filename: testCase.filename,
        success: false,
        error: error.message
      });
    }
    
    console.log('');
  }

  // Final validation report
  console.log('📋 FINAL FOOTER REMOVAL VALIDATION RESULTS:');
  console.log('═'.repeat(60));
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  results.forEach(result => {
    const status = result.success ? '✅ CLEAN' : '❌ BRANDED';
    console.log(`${status} ${result.airline.padEnd(35)} ${result.filename}`);
    if (result.foundTerms && result.foundTerms.length > 0) {
      console.log(`     🚨 Branding found: ${result.foundTerms.join(', ')}`);
    }
    if (result.error) {
      console.log(`     ❌ Error: ${result.error}`);
    }
  });
  
  console.log('═'.repeat(60));
  console.log(`📊 Final Result: ${successCount}/${totalCount} PDFs are completely clean`);
  
  if (successCount === totalCount) {
    console.log('🎉 SUCCESS: All PDFs have authentic airline appearance!');
    console.log('✨ Footer branding completely eliminated from all generated tickets');
    console.log('🏆 PDFs now maintain professional airline reservation styling');
    console.log('📁 Clean PDFs saved in: backend/test-outputs/');
  } else {
    console.log('❌ FAILURE: Some PDFs still contain branding');
    console.log('🔧 Manual review required for failed tests');
  }

  return {
    success: successCount === totalCount,
    results: results,
    cleanPdfs: successCount,
    totalPdfs: totalCount
  };
}

// Run the final validation
if (require.main === module) {
  finalFooterValidation()
    .then(result => {
      console.log('\n' + '='.repeat(60));
      if (result.success) {
        console.log('🎯 FINAL VALIDATION: PASSED ✅');
        console.log('All airline PDFs are now clean and professional!');
      } else {
        console.log('🎯 FINAL VALIDATION: FAILED ❌');
        console.log('Some PDFs still contain branding elements.');
      }
      console.log('='.repeat(60));
      
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Final validation failed:', error);
      process.exit(1);
    });
}

module.exports = { finalFooterValidation };
