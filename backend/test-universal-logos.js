const airlineLogoService = require('./services/airlineLogoService');

async function testUniversalLogos() {
  console.log('🌍 Testing Universal Airline Logo System...\n');

  // Test airlines from different regions and types
  const testAirlines = [
    // Major carriers with known logos
    { code: 'PC', name: 'Pegasus Airlines', region: 'Turkey' },
    { code: 'LH', name: 'Lufthansa', region: 'Germany' },
    { code: 'BA', name: 'British Airways', region: 'UK' },
    { code: 'EK', name: 'Emirates', region: 'UAE' },
    
    // Airlines that might not be in our database
    { code: 'SU', name: 'Aeroflot', region: 'Russia' },
    { code: 'CX', name: 'Cathay Pacific', region: 'Hong Kong' },
    { code: 'JL', name: 'Japan Airlines', region: 'Japan' },
    { code: 'AC', name: 'Air Canada', region: 'Canada' },
    
    // Regional/smaller airlines
    { code: 'WF', name: 'Widerøe', region: 'Norway' },
    { code: 'BT', name: 'Air Baltic', region: 'Latvia' },
    { code: 'OS', name: 'Austrian Airlines', region: 'Austria' },
    
    // Made-up airline codes to test fallback
    { code: 'XY', name: 'Test Airways', region: 'Test' },
    { code: 'ZZ', name: 'Unknown Airline', region: 'Unknown' }
  ];

  console.log('📊 Testing logo retrieval for various airlines:\n');

  for (const airline of testAirlines) {
    try {
      console.log(`🔍 Testing ${airline.name} (${airline.code}) - ${airline.region}`);
      
      const logoUrl = await airlineLogoService.getAirlineLogo(airline.code, airline.name);
      
      if (logoUrl.startsWith('http')) {
        console.log(`  ✅ External logo URL: ${logoUrl}`);
        
        // Test if the URL is accessible
        try {
          const response = await fetch(logoUrl, { method: 'HEAD' });
          if (response.ok) {
            console.log(`  🌐 Logo URL is accessible (${response.status})`);
          } else {
            console.log(`  ⚠️  Logo URL returned ${response.status} - will fallback to SVG`);
          }
        } catch (fetchError) {
          console.log(`  ⚠️  Logo URL not accessible - will fallback to SVG`);
        }
      } else if (logoUrl.startsWith('data:image/svg+xml')) {
        console.log(`  🎨 Generated SVG fallback logo`);
        console.log(`  📏 SVG size: ${logoUrl.length} characters`);
      }
      
      console.log(''); // Empty line for readability
      
    } catch (error) {
      console.error(`  ❌ Error testing ${airline.code}:`, error.message);
    }
  }

  console.log('\n🧪 Testing logo source generation for unknown airlines:');
  
  const unknownAirlines = ['XX', 'YY', 'AB', 'CD', 'EF'];
  for (const code of unknownAirlines) {
    const sources = airlineLogoService.getAllLogoSources(code);
    console.log(`${code}: ${sources.length} sources`);
    console.log(`  Primary: ${sources[0]}`);
  }

  console.log('\n✅ Universal airline logo system test completed!');
  console.log('\n📋 Summary:');
  console.log('- ✅ Known airlines will use curated logo URLs');
  console.log('- ✅ Unknown airlines will try universal logo sources');
  console.log('- ✅ All airlines get beautiful SVG fallbacks if logos fail');
  console.log('- ✅ SVG fallbacks use airline-specific colors when possible');
  console.log('- ✅ System works for ANY airline code automatically');
}

// Add fetch polyfill for Node.js testing
if (typeof fetch === 'undefined') {
  global.fetch = async (url, options = {}) => {
    const https = require('https');
    const http = require('http');
    const urlModule = require('url');
    
    return new Promise((resolve, reject) => {
      const parsedUrl = urlModule.parse(url);
      const client = parsedUrl.protocol === 'https:' ? https : http;
      
      const req = client.request({
        hostname: parsedUrl.hostname,
        port: parsedUrl.port,
        path: parsedUrl.path,
        method: options.method || 'GET',
        headers: options.headers || {}
      }, (res) => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          statusText: res.statusMessage
        });
      });
      
      req.on('error', reject);
      req.end();
    });
  };
}

// Run the test
testUniversalLogos().catch(console.error);
