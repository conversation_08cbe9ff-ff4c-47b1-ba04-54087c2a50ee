const pdfService = require('./services/pdfService');
const airlineLogoService = require('./services/airlineLogoService');

async function debugLogoPDF() {
  console.log('🔍 Debugging airline logo display in PDF...\n');

  // Test data with airlines from the screenshots
  const testTicketData = {
    tripDates: '21 JUL 2025 › 28 JUL 2025',
    destination: 'TRIP TO DEBUG LOGO TEST',
    passengers: [
      { name: 'DEBUG/TEST', surname: '<PERSON>OGO/VERIFICATION' }
    ],
    reservationCode: 'DEBUG1',
    airlineReservationCode: 'LOGO01',
    segments: [
      {
        airline: 'JET2',
        flightNo: 'LS 803',
        route: 'MAN – BCN',
        duration: '2H 35M',
        stops: 0,
        departure: {
          date: 'MONDAY, JUL 21',
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '08:00',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'BCN',
          airportName: '<PERSON><PERSON>dellas Barcelona-El Prat Airport',
          time: '11:35',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'BOEING 737-800',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      {
        airline: 'KLM',
        flightNo: 'KL 1518',
        route: 'BCN – MAN',
        duration: '5H 25M',
        stops: 1,
        departure: {
          date: 'MONDAY, JUL 28',
          airport: 'BCN',
          airportName: 'Josep Tarradellas Barcelona-El Prat Airport',
          time: '17:20',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '21:45',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'BOEING 737-800',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      {
        airline: 'VUELING',
        flightNo: 'VY 8747',
        route: 'MAN – BCN',
        duration: '2H 25M',
        stops: 0,
        departure: {
          date: 'MONDAY, JUL 21',
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '09:20',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'BCN',
          airportName: 'Josep Tarradellas Barcelona-El Prat Airport',
          time: '12:45',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'AIRBUS A319',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      {
        airline: 'RYANAIR UK',
        flightNo: 'RK 5270',
        route: 'BCN – MAN',
        duration: '2H 35M',
        stops: 0,
        departure: {
          date: 'MONDAY, JUL 28',
          airport: 'BCN',
          airportName: 'Josep Tarradellas Barcelona-El Prat Airport',
          time: '22:10',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '23:45',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'AIRBUS A320',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      }
    ],
    showNotice: true,
    customNotice: "DEBUG: Testing airline logo display in PDF generation."
  };

  // Test airline code mapping
  console.log('1. Testing airline code mapping:');
  for (const segment of testTicketData.segments) {
    const airlineCode = pdfService.getAirlineCode(segment.airline);
    console.log(`   ${segment.airline} -> ${airlineCode}`);
  }

  // Test logo retrieval
  console.log('\n2. Testing logo retrieval:');
  for (const segment of testTicketData.segments) {
    const airlineCode = pdfService.getAirlineCode(segment.airline);
    try {
      const logo = await airlineLogoService.getLogoForPDF(airlineCode, segment.airline);
      const isBase64 = logo.startsWith('data:image/png;base64,');
      const isSvg = logo.startsWith('data:image/svg');
      const isUrl = logo.startsWith('http');
      
      console.log(`   ${segment.airline} (${airlineCode}):`);
      console.log(`     Type: ${isBase64 ? 'Base64 PNG' : isSvg ? 'SVG' : isUrl ? 'External URL' : 'Unknown'}`);
      console.log(`     Length: ${logo.length} chars`);
      console.log(`     Preview: ${logo.substring(0, 60)}...`);
    } catch (error) {
      console.log(`   ${segment.airline} (${airlineCode}): ERROR - ${error.message}`);
    }
  }

  // Generate PDF
  console.log('\n3. Generating debug PDF...');
  try {
    const outputPath = './test-outputs/debug-logo-test.pdf';
    await pdfService.generatePDF(testTicketData, outputPath);
    console.log(`✅ PDF generated: ${outputPath}`);
    console.log('\n📋 Next steps:');
    console.log('   1. Open the generated PDF');
    console.log('   2. Check if airline logos are displaying correctly');
    console.log('   3. Compare with the screenshots provided');
  } catch (error) {
    console.error('❌ PDF generation failed:', error);
  }
}

debugLogoPDF().catch(console.error);
