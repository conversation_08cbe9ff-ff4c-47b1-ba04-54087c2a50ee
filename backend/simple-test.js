console.log('🧪 Starting simple PDF test...');

try {
  const PDFDocument = require('pdfkit');
  const fs = require('fs');
  const path = require('path');
  
  console.log('✅ PDFKit loaded successfully');
  
  // Create a simple test PDF
  const doc = new PDFDocument();
  const testPath = path.join(__dirname, 'temp', 'simple-test.pdf');
  const stream = fs.createWriteStream(testPath);
  
  doc.pipe(stream);
  
  doc.fontSize(20)
     .text('CATHAY PACIFIC TEST', 100, 100);
     
  doc.fontSize(12)
     .text('This is a test PDF to verify PDFKit is working', 100, 150);
  
  doc.end();
  
  stream.on('finish', () => {
    console.log(`✅ Simple test PDF created: ${testPath}`);
    
    // Now test the Cathay service
    console.log('🧪 Testing Cathay PDF service...');
    const cathayPdfService = require('./services/cathayPdfService');
    
    const testData = {
      bookingReference: 'SIMPLE001',
      passengers: [{ firstName: 'TEST', lastName: 'USER' }],
      outboundFlight: {
        airline: 'CATHAY PACIFIC',
        flightNumber: 'CX 784',
        departureDate: '2021-07-18',
        departureTime: '16:05',
        arrivalTime: '21:05',
        duration: '05hr(s) 00min(s)',
        departureAirport: { iata: 'DPS', city: 'Denpasar-Bali', country: 'Indonesia' },
        arrivalAirport: { iata: 'HKG', city: 'Hong Kong', country: 'Hong Kong' }
      }
    };
    
    cathayPdfService.generateCathayPDF(testData)
      .then(pdfPath => {
        console.log(`✅ Cathay PDF generated: ${pdfPath}`);
        process.exit(0);
      })
      .catch(error => {
        console.error('❌ Cathay PDF failed:', error.message);
        console.error('Stack:', error.stack);
        process.exit(1);
      });
  });
  
  stream.on('error', (error) => {
    console.error('❌ Simple PDF failed:', error);
    process.exit(1);
  });
  
} catch (error) {
  console.error('❌ Test setup failed:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}
