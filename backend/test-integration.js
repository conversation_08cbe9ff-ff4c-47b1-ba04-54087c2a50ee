const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

// Test integration with the actual API endpoints
const API_BASE_URL = 'http://localhost:5001/api';

// Test data for API integration
const testTicketData = {
  // Multi-leg flight test data
  multiLegTicket: {
    flightData: {
      trips: [
        {
          type: 'outbound',
          legs: [
            {
              legNumber: 1,
              airline: { name: 'SAS', code: 'SK' },
              flightNumber: 'SK4610',
              departure: {
                airport: 'MAN',
                city: 'Manchester',
                country: 'United Kingdom',
                datetime: '2025-07-28T13:55:00Z',
                terminal: 'Not assigned'
              },
              arrival: {
                airport: 'OSL',
                city: 'Oslo',
                country: 'Norway',
                datetime: '2025-07-28T16:55:00Z',
                terminal: 'Not assigned'
              },
              duration: '3h 00m',
              stopCount: 0,
              meals: 'Available at check-in',
              baggage: 'Available at check-in',
              aircraft: 'Boeing 737-800',
              checkIn: '2 hr(s) before departure'
            },
            {
              legNumber: 2,
              airline: { name: 'SAS', code: 'SK' },
              flightNumber: 'SK4785',
              departure: {
                airport: 'OSL',
                city: 'Oslo',
                country: 'Norway',
                datetime: '2025-07-28T18:05:00Z',
                terminal: 'Not assigned'
              },
              arrival: {
                airport: 'KEF',
                city: 'Keflavik',
                country: 'Iceland',
                datetime: '2025-07-28T18:55:00Z',
                terminal: 'Not assigned'
              },
              duration: '1h 50m',
              stopCount: 0,
              meals: 'Available at check-in',
              baggage: 'Available at check-in',
              aircraft: 'Airbus A320',
              checkIn: '2 hr(s) before departure'
            }
          ]
        }
      ]
    },
    passengerData: [
      { firstName: 'INTEGRATION', lastName: 'TEST' }
    ],
    email: '<EMAIL>'
  }
};

async function testAPIIntegration() {
  console.log('🔗 Starting API integration tests...\n');

  try {
    // Test 1: Health check
    console.log('📡 Testing API health check...');
    const healthResponse = await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ API health check passed:', healthResponse.data);

    // Test 2: Generate ticket via API
    console.log('\n🎫 Testing ticket generation via API...');
    const ticketResponse = await axios.post(`${API_BASE_URL}/tickets/generate`, testTicketData.multiLegTicket);
    
    if (ticketResponse.data.success) {
      console.log('✅ Ticket generation API call successful');
      console.log('   Booking Reference:', ticketResponse.data.bookingReference);
      console.log('   Download URL:', ticketResponse.data.downloadUrl);

      // Test 3: Download the generated PDF
      const bookingRef = ticketResponse.data.bookingReference;
      console.log(`\n📥 Testing PDF download for booking: ${bookingRef}...`);
      
      const downloadResponse = await axios.get(`${API_BASE_URL}/tickets/download/${bookingRef}`, {
        responseType: 'arraybuffer'
      });

      if (downloadResponse.status === 200) {
        console.log('✅ PDF download successful');
        console.log('   Content-Type:', downloadResponse.headers['content-type']);
        console.log('   File size:', downloadResponse.data.length, 'bytes');

        // Save the downloaded PDF for verification
        const testPdfPath = path.join(__dirname, 'temp', `integration_test_${bookingRef}.pdf`);
        await fs.writeFile(testPdfPath, downloadResponse.data);
        console.log('   Saved to:', testPdfPath);
      } else {
        console.log('❌ PDF download failed');
      }
    } else {
      console.log('❌ Ticket generation API call failed:', ticketResponse.data);
    }

  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ API server is not running. Please start the server with: npm start');
    } else {
      console.error('❌ Integration test error:', error.message);
      if (error.response) {
        console.error('   Response status:', error.response.status);
        console.error('   Response data:', error.response.data);
      }
    }
  }

  console.log('\n🏁 API integration tests completed!');
}

// Function to check if server is running
async function checkServerStatus() {
  try {
    const response = await axios.get(`${API_BASE_URL}/health`, { timeout: 5000 });
    return response.status === 200;
  } catch (error) {
    return false;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  checkServerStatus().then(isRunning => {
    if (isRunning) {
      testAPIIntegration().catch(console.error);
    } else {
      console.log('⚠️  API server is not running.');
      console.log('   Please start the server first with: npm start');
      console.log('   Then run this test again.');
    }
  });
}

module.exports = { testAPIIntegration, testTicketData };
