const axios = require('axios');
const fs = require('fs');
const path = require('path');

/**
 * Test the actual API endpoint to verify airline logos are working
 */
async function testAPILogos() {
  console.log('🌐 Testing API Endpoint for Airline Logo Display...\n');

  const testTicketData = {
    tripDates: '21 JUL 2025 › 28 JUL 2025',
    destination: 'TRIP TO LEONARDO DA VINCI INTERNATIONAL AIRPORT',
    passengers: [
      { name: 'LOGO/TEST', surname: 'API/VERIFICATION' }
    ],
    reservationCode: 'API001',
    airlineReservationCode: 'LOGO01',
    segments: [
      {
        airline: 'CATHAY PACIFIC',
        flightNo: 'CX 123',
        route: 'MAN – FCO',
        duration: '2H 55M',
        stops: 0,
        departure: {
          date: 'MONDAY, JUL 21',
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '07:05',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'FCO',
          airportName: 'Leonardo da Vinci International Airport',
          time: '11:00',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'BOEING 737-800',
        flightClass: 'Economy Class (M)',
        status: 'Confirmed'
      },
      {
        airline: 'BRITISH AIRWAYS',
        flightNo: 'BA 456',
        route: 'FCO – MAN',
        duration: '3H 5M',
        stops: 0,
        departure: {
          date: 'MONDAY, JUL 28',
          airport: 'FCO',
          airportName: 'Leonardo da Vinci International Airport',
          time: '12:00',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '14:05',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'AIRBUS A320',
        flightClass: 'Economy Class (M)',
        status: 'Confirmed'
      },
      {
        airline: 'LUFTHANSA',
        flightNo: 'LH 789',
        route: 'FRA – JFK',
        duration: '8H 30M',
        stops: 0,
        departure: {
          date: 'TUESDAY, JUL 29',
          airport: 'FRA',
          airportName: 'Frankfurt Airport',
          time: '10:30',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'JFK',
          airportName: 'John F. Kennedy International Airport',
          time: '14:00',
          terminal: 'TERMINAL 4'
        },
        aircraft: 'BOEING 747-8',
        flightClass: 'Economy Class (M)',
        status: 'Confirmed'
      }
    ],
    showNotice: true,
    customNotice: "This is an API test reservation for airline logo verification."
  };

  try {
    console.log('📤 Sending API request to generate PDF...');
    
    const response = await axios.post('http://localhost:5001/api/tickets/generate-pdf', testTicketData, {
      responseType: 'arraybuffer'
    });

    if (response.status === 200) {
      const outputPath = path.join(__dirname, 'test-outputs', 'api-airline-logo-test.pdf');
      
      // Ensure output directory exists
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      fs.writeFileSync(outputPath, response.data);
      
      console.log('✅ API PDF generated successfully!');
      console.log(`📁 Output: ${outputPath}`);
      console.log(`📊 File size: ${response.data.length} bytes`);
      
      // Open the PDF for visual inspection
      console.log('\n🔍 Opening PDF for visual inspection...');
      const { exec } = require('child_process');
      exec(`open "${outputPath}"`, (error) => {
        if (error) {
          console.log('⚠️  Could not auto-open PDF. Please open manually:', outputPath);
        }
      });

      console.log('\n📋 Airlines tested in API PDF:');
      testTicketData.segments.forEach((segment, index) => {
        console.log(`  ${index + 1}. ${segment.airline} (${segment.flightNo})`);
      });

      console.log('\n🎯 What to verify in the API-generated PDF:');
      console.log('  • CATHAY PACIFIC should show authentic Cathay Pacific logo');
      console.log('  • BRITISH AIRWAYS should show authentic British Airways logo');
      console.log('  • LUFTHANSA should show authentic Lufthansa logo');
      console.log('  • All logos should be clear, properly sized, and not placeholders');
      console.log('  • No broken image icons or generic airline symbols');

      return outputPath;

    } else {
      console.log('❌ API request failed with status:', response.status);
      return null;
    }

  } catch (error) {
    console.error('❌ API test error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testAPILogos().catch(console.error);
}

module.exports = { testAPILogos };
