const axios = require('axios');

async function testCathayAPI() {
  try {
    console.log('🧪 Testing Cathay PDF API...');
    
    const testData = {
      bookingReference: 'CATHAY001',
      passengers: [
        { firstName: 'JANE', lastName: 'COOPER' },
        { firstName: 'JENNY', lastName: 'WILSON' }
      ],
      outboundFlight: {
        airline: 'CATHAY PACIFIC',
        flightNumber: 'CX 784',
        departureDate: '2021-07-18',
        departureTime: '16:05',
        arrivalTime: '21:05',
        duration: '05hr(s) 00min(s)',
        aircraft: 'AIRBUS INDUSTRIE A330-300',
        departureAirport: {
          iata: 'DPS',
          city: 'Denpasar-Bali',
          country: 'Indonesia'
        },
        arrivalAirport: {
          iata: 'HKG',
          city: 'Hong Kong',
          country: 'Hong Kong'
        }
      },
      returnFlight: {
        airline: 'CATHAY PACIFIC',
        flightNumber: 'CX 844',
        departureDate: '2021-07-19',
        departureTime: '02:05',
        arrivalTime: '06:00',
        duration: '15hr(s) 55min(s)',
        aircraft: 'BOEING 777-300ER',
        departureAirport: {
          iata: 'HKG',
          city: 'Hong Kong',
          country: 'Hong Kong'
        },
        arrivalAirport: {
          iata: 'JFK',
          city: 'New York',
          country: 'United States Of America'
        }
      }
    };

    console.log('📤 Sending API request...');
    const response = await axios.post('http://localhost:5001/api/tickets/generate', testData, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 10000
    });

    console.log('✅ API Response:', response.data);
    
    if (response.data.success) {
      console.log(`🎯 PDF should be available at: ${response.data.downloadUrl}`);
      console.log('📁 Check temp/CATHAY001_FlightReservation.pdf');
    }

  } catch (error) {
    console.error('❌ API Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
  }
}

testCathayAPI();
