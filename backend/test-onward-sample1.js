const path = require('path');
const pdfService = require('./services/pdfService');

/**
 * Test script to generate PDF matching OnwardTicket Sample 1 format
 * This matches the Turkish Airlines + EgyptAir sample with precise styling
 */

async function testOnwardSample1() {
  console.log('🎯 Testing OnwardTicket Sample 1 Format...\n');

  // Sample 1 data - Turkish Airlines + EgyptAir (matching the reference image)
  const testTicketData = {
    tripDates: '21 JUL 2025 › 28 JUL 2025',
    destination: 'TRIP TO MITIGA INTERNATIONAL AIRPORT',
    passengers: [
      { name: 'ZEMIT/ATEDAL' },
      { name: 'MISRATI/HUDIFA' }
    ],
    reservationCode: 'QJNY2AV',
    airlineReservationCode: 'TUDAQF',
    segments: [
      {
        airline: 'TURKISH AIRLINES',
        flightNo: 'TK 1916',
        duration: '9h 50m',
        flightClass: 'Economy Class (M)',
        departureDay: 'MONDAY, JUL 21',
        from: {
          code: 'MAN',
          city: 'Manchester Airport',
          time: '23:15',
          terminal: '1'
        },
        to: {
          code: 'MJI',
          city: 'Mitiga International Airport',
          time: '10:05',
          terminal: '1'
        },
        aircraft: 'BOEING 737-800',
        distance: 'Not Available',
        stops: 1,
        layovers: [{ airport: 'IST', duration: '2h 40m' }],
        meals: 'Not Available'
      },
      {
        airline: 'EGYPTAIR',
        flightNo: 'MS 892',
        duration: '13h',
        flightClass: 'Economy Class (M)',
        departureDay: 'MONDAY, JUL 28',
        from: {
          code: 'MJI',
          city: 'Mitiga International Airport',
          time: '10:45',
          terminal: '1'
        },
        to: {
          code: 'MAN',
          city: 'Manchester Airport',
          time: '22:45',
          terminal: '1'
        },
        aircraft: 'AIRBUS A320',
        distance: 'Not Available',
        stops: 2,
        layovers: [
          { airport: 'CAI', duration: '1h 50m' },
          { airport: 'FRA', duration: '2h 5m' }
        ],
        meals: 'Not Available'
      }
    ],
    showNotice: false
  };

  try {
    console.log('📄 Generating PDF with OnwardTicket Sample 1 format...');
    
    const outputPath = path.join(__dirname, 'temp', 'ticket-ONWARD-SAMPLE1.pdf');
    const pdfBuffer = await pdfService.generatePDF(testTicketData, outputPath);
    
    console.log('✅ PDF generated successfully with OnwardTicket Sample 1 format');
    console.log(`📊 File size: ${pdfBuffer.length} bytes`);
    console.log(`💾 Saved to: ${outputPath}`);
    
    console.log('\n🎯 Applied OnwardTicket Sample 1 styling:');
    console.log('  ✓ Light gray borders (#cccccc) throughout');
    console.log('  ✓ Compressed cell padding (2px 6px)');
    console.log('  ✓ Light gray section headers (#f8f8f8)');
    console.log('  ✓ Arial/Helvetica font family');
    console.log('  ✓ Professional airline e-ticket appearance');
    
  } catch (error) {
    console.error('❌ PDF generation failed:', error);
    throw error;
  } finally {
    await pdfService.cleanup();
    console.log('🧹 PDF service cleaned up');
  }
}

// Run the test
if (require.main === module) {
  testOnwardSample1().catch(console.error);
}

module.exports = testOnwardSample1;
