const pdfService = require('./services/pdfService');
const path = require('path');

async function testSample2Styling() {
  console.log('🎨 Testing Sample 2 Styling Updates...\n');

  // Test ticket data matching the sample 2 format
  const testTicketData = {
    tripDates: '18 JUL 2021 › 19 JUL 2021',
    destination: 'TRIP TO NEW YORK CITY',
    passengers: [
      { name: 'COOPER/JANE MR.' },
      { name: 'WILSON/JENNY MR.' }
    ],
    reservationCode: 'NHG8IQ',
    airlineReservationCode: 'NHG8IQ',
    segments: [
      {
        airline: 'CATHAY PACIFIC',
        flightNo: 'CX 784',
        duration: '05hr(s) 00min(s)',
        flightClass: 'Economy Class (M)',
        departureDay: 'SUNDAY 18 JUL',
        from: {
          code: 'DPS',
          city: 'Denpasar-Bali, Indonesia',
          time: '16:05',
          terminal: '1'
        },
        to: {
          code: 'HKG',
          city: 'Hong Kong, Hong Kong',
          time: '21:05',
          terminal: '1'
        },
        aircraft: 'AIRBUS INDUSTRIE A330-300',
        distance: 'Not Available',
        stops: 0,
        meals: 'Not Available'
      },
      {
        airline: 'CATHAY PACIFIC',
        flightNo: 'CX 844',
        duration: '15hr(s) 55min(s)',
        flightClass: 'Economy Class (M)',
        departureDay: 'MONDAY 19 JUL',
        from: {
          code: 'HKG',
          city: 'Hong Kong, Hong Kong',
          time: '02:05',
          terminal: '1'
        },
        to: {
          code: 'JFK',
          city: 'New York, United States Of America',
          time: '06:00',
          terminal: '8'
        },
        aircraft: 'BOEING 777-300ER',
        distance: 'Not Available',
        stops: 0,
        meals: 'Not Available'
      }
    ],
    showNotice: false
  };

  try {
    console.log('📄 Generating PDF with Sample 2 styling updates...');
    
    const outputPath = path.join(__dirname, 'temp', 'ticket-SAMPLE2-STYLING.pdf');
    const pdfBuffer = await pdfService.generatePDF(testTicketData, outputPath);
    
    if (pdfBuffer) {
      console.log('✅ PDF generated successfully with Sample 2 styling');
      console.log(`📊 File size: ${pdfBuffer.length} bytes`);
      console.log(`💾 Saved to: ${outputPath}`);
      
      console.log('\n🎯 Applied styling changes:');
      console.log('  ✓ Changed borders from 3px solid black to 1px solid light gray (#ccc)');
      console.log('  ✓ Updated table cell padding to 3px vertical, 6px horizontal');
      console.log('  ✓ Changed "CONFIRMED" text to regular weight, uppercase');
      console.log('  ✓ Increased airline logo size from 50x30px to 70x45px');
      console.log('  ✓ Standardized airplane icons to normal size and weight');
      console.log('  ✓ Applied consistent light gray borders throughout');
      
    } else {
      console.log('❌ PDF generation failed');
    }
  } catch (error) {
    console.log('❌ PDF generation error:', error.message);
    console.error(error);
  }

  // Clean up
  try {
    await pdfService.cleanup();
  } catch (error) {
    console.log('⚠️ Cleanup warning:', error.message);
  }
}

// Run the test
testSample2Styling().catch(console.error);
