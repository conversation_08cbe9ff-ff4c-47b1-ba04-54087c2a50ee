const stripe = process.env.STRIPE_SECRET_KEY ? require('stripe')(process.env.STRIPE_SECRET_KEY) : null;

class StripeService {
  // Create payment intent
  async createPaymentIntent({ amount, currency, metadata = {} }) {
    if (!stripe) {
      throw new Error('Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.');
    }

    try {
      const paymentIntent = await stripe.paymentIntents.create({
        amount,
        currency,
        metadata: {
          ...metadata,
          service: 'VerifiedOnward'
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      return paymentIntent;
    } catch (error) {
      console.error('Stripe payment intent creation error:', error);
      throw new Error(`Stripe error: ${error.message}`);
    }
  }

  // Retrieve payment intent
  async retrievePaymentIntent(paymentIntentId) {
    if (!stripe) {
      throw new Error('Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.');
    }

    try {
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      return paymentIntent;
    } catch (error) {
      console.error('Stripe payment intent retrieval error:', error);
      throw new Error(`Stripe error: ${error.message}`);
    }
  }

  // Create customer (optional for future use)
  async createCustomer({ email, name }) {
    try {
      const customer = await stripe.customers.create({
        email,
        name,
        metadata: {
          service: 'VerifiedOnward'
        }
      });

      return customer;
    } catch (error) {
      console.error('Stripe customer creation error:', error);
      throw new Error(`Stripe error: ${error.message}`);
    }
  }

  // List payment methods for customer (optional)
  async listPaymentMethods(customerId) {
    try {
      const paymentMethods = await stripe.paymentMethods.list({
        customer: customerId,
        type: 'card',
      });

      return paymentMethods;
    } catch (error) {
      console.error('Stripe payment methods list error:', error);
      throw new Error(`Stripe error: ${error.message}`);
    }
  }

  // Create refund (optional for future use)
  async createRefund(paymentIntentId, amount = null) {
    try {
      const refundData = {
        payment_intent: paymentIntentId,
        metadata: {
          service: 'VerifiedOnward'
        }
      };

      if (amount) {
        refundData.amount = amount;
      }

      const refund = await stripe.refunds.create(refundData);
      return refund;
    } catch (error) {
      console.error('Stripe refund creation error:', error);
      throw new Error(`Stripe error: ${error.message}`);
    }
  }
}

module.exports = new StripeService();
