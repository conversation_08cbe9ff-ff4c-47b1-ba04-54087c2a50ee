const axios = require('axios');

class PayPalService {
  constructor() {
    this.clientId = process.env.PAYPAL_CLIENT_ID;
    this.clientSecret = process.env.PAYPAL_CLIENT_SECRET;
    this.mode = process.env.PAYPAL_MODE || 'sandbox';
    this.baseUrl = this.mode === 'sandbox' 
      ? 'https://api-m.sandbox.paypal.com'
      : 'https://api-m.paypal.com';
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  // Get OAuth2 access token
  async getAccessToken() {
    try {
      // Check if token is still valid
      if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
        return this.accessToken;
      }

      const auth = Buffer.from(`${this.clientId}:${this.clientSecret}`).toString('base64');

      const response = await axios.post(`${this.baseUrl}/v1/oauth2/token`, 
        'grant_type=client_credentials',
        {
          headers: {
            'Authorization': `Basic ${auth}`,
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      this.accessToken = response.data.access_token;
      // Set expiry to 5 minutes before actual expiry for safety
      this.tokenExpiry = Date.now() + (response.data.expires_in - 300) * 1000;

      return this.accessToken;
    } catch (error) {
      console.error('PayPal token error:', error.response?.data || error.message);
      throw new Error('Failed to authenticate with PayPal API');
    }
  }

  // Create order
  async createOrder({ amount, currency }) {
    try {
      const token = await this.getAccessToken();

      const orderData = {
        intent: 'CAPTURE',
        purchase_units: [{
          amount: {
            currency_code: currency,
            value: amount
          },
          description: 'Embassy-Approved Flight Reservation - VerifiedOnward.com'
        }],
        application_context: {
          return_url: `${process.env.FRONTEND_URL}/payment/success`,
          cancel_url: `${process.env.FRONTEND_URL}/payment/cancel`,
          brand_name: 'VerifiedOnward',
          landing_page: 'NO_PREFERENCE',
          user_action: 'PAY_NOW'
        }
      };

      const response = await axios.post(`${this.baseUrl}/v2/checkout/orders`, orderData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error) {
      console.error('PayPal order creation error:', error.response?.data || error.message);
      throw new Error('Failed to create PayPal order');
    }
  }

  // Capture order
  async captureOrder(orderId) {
    try {
      const token = await this.getAccessToken();

      const response = await axios.post(
        `${this.baseUrl}/v2/checkout/orders/${orderId}/capture`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('PayPal order capture error:', error.response?.data || error.message);
      throw new Error('Failed to capture PayPal order');
    }
  }

  // Get order details
  async getOrder(orderId) {
    try {
      const token = await this.getAccessToken();

      const response = await axios.get(`${this.baseUrl}/v2/checkout/orders/${orderId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error) {
      console.error('PayPal order retrieval error:', error.response?.data || error.message);
      throw new Error('Failed to retrieve PayPal order');
    }
  }

  // Create refund (optional for future use)
  async createRefund(captureId, amount = null) {
    try {
      const token = await this.getAccessToken();

      const refundData = {
        note_to_payer: 'Refund from VerifiedOnward'
      };

      if (amount) {
        refundData.amount = {
          value: amount.value,
          currency_code: amount.currency
        };
      }

      const response = await axios.post(
        `${this.baseUrl}/v2/payments/captures/${captureId}/refund`,
        refundData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('PayPal refund creation error:', error.response?.data || error.message);
      throw new Error('Failed to create PayPal refund');
    }
  }
}

module.exports = new PayPalService();
