const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

class AirlineLogoService {
  constructor() {
    this.logoCache = new Map();
    this.logoDir = path.join(__dirname, '../assets/airline-logos');
    // Don't call async method in constructor
  }

  // Ensure logo directory exists
  async ensureLogoDirectory() {
    try {
      await fs.mkdir(this.logoDir, { recursive: true });
    } catch (error) {
      console.error('Error creating logo directory:', error);
    }
  }

  // Comprehensive airline logo database with multiple sources
  getLogoSources(airlineCode) {
    const logoDatabase = {
      // Major Airlines with multiple fallback sources
      'LH': {
        name: '<PERSON><PERSON>han<PERSON>',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/LH.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/LH.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Lufthansa-Logo.png'
        ],
        colors: { bg: '#FFD800', text: '#004080' }
      },
      'BA': {
        name: 'British Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/BA.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/BA.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png'
        ],
        colors: { bg: '#004775', text: 'white' }
      },
      'SK': {
        name: 'SAS',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/SK.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/SK.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/SAS-Logo.png'
        ],
        colors: { bg: '#004775', text: 'white' }
      },
      'EK': {
        name: 'Emirates',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/EK.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/EK.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Emirates-Logo.png'
        ],
        colors: { bg: '#FF0000', text: 'white' }
      },
      'AF': {
        name: 'Air France',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/AF.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/AF.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Air-France-Logo.png'
        ],
        colors: { bg: '#004775', text: 'white' }
      },
      'KL': {
        name: 'KLM',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/KL.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/KL.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/KLM-Logo.png'
        ],
        colors: { bg: '#0099CC', text: 'white' }
      },
      'TK': {
        name: 'Turkish Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/TK.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/TK.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Turkish-Airlines-Logo.png'
        ],
        colors: { bg: '#E30613', text: 'white' }
      },
      'QR': {
        name: 'Qatar Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/QR.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/QR.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Qatar-Airways-Logo.png'
        ],
        colors: { bg: '#681438', text: 'white' }
      },
      'AA': {
        name: 'American Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/AA.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/AA.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/American-Airlines-Logo.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'DL': {
        name: 'Delta Air Lines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/DL.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/DL.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Delta-Air-Lines-Logo.png'
        ],
        colors: { bg: '#003366', text: 'white' }
      },
      'UA': {
        name: 'United Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/UA.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/UA.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/United-Airlines-Logo.png'
        ],
        colors: { bg: '#003366', text: 'white' }
      },
      'TG': {
        name: 'Thai Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/TG.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/TG.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Thai-Airways-Logo.png'
        ],
        colors: { bg: '#662483', text: 'white' }
      },
      'SQ': {
        name: 'Singapore Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/SQ.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/SQ.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Singapore-Airlines-Logo.png'
        ],
        colors: { bg: '#003366', text: 'white' }
      },
      'FR': {
        name: 'Ryanair',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/FR.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/FR.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Ryanair-Logo.png'
        ],
        colors: { bg: '#FFD800', text: '#003366' }
      },
      'U2': {
        name: 'easyJet',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/U2.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/U2.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/easyJet-Logo.png'
        ],
        colors: { bg: '#FF6900', text: 'white' }
      },
      'PC': {
        name: 'Pegasus Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/PC.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/PC.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Pegasus-Airlines-Logo.png'
        ],
        colors: { bg: '#E30613', text: 'white' }
      },
      'IB': {
        name: 'Iberia',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/IB.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/IB.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Iberia-Logo.png'
        ],
        colors: { bg: '#E30613', text: 'white' }
      },
      'VY': {
        name: 'Vueling',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/VY.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/VY.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Vueling-Logo.png'
        ],
        colors: { bg: '#FFD800', text: '#003366' }
      },
      'W6': {
        name: 'Wizz Air',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/W6.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/W6.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Wizz-Air-Logo.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'LX': {
        name: 'Swiss International Air Lines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/LX.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/LX.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Swiss-International-Air-Lines-Logo.png'
        ],
        colors: { bg: '#E30613', text: 'white' }
      }
    };

    return logoDatabase[airlineCode] || null;
  }

  // Get the best available logo for an airline
  async getAirlineLogo(airlineCode, airlineName) {
    if (!airlineCode) {
      return this.generateSVGLogo('XX', airlineName || 'Airline');
    }

    // Check cache first
    const cacheKey = `${airlineCode}_${airlineName}`;
    if (this.logoCache.has(cacheKey)) {
      return this.logoCache.get(cacheKey);
    }

    const logoData = this.getLogoSources(airlineCode);
    
    if (logoData && logoData.sources.length > 0) {
      // Try to get a working logo URL
      for (const source of logoData.sources) {
        try {
          // For PDF generation, we'll use the first source (Google Static is most reliable)
          // In a production environment, you might want to validate the URL first
          this.logoCache.set(cacheKey, source);
          return source;
        } catch (error) {
          console.log(`Logo source failed for ${airlineCode}: ${source}`);
          continue;
        }
      }
    }

    // Fallback to generated SVG logo
    const fallbackLogo = this.generateSVGLogo(airlineCode, airlineName, logoData?.colors);
    this.logoCache.set(cacheKey, fallbackLogo);
    return fallbackLogo;
  }

  // Generate a branded SVG logo
  generateSVGLogo(airlineCode, airlineName, colors = null) {
    const defaultColors = { bg: '#2563EB', text: 'white' };
    const logoColors = colors || defaultColors;
    const displayCode = airlineCode || 'XX';

    const svgContent = `
      <svg width="70" height="45" viewBox="0 0 70 45" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="70" height="45" fill="${logoColors.bg}" rx="4"/>
        <text x="35" y="28" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="${logoColors.text}" text-anchor="middle">${displayCode}</text>
      </svg>
    `;

    return `data:image/svg+xml;base64,${Buffer.from(svgContent.trim()).toString('base64')}`;
  }

  // Validate if a URL is accessible (for future use)
  async validateLogoUrl(url) {
    try {
      const response = await axios.head(url, { timeout: 5000 });
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  // Clear cache (for testing/debugging)
  clearCache() {
    this.logoCache.clear();
  }
}

module.exports = new AirlineLogoService();
