/**
 * Enhanced Airline Logo Service for VerifiedOnward.com
 * Provides airline logos and branding for professional flight reservations
 */

const path = require('path');
const fs = require('fs').promises;

class AirlineLogoService {
  constructor() {
    this.logoCache = new Map();
    this.logoDirectory = path.join(__dirname, '../assets/airline-logos');
  }

  /**
   * Get airline logo URL or generate placeholder
   */
  async getAirlineLogo(airlineName) {
    if (!airlineName) return this.generatePlaceholderLogo('AIRLINE');

    const normalizedName = airlineName.toUpperCase().trim();
    
    // Check cache first
    if (this.logoCache.has(normalizedName)) {
      return this.logoCache.get(normalizedName);
    }

    // Try to find existing logo
    const logoPath = await this.findLogoFile(normalizedName);
    if (logoPath) {
      this.logoCache.set(normalizedName, logoPath);
      return logoPath;
    }

    // Generate placeholder logo
    const placeholderLogo = this.generatePlaceholderLogo(normalizedName);
    this.logoCache.set(normalizedName, placeholderLogo);
    return placeholderLogo;
  }

  /**
   * Find logo file for airline
   */
  async findLogoFile(airlineName) {
    const logoMap = {
      // Required airlines for professional e-tickets
      'RYANAIR': 'ryanair.png',
      'EASYJET': 'easyjet.png',
      'AEGEAN AIRLINES': 'aegean.png',
      'BRITISH AIRWAYS': 'british-airways.png',
      'LUFTHANSA': 'lufthansa.png',
      'AIR FRANCE': 'air-france.png',
      'KLM': 'klm.png',
      'TURKISH AIRLINES': 'turkish-airlines.png',
      'EMIRATES': 'emirates.png',
      'QATAR AIRWAYS': 'qatar-airways.png',

      // Additional supported airlines
      'CATHAY PACIFIC': 'cathay-pacific.png',
      'JAPAN AIRLINES': 'japan-airlines.png',
      'SINGAPORE AIRLINES': 'singapore-airlines.png',
      'AMERICAN AIRLINES': 'american-airlines.png',
      'DELTA AIR LINES': 'delta.png',
      'UNITED AIRLINES': 'united.png',
      'SWISS': 'swiss.png'
    };

    const fileName = logoMap[airlineName];
    if (!fileName) return null;

    const filePath = path.join(this.logoDirectory, fileName);
    
    try {
      await fs.access(filePath);
      return `/assets/airline-logos/${fileName}`;
    } catch (error) {
      return null;
    }
  }

  /**
   * Generate placeholder logo as SVG data URL
   */
  generatePlaceholderLogo(airlineName) {
    const initials = this.getAirlineInitials(airlineName);
    const color = this.getAirlineColor(airlineName);
    
    const svg = `
      <svg width="120" height="40" xmlns="http://www.w3.org/2000/svg">
        <rect width="120" height="40" fill="${color}" rx="4"/>
        <text x="60" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" 
              text-anchor="middle" fill="white">${initials}</text>
      </svg>
    `;

    return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;
  }

  /**
   * Get airline initials for logo
   */
  getAirlineInitials(airlineName) {
    if (!airlineName) return 'AL';
    
    const words = airlineName.split(' ').filter(word => word.length > 0);
    if (words.length === 1) {
      return words[0].substring(0, 2).toUpperCase();
    }
    
    return words.slice(0, 2).map(word => word[0]).join('').toUpperCase();
  }

  /**
   * Get brand color for airline
   */
  getAirlineColor(airlineName) {
    const colorMap = {
      // Required airlines for professional e-tickets
      'RYANAIR': '#073590',
      'EASYJET': '#FF6900',
      'AEGEAN AIRLINES': '#003F7F',
      'BRITISH AIRWAYS': '#075AAA',
      'LUFTHANSA': '#F9BA00',
      'AIR FRANCE': '#002157',
      'KLM': '#006DB7',
      'TURKISH AIRLINES': '#E30613',
      'EMIRATES': '#FF0000',
      'QATAR AIRWAYS': '#5C0A3B',

      // Additional supported airlines
      'CATHAY PACIFIC': '#00B2A9',
      'JAPAN AIRLINES': '#DC143C',
      'SINGAPORE AIRLINES': '#1E3A8A',
      'AMERICAN AIRLINES': '#C8102E',
      'DELTA AIR LINES': '#003366',
      'UNITED AIRLINES': '#0039A6',
      'SWISS': '#E30613'
    };

    return colorMap[airlineName?.toUpperCase()] || '#1F2937';
  }

  /**
   * Get airline IATA code
   */
  getAirlineCode(airlineName) {
    const codeMap = {
      // Required airlines for professional e-tickets
      'RYANAIR': 'FR',
      'EASYJET': 'U2',
      'AEGEAN AIRLINES': 'A3',
      'BRITISH AIRWAYS': 'BA',
      'LUFTHANSA': 'LH',
      'AIR FRANCE': 'AF',
      'KLM': 'KL',
      'TURKISH AIRLINES': 'TK',
      'EMIRATES': 'EK',
      'QATAR AIRWAYS': 'QR',

      // Additional supported airlines
      'CATHAY PACIFIC': 'CX',
      'JAPAN AIRLINES': 'JL',
      'SINGAPORE AIRLINES': 'SQ',
      'AMERICAN AIRLINES': 'AA',
      'DELTA AIR LINES': 'DL',
      'UNITED AIRLINES': 'UA',
      'SWISS': 'LX'
    };

    return codeMap[airlineName?.toUpperCase()] || 'XX';
  }

  /**
   * Clear logo cache
   */
  clearCache() {
    this.logoCache.clear();
    console.log('🧹 Airline logo cache cleared');
  }
}

module.exports = new AirlineLogoService();
