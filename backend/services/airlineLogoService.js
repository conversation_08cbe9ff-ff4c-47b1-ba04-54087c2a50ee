// Import fetch for Node.js environments
const fetch = require('node-fetch');

class AirlineLogoService {
  constructor() {
    this.logoCache = new Map();
  }

  // Comprehensive airline logo database with multiple sources
  getLogoSources(airlineCode) {
    const logoDatabase = {
      // Required airlines for professional e-tickets
      'FR': {
        name: 'Ryanair',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/FR.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/FR.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Ryanair-Logo.png'
        ],
        colors: { bg: '#073590', text: 'white' }
      },
      'U2': {
        name: 'easyJet',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/U2.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/U2.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/EasyJet-Logo.png'
        ],
        colors: { bg: '#FF6900', text: 'white' }
      },
      'A3': {
        name: 'Aegean Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/A3.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/A3.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Aegean-Airlines-Logo.png'
        ],
        colors: { bg: '#003F7F', text: 'white' }
      },
      'BA': {
        name: 'British Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/BA.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/BA.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png'
        ],
        colors: { bg: '#075AAA', text: 'white' }
      },
      'LH': {
        name: 'Lufthansa',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/LH.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/LH.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Lufthansa-Logo.png'
        ],
        colors: { bg: '#F9BA00', text: '#004080' }
      },
      'AF': {
        name: 'Air France',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/AF.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/AF.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Air-France-Logo.png'
        ],
        colors: { bg: '#002157', text: 'white' }
      },
      'KL': {
        name: 'KLM',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/KL.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/KL.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/KLM-Logo.png'
        ],
        colors: { bg: '#006DB7', text: 'white' }
      },
      'TK': {
        name: 'Turkish Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/TK.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/TK.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Turkish-Airlines-Logo.png'
        ],
        colors: { bg: '#E30613', text: 'white' }
      },
      'EK': {
        name: 'Emirates',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/EK.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/EK.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Emirates-Logo.png'
        ],
        colors: { bg: '#FF0000', text: 'white' }
      },
      'QR': {
        name: 'Qatar Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/QR.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/QR.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Qatar-Airways-Logo.png'
        ],
        colors: { bg: '#5C0A3B', text: 'white' }
      },
      'BA': {
        name: 'British Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/BA.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/BA.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png'
        ],
        colors: { bg: '#004775', text: 'white' }
      },
      'SK': {
        name: 'SAS',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/SK.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/SK.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/SAS-Logo.png'
        ],
        colors: { bg: '#004775', text: 'white' }
      },
      'EK': {
        name: 'Emirates',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/EK.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/EK.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Emirates-Logo.png'
        ],
        colors: { bg: '#FF0000', text: 'white' }
      },
      'AF': {
        name: 'Air France',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/AF.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/AF.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Air-France-Logo.png'
        ],
        colors: { bg: '#004775', text: 'white' }
      },
      'KL': {
        name: 'KLM',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/KL.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/KL.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/KLM-Logo.png'
        ],
        colors: { bg: '#0099CC', text: 'white' }
      },
      'TK': {
        name: 'Turkish Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/TK.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/TK.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Turkish-Airlines-Logo.png'
        ],
        colors: { bg: '#E30613', text: 'white' }
      },
      'QR': {
        name: 'Qatar Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/QR.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/QR.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Qatar-Airways-Logo.png'
        ],
        colors: { bg: '#681438', text: 'white' }
      },
      'AA': {
        name: 'American Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/AA.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/AA.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/American-Airlines-Logo.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'DL': {
        name: 'Delta Air Lines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/DL.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/DL.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Delta-Air-Lines-Logo.png'
        ],
        colors: { bg: '#003366', text: 'white' }
      },
      'UA': {
        name: 'United Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/UA.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/UA.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/United-Airlines-Logo.png'
        ],
        colors: { bg: '#003366', text: 'white' }
      },
      'TG': {
        name: 'Thai Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/TG.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/TG.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Thai-Airways-Logo.png'
        ],
        colors: { bg: '#662483', text: 'white' }
      },
      'SQ': {
        name: 'Singapore Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/SQ.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/SQ.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Singapore-Airlines-Logo.png'
        ],
        colors: { bg: '#003366', text: 'white' }
      },
      'FR': {
        name: 'Ryanair',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/FR.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/FR.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Ryanair-Logo.png'
        ],
        colors: { bg: '#FFD800', text: '#003366' }
      },
      'U2': {
        name: 'easyJet',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/U2.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/U2.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/easyJet-Logo.png'
        ],
        colors: { bg: '#FF6900', text: 'white' }
      },
      'PC': {
        name: 'Pegasus Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/PC.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/PC.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Pegasus-Airlines-Logo.png'
        ],
        colors: { bg: '#E30613', text: 'white' }
      },
      'IB': {
        name: 'Iberia',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/IB.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/IB.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Iberia-Logo.png'
        ],
        colors: { bg: '#E30613', text: 'white' }
      },
      'VY': {
        name: 'Vueling',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/VY.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/VY.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Vueling-Logo.png'
        ],
        colors: { bg: '#FFD800', text: '#003366' }
      },
      'W6': {
        name: 'Wizz Air',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/W6.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/W6.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Wizz-Air-Logo.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'LX': {
        name: 'Swiss International Air Lines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/LX.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/LX.png?crop=false&width=108&height=92',
          'https://logos-world.net/wp-content/uploads/2020/03/Swiss-International-Air-Lines-Logo.png'
        ],
        colors: { bg: '#E30613', text: 'white' }
      },
      'LS': {
        name: 'Jet2',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/LS.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/LS.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/LS.png'
        ],
        colors: { bg: '#F39800', text: 'white' }
      },
      'VY': {
        name: 'Vueling',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/VY.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/VY.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/VY.png'
        ],
        colors: { bg: '#FFD800', text: '#000' }
      },
      'W6': {
        name: 'Wizz Air',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/W6.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/W6.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/W6.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'DY': {
        name: 'Norwegian',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/DY.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/DY.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/DY.png'
        ],
        colors: { bg: '#D81939', text: 'white' }
      },

      // Asian Airlines
      'KE': {
        name: 'Korean Air',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/KE.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/KE.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/KE.png'
        ],
        colors: { bg: '#0F4C96', text: 'white' }
      },
      'TG': {
        name: 'Thai Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/TG.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/TG.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/TG.png'
        ],
        colors: { bg: '#7B2D8E', text: 'white' }
      },
      'MH': {
        name: 'Malaysia Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/MH.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/MH.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/MH.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'PR': {
        name: 'Philippine Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/PR.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/PR.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/PR.png'
        ],
        colors: { bg: '#003F7F', text: 'white' }
      },
      'CI': {
        name: 'China Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/CI.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/CI.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/CI.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'MU': {
        name: 'China Eastern',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/MU.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/MU.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/MU.png'
        ],
        colors: { bg: '#003F7F', text: 'white' }
      },
      'CZ': {
        name: 'China Southern',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/CZ.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/CZ.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/CZ.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'OZ': {
        name: 'Asiana Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/OZ.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/OZ.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/OZ.png'
        ],
        colors: { bg: '#E30613', text: 'white' }
      },
      'VN': {
        name: 'Vietnam Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/VN.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/VN.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/VN.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'GA': {
        name: 'Garuda Indonesia',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/GA.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/GA.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/GA.png'
        ],
        colors: { bg: '#003F7F', text: 'white' }
      },
      '5J': {
        name: 'Cebu Pacific',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/5J.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/5J.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/5J.png'
        ],
        colors: { bg: '#FF6900', text: 'white' }
      },
      '3K': {
        name: 'Jetstar Asia',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/3K.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/3K.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/3K.png'
        ],
        colors: { bg: '#FF6900', text: 'white' }
      },

      // Middle Eastern Airlines
      'EY': {
        name: 'Etihad Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/EY.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/EY.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/EY.png'
        ],
        colors: { bg: '#FFD800', text: '#000' }
      },
      'SV': {
        name: 'Saudia',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/SV.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/SV.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/SV.png'
        ],
        colors: { bg: '#006633', text: 'white' }
      },
      'GF': {
        name: 'Gulf Air',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/GF.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/GF.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/GF.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'WY': {
        name: 'Oman Air',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/WY.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/WY.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/WY.png'
        ],
        colors: { bg: '#7B2D8E', text: 'white' }
      },
      'FZ': {
        name: 'flydubai',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/FZ.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/FZ.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/FZ.png'
        ],
        colors: { bg: '#00A651', text: 'white' }
      },
      'G9': {
        name: 'Air Arabia',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/G9.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/G9.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/G9.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },

      // African Airlines
      'ET': {
        name: 'Ethiopian Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/ET.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/ET.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/ET.png'
        ],
        colors: { bg: '#006633', text: 'white' }
      },
      'SA': {
        name: 'South African Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/SA.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/SA.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/SA.png'
        ],
        colors: { bg: '#003F7F', text: 'white' }
      },
      'KQ': {
        name: 'Kenya Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/KQ.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/KQ.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/KQ.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'MS': {
        name: 'EgyptAir',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/MS.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/MS.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/MS.png'
        ],
        colors: { bg: '#003F7F', text: 'white' }
      },
      'AT': {
        name: 'Royal Air Maroc',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/AT.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/AT.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/AT.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'TU': {
        name: 'Tunisair',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/TU.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/TU.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/TU.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'AH': {
        name: 'Air Algerie',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/AH.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/AH.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/AH.png'
        ],
        colors: { bg: '#006633', text: 'white' }
      },
      'WB': {
        name: 'RwandAir',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/WB.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/WB.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/WB.png'
        ],
        colors: { bg: '#003F7F', text: 'white' }
      },

      // South American Airlines
      'LA': {
        name: 'LATAM',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/LA.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/LA.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/LA.png'
        ],
        colors: { bg: '#7B2D8E', text: 'white' }
      },
      'AV': {
        name: 'Avianca',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/AV.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/AV.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/AV.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'CM': {
        name: 'Copa Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/CM.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/CM.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/CM.png'
        ],
        colors: { bg: '#003F7F', text: 'white' }
      },
      'G3': {
        name: 'GOL',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/G3.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/G3.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/G3.png'
        ],
        colors: { bg: '#FF6900', text: 'white' }
      },
      'AD': {
        name: 'Azul',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/AD.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/AD.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/AD.png'
        ],
        colors: { bg: '#003F7F', text: 'white' }
      },

      // Missing European Airlines
      'BE': {
        name: 'Flybe',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/BE.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/BE.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/BE.png'
        ],
        colors: { bg: '#7B2D8E', text: 'white' }
      },
      'T7': {
        name: 'TwinJet',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/T7.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/T7.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/T7.png'
        ],
        colors: { bg: '#003F7F', text: 'white' }
      },
      'TP': {
        name: 'TAP Air Portugal',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/TP.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/TP.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/TP.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'AZ': {
        name: 'Alitalia',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/AZ.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/AZ.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/AZ.png'
        ],
        colors: { bg: '#006633', text: 'white' }
      },
      'SU': {
        name: 'Aeroflot',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/SU.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/SU.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/SU.png'
        ],
        colors: { bg: '#0F4C96', text: 'white' }
      },
      'AY': {
        name: 'Finnair',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/AY.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/AY.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/AY.png'
        ],
        colors: { bg: '#003F7F', text: 'white' }
      },

      // Missing Asian Airlines
      'CX': {
        name: 'Cathay Pacific',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/CX.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/CX.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/CX.png'
        ],
        colors: { bg: '#00B2A9', text: 'white' }
      },
      'JL': {
        name: 'Japan Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/JL.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/JL.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/JL.png'
        ],
        colors: { bg: '#DC143C', text: 'white' }
      },

      // Missing African Airlines
      'FN': {
        name: 'Fastjet',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/FN.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/FN.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/FN.png'
        ],
        colors: { bg: '#FF6900', text: 'white' }
      },

      // Missing South American Airlines
      'H2': {
        name: 'Sky Airline',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/H2.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/H2.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/H2.png'
        ],
        colors: { bg: '#003F7F', text: 'white' }
      },
      'VV': {
        name: 'Viva Air',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/VV.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/VV.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/VV.png'
        ],
        colors: { bg: '#FF6900', text: 'white' }
      },

      // Missing Oceania Airlines
      'TT': {
        name: 'Tigerair',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/TT.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/TT.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/TT.png'
        ],
        colors: { bg: '#FF6900', text: 'white' }
      },

      // Oceania Airlines
      'QF': {
        name: 'Qantas',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/QF.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/QF.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/QF.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      },
      'JQ': {
        name: 'Jetstar',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/JQ.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/JQ.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/JQ.png'
        ],
        colors: { bg: '#FF6900', text: 'white' }
      },
      'NZ': {
        name: 'Air New Zealand',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/NZ.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/NZ.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/NZ.png'
        ],
        colors: { bg: '#000', text: 'white' }
      },
      'VA': {
        name: 'Virgin Australia',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/VA.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/VA.png?crop=false&width=108&height=92',
          'https://www.flightaware.com/images/airline_logos/90p/VA.png'
        ],
        colors: { bg: '#C41E3A', text: 'white' }
      }
    };

    return logoDatabase[airlineCode] || null;
  }

  // Get the best available logo for an airline
  async getAirlineLogo(airlineCode, airlineName) {
    if (!airlineCode) {
      return this.generateSVGLogo('XX', airlineName || 'Airline');
    }

    // Check cache first
    const cacheKey = `${airlineCode}_${airlineName}`;
    if (this.logoCache.has(cacheKey)) {
      return this.logoCache.get(cacheKey);
    }

    // Try multiple logo sources in order of reliability
    const logoSources = this.getAllLogoSources(airlineCode);

    for (const source of logoSources) {
      try {
        // For PDF generation, we'll use the URL directly
        // The browser will handle the loading
        this.logoCache.set(cacheKey, source);
        return source;
      } catch (error) {
        console.log(`Logo source failed for ${airlineCode}: ${source}`);
        continue;
      }
    }

    // Fallback to generated SVG logo with airline-specific colors
    const logoData = this.getLogoSources(airlineCode);
    const fallbackLogo = this.generateSVGLogo(airlineCode, airlineName, logoData?.colors);
    this.logoCache.set(cacheKey, fallbackLogo);
    return fallbackLogo;
  }

  // Get high-resolution logo for PDF generation with enhanced error handling
  async getLogoForPDF(airlineCode, airlineName) {
    const cacheKey = `pdf_${airlineCode}_${airlineName}`;

    // Check cache first
    if (this.logoCache.has(cacheKey)) {
      return this.logoCache.get(cacheKey);
    }

    // Log attempt for debugging
    console.log(`🔍 Fetching logo for ${airlineName} (${airlineCode})`);

    // Try to get high-resolution logo from multiple sources
    const logoSources = this.getAllLogoSources(airlineCode);
    let lastError = null;

    for (let i = 0; i < logoSources.length; i++) {
      const logoUrl = logoSources[i];
      try {
        console.log(`   Trying source ${i + 1}/${logoSources.length}: ${logoUrl}`);

        const response = await fetch(logoUrl, {
          timeout: 10000, // 10 second timeout
          headers: {
            'User-Agent': 'VerifiedOnward-PDF-Generator/1.0'
          }
        });

        if (response.ok) {
          // Convert to base64 for embedding in PDF
          const arrayBuffer = await response.arrayBuffer();
          const buffer = Buffer.from(arrayBuffer);
          const base64Logo = `data:image/png;base64,${buffer.toString('base64')}`;

          console.log(`   ✅ Successfully fetched logo from source ${i + 1} (${buffer.length} bytes)`);
          this.logoCache.set(cacheKey, base64Logo);
          return base64Logo;
        } else {
          console.log(`   ⚠️  HTTP ${response.status} from source ${i + 1}`);
          lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.log(`   ❌ Failed to fetch from source ${i + 1}: ${error.message}`);
        lastError = error;
        continue;
      }
    }

    // Log fallback usage
    console.log(`   🎨 Using SVG fallback for ${airlineName} (${airlineCode}) - all ${logoSources.length} sources failed`);
    if (lastError) {
      console.log(`   Last error: ${lastError.message}`);
    }

    // Fallback to generated SVG logo with enhanced styling
    const logoData = this.getLogoSources(airlineCode);
    const fallbackLogo = this.generateSVGLogo(airlineCode, airlineName, logoData?.colors);
    this.logoCache.set(cacheKey, fallbackLogo);

    // Log successful fallback
    console.log(`   ✅ Generated SVG fallback for ${airlineName} (${airlineCode})`);
    return fallbackLogo;
  }

  // Get all possible logo sources for any airline (universal approach)
  getAllLogoSources(airlineCode) {
    // First check our curated database
    const curatedData = this.getLogoSources(airlineCode);
    if (curatedData && curatedData.sources.length > 0) {
      return curatedData.sources;
    }

    // Universal fallback sources that work for most airlines
    return [
      `https://www.gstatic.com/flights/airline_logos/70px/${airlineCode}.png`,
      `https://content.r9cdn.net/rimg/provider-logos/airlines/v/${airlineCode}.png?crop=false&width=108&height=92`,
      `https://www.flightaware.com/images/airline_logos/90p/${airlineCode}.png`,
      `https://images.kiwi.com/airlines/64x64/${airlineCode}.png`,
      `https://www.gstatic.com/flights/airline_logos/35px/${airlineCode}.png`
    ];
  }

  // Generate a branded SVG logo with enhanced styling
  generateSVGLogo(airlineCode, airlineName, colors = null) {
    const displayCode = airlineCode || 'XX';
    const logoColors = colors || this.getDefaultAirlineColors(airlineCode);

    // Create a more professional-looking SVG logo
    const svgContent = `
      <svg width="70" height="45" viewBox="0 0 70 45" fill="none" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad${displayCode}" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${logoColors.bg};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${this.darkenColor(logoColors.bg, 20)};stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="70" height="45" fill="url(#grad${displayCode})" rx="6" stroke="${this.darkenColor(logoColors.bg, 30)}" stroke-width="1"/>
        <text x="35" y="30" font-family="Arial, Helvetica, sans-serif" font-size="14" font-weight="bold" fill="${logoColors.text}" text-anchor="middle">${displayCode}</text>
        ${airlineName && airlineName.length <= 12 ? `<text x="35" y="40" font-family="Arial, Helvetica, sans-serif" font-size="6" fill="${logoColors.text}" text-anchor="middle" opacity="0.8">${airlineName.toUpperCase()}</text>` : ''}
      </svg>
    `;

    return `data:image/svg+xml;base64,${Buffer.from(svgContent.trim()).toString('base64')}`;
  }

  // Get default colors based on airline code patterns
  getDefaultAirlineColors(airlineCode) {
    const colorMap = {
      // European carriers - often blue/red
      'LH': { bg: '#FFD800', text: '#004080' }, // Lufthansa - yellow/blue
      'BA': { bg: '#004775', text: 'white' },   // British Airways - navy
      'AF': { bg: '#004775', text: 'white' },   // Air France - navy
      'KL': { bg: '#0099CC', text: 'white' },   // KLM - light blue

      // Middle Eastern carriers - often red/gold
      'EK': { bg: '#FF0000', text: 'white' },   // Emirates - red
      'QR': { bg: '#681438', text: 'white' },   // Qatar - burgundy
      'TK': { bg: '#E30613', text: 'white' },   // Turkish - red

      // US carriers - often blue/red
      'AA': { bg: '#C41E3A', text: 'white' },   // American - red
      'DL': { bg: '#003366', text: 'white' },   // Delta - navy
      'UA': { bg: '#003366', text: 'white' },   // United - navy

      // Low-cost carriers - often bright colors
      'FR': { bg: '#FFD800', text: '#003366' }, // Ryanair - yellow/blue
      'U2': { bg: '#FF6900', text: 'white' },   // easyJet - orange
      'W6': { bg: '#C41E3A', text: 'white' },   // Wizz Air - magenta
    };

    // Return specific colors if available, otherwise use pattern-based defaults
    if (colorMap[airlineCode]) {
      return colorMap[airlineCode];
    }

    // Pattern-based color assignment for unknown airlines
    const codeSum = airlineCode.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
    const colorOptions = [
      { bg: '#2563EB', text: 'white' }, // Blue
      { bg: '#DC2626', text: 'white' }, // Red
      { bg: '#059669', text: 'white' }, // Green
      { bg: '#7C3AED', text: 'white' }, // Purple
      { bg: '#EA580C', text: 'white' }, // Orange
      { bg: '#0891B2', text: 'white' }, // Cyan
      { bg: '#BE185D', text: 'white' }, // Pink
      { bg: '#4338CA', text: 'white' }, // Indigo
    ];

    return colorOptions[codeSum % colorOptions.length];
  }

  // Helper function to darken a color
  darkenColor(color, percent) {
    // Simple color darkening for hex colors
    if (color.startsWith('#')) {
      const num = parseInt(color.replace('#', ''), 16);
      const amt = Math.round(2.55 * percent);
      const R = (num >> 16) - amt;
      const G = (num >> 8 & 0x00FF) - amt;
      const B = (num & 0x0000FF) - amt;
      return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
        (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
        (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
    return color;
  }



  // Get airline IATA code from airline name
  getAirlineCode(airlineName) {
    const codeMap = {
      'RYANAIR': 'FR',
      'EASYJET': 'U2',
      'AEGEAN AIRLINES': 'A3',
      'BRITISH AIRWAYS': 'BA',
      'LUFTHANSA': 'LH',
      'AIR FRANCE': 'AF',
      'KLM': 'KL',
      'TURKISH AIRLINES': 'TK',
      'EMIRATES': 'EK',
      'QATAR AIRWAYS': 'QR',
      'CATHAY PACIFIC': 'CX',
      'JAPAN AIRLINES': 'JL',
      'SINGAPORE AIRLINES': 'SQ',
      'AMERICAN AIRLINES': 'AA',
      'DELTA AIR LINES': 'DL',
      'UNITED AIRLINES': 'UA',
      'SWISS': 'LX'
    };
    return codeMap[airlineName?.toUpperCase()] || 'XX';
  }

  // Clear cache (for testing/debugging)
  clearCache() {
    this.logoCache.clear();
  }

  // Get comprehensive logo statistics for monitoring
  getLogoStats() {
    const stats = {
      cacheSize: this.logoCache.size,
      supportedAirlines: 0,
      logoSources: {},
      lastErrors: []
    };

    // Count supported airlines by checking the database
    const testCodes = ['FR', 'U2', 'BA', 'LH', 'AF', 'KL', 'TK', 'EK', 'QR', 'ET', 'AA', 'DL', 'UA'];
    testCodes.forEach(code => {
      const sources = this.getLogoSources(code);
      if (sources) {
        stats.supportedAirlines++;
        stats.logoSources[code] = sources.sources.length;
      }
    });

    return stats;
  }

  // Validate logo URL accessibility (for health checks)
  async validateLogoUrl(url, timeout = 5000) {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        timeout: timeout,
        headers: {
          'User-Agent': 'VerifiedOnward-Logo-Validator/1.0'
        }
      });
      return {
        url,
        accessible: response.ok,
        status: response.status,
        contentType: response.headers.get('content-type')
      };
    } catch (error) {
      return {
        url,
        accessible: false,
        error: error.message
      };
    }
  }

  // Preload critical airline logos for better performance
  async preloadCriticalLogos() {
    const criticalAirlines = [
      { code: 'ET', name: 'Ethiopian Airlines' },
      { code: 'TK', name: 'Turkish Airlines' },
      { code: 'EK', name: 'Emirates' },
      { code: 'QR', name: 'Qatar Airways' },
      { code: 'BA', name: 'British Airways' },
      { code: 'LH', name: 'Lufthansa' },
      { code: 'AF', name: 'Air France' },
      { code: 'KL', name: 'KLM' }
    ];

    console.log('🚀 Preloading critical airline logos...');
    const results = [];

    for (const airline of criticalAirlines) {
      try {
        const logo = await this.getLogoForPDF(airline.code, airline.name);
        results.push({
          airline: airline.name,
          code: airline.code,
          success: true,
          logoType: logo.startsWith('data:image/png') ? 'PNG' :
                   logo.startsWith('data:image/svg') ? 'SVG' : 'URL'
        });
        console.log(`   ✅ ${airline.name} (${airline.code})`);
      } catch (error) {
        results.push({
          airline: airline.name,
          code: airline.code,
          success: false,
          error: error.message
        });
        console.log(`   ❌ ${airline.name} (${airline.code}): ${error.message}`);
      }
    }

    console.log(`✅ Preloaded ${results.filter(r => r.success).length}/${results.length} critical logos`);
    return results;
  }
}

module.exports = new AirlineLogoService();
