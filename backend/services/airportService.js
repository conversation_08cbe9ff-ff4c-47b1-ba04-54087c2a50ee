// Comprehensive Airport Service
// Combines all airport databases for autocomplete functionality

const { allAirports } = require('../data/allAirports');
const { asiaAirports } = require('../data/asiaAirports');
const { middleEastEuropeAirports } = require('../data/middleEastEuropeAirports');
const { europeOceaniaAirports } = require('../data/europeOceaniaAirports');
const { usAirports, canadianAirports } = require('../data/airports');
const { europeanAirports } = require('../data/europeanAirports');
const { internationalAirports } = require('../data/internationalAirports');
const { otherContinentsAirports } = require('../data/otherContinentsAirports');

class AirportService {
  constructor() {
    // Combine all airport databases into one comprehensive list
    this.allAirports = [
      ...allAirports,
      ...asiaAirports,
      ...middleEastEuropeAirports,
      ...europeOceaniaAirports,
      ...usAirports,
      ...canadianAirports,
      ...europeanAirports,
      ...internationalAirports,
      ...otherContinentsAirports
    ];

    // Remove duplicates based on IATA code
    this.allAirports = this.removeDuplicates(this.allAirports);

    console.log(`🌍 Airport Service initialized with ${this.allAirports.length} airports worldwide`);
  }

  // Remove duplicate airports based on IATA code
  removeDuplicates(airports) {
    const seen = new Set();
    return airports.filter(airport => {
      if (seen.has(airport.iataCode)) {
        return false;
      }
      seen.add(airport.iataCode);
      return true;
    });
  }

  // Search airports with comprehensive matching
  searchAirports(query, limit = 15) {
    if (!query || query.length < 2) {
      return [];
    }

    const searchTerm = query.toLowerCase().trim();
    const results = [];

    // Search through all airports
    for (const airport of this.allAirports) {
      const score = this.calculateRelevanceScore(airport, searchTerm);
      if (score > 0) {
        results.push({
          ...airport,
          score
        });
      }
    }

    // Sort by relevance score (higher is better) and limit results
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(({ score, ...airport }) => airport); // Remove score from final result
  }

  // Calculate relevance score for search matching
  calculateRelevanceScore(airport, searchTerm) {
    let score = 0;

    // Exact IATA code match (highest priority)
    if (airport.iataCode.toLowerCase() === searchTerm) {
      score += 1000;
    }
    // IATA code starts with search term
    else if (airport.iataCode.toLowerCase().startsWith(searchTerm)) {
      score += 500;
    }
    // IATA code contains search term
    else if (airport.iataCode.toLowerCase().includes(searchTerm)) {
      score += 100;
    }

    // City name exact match
    if (airport.city.toLowerCase() === searchTerm) {
      score += 800;
    }
    // City name starts with search term
    else if (airport.city.toLowerCase().startsWith(searchTerm)) {
      score += 400;
    }
    // City name contains search term
    else if (airport.city.toLowerCase().includes(searchTerm)) {
      score += 80;
    }

    // Airport name exact match
    if (airport.name.toLowerCase() === searchTerm) {
      score += 600;
    }
    // Airport name starts with search term
    else if (airport.name.toLowerCase().startsWith(searchTerm)) {
      score += 300;
    }
    // Airport name contains search term
    else if (airport.name.toLowerCase().includes(searchTerm)) {
      score += 60;
    }

    // Country name exact match
    if (airport.country.toLowerCase() === searchTerm) {
      score += 200;
    }
    // Country name starts with search term
    else if (airport.country.toLowerCase().startsWith(searchTerm)) {
      score += 100;
    }
    // Country name contains search term
    else if (airport.country.toLowerCase().includes(searchTerm)) {
      score += 20;
    }

    // Boost score for major airports (common IATA codes)
    const majorAirports = [
      'ATL', 'LAX', 'ORD', 'DFW', 'DEN', 'JFK', 'SFO', 'LAS', 'SEA', 'MIA', // US
      'LHR', 'LGW', 'MAN', 'EDI', 'GLA', 'BHX', 'STN', 'LTN', // UK
      'CDG', 'ORY', 'NCE', 'LYS', 'MRS', 'TLS', // France
      'FRA', 'MUC', 'DUS', 'BER', 'HAM', 'CGN', // Germany
      'AMS', 'BRU', 'ZUR', 'GVA', 'VIE', 'CPH', // Western Europe
      'MAD', 'BCN', 'PMI', 'LIS', 'OPO', 'FCO', 'MXP', // Southern Europe
      'NRT', 'HND', 'KIX', 'ICN', 'PVG', 'PEK', 'HKG', 'SIN', 'BKK', // Asia
      'DXB', 'DOH', 'AUH', 'IST', 'TLV', 'CAI', // Middle East
      'SYD', 'MEL', 'BNE', 'PER', 'AKL', 'CHC', // Oceania
      'YYZ', 'YVR', 'YUL', 'YYC', // Canada
      'GRU', 'GIG', 'SCL', 'BOG', 'LIM', 'EZE', // South America
      'JNB', 'CPT', 'CAI', 'ADD', 'NBO', 'LOS' // Africa
    ];

    if (majorAirports.includes(airport.iataCode)) {
      score += 50;
    }

    return score;
  }

  // Get airport by IATA code
  getAirportByCode(iataCode) {
    if (!iataCode || iataCode.length !== 3) {
      return null;
    }

    return this.allAirports.find(
      airport => airport.iataCode.toUpperCase() === iataCode.toUpperCase()
    );
  }

  // Get airports by country
  getAirportsByCountry(country, limit = 50) {
    if (!country) {
      return [];
    }

    return this.allAirports
      .filter(airport => 
        airport.country.toLowerCase().includes(country.toLowerCase())
      )
      .slice(0, limit);
  }

  // Get airports by city
  getAirportsByCity(city, limit = 20) {
    if (!city) {
      return [];
    }

    return this.allAirports
      .filter(airport => 
        airport.city.toLowerCase().includes(city.toLowerCase())
      )
      .slice(0, limit);
  }

  // Get random popular airports for suggestions
  getPopularAirports(limit = 20) {
    const popularCodes = [
      'ATL', 'LAX', 'ORD', 'DFW', 'DEN', 'JFK', 'SFO', 'LAS', 'SEA', 'MIA',
      'LHR', 'LGW', 'MAN', 'CDG', 'AMS', 'FRA', 'MUC', 'MAD', 'BCN', 'FCO',
      'NRT', 'ICN', 'PVG', 'HKG', 'SIN', 'BKK', 'DXB', 'DOH', 'SYD', 'MEL'
    ];

    return popularCodes
      .map(code => this.getAirportByCode(code))
      .filter(airport => airport !== null)
      .slice(0, limit);
  }

  // Get total airport count
  getTotalAirportCount() {
    return this.allAirports.length;
  }

  // Get airports by continent/region
  getAirportsByRegion(region, limit = 100) {
    const regionKeywords = {
      'europe': ['United Kingdom', 'France', 'Germany', 'Spain', 'Italy', 'Netherlands', 'Belgium', 'Switzerland', 'Austria', 'Portugal', 'Greece', 'Poland', 'Czech Republic', 'Hungary', 'Croatia', 'Slovenia', 'Slovakia', 'Romania', 'Bulgaria', 'Ireland', 'Denmark', 'Sweden', 'Norway', 'Finland', 'Iceland'],
      'asia': ['China', 'Japan', 'South Korea', 'India', 'Thailand', 'Singapore', 'Malaysia', 'Indonesia', 'Philippines', 'Vietnam', 'Taiwan', 'Hong Kong', 'Macau', 'Mongolia', 'Kazakhstan', 'Uzbekistan'],
      'middle-east': ['United Arab Emirates', 'Saudi Arabia', 'Qatar', 'Kuwait', 'Bahrain', 'Oman', 'Iran', 'Iraq', 'Israel', 'Jordan', 'Lebanon', 'Syria', 'Turkey', 'Cyprus', 'Georgia', 'Armenia', 'Azerbaijan'],
      'north-america': ['United States', 'Canada', 'Mexico', 'Greenland'],
      'south-america': ['Brazil', 'Argentina', 'Chile', 'Colombia', 'Peru', 'Venezuela', 'Ecuador', 'Bolivia', 'Uruguay', 'Paraguay', 'Guyana', 'Suriname', 'French Guiana'],
      'africa': ['South Africa', 'Egypt', 'Morocco', 'Kenya', 'Nigeria', 'Ethiopia', 'Ghana', 'Tanzania', 'Uganda', 'Zimbabwe', 'Zambia', 'Botswana', 'Namibia', 'Tunisia', 'Algeria', 'Libya'],
      'oceania': ['Australia', 'New Zealand', 'Fiji', 'Papua New Guinea', 'Samoa', 'Tonga', 'Vanuatu', 'Solomon Islands', 'Guam', 'French Polynesia']
    };

    const countries = regionKeywords[region.toLowerCase()] || [];
    if (countries.length === 0) {
      return [];
    }

    return this.allAirports
      .filter(airport => 
        countries.some(country => 
          airport.country.toLowerCase().includes(country.toLowerCase())
        )
      )
      .slice(0, limit);
  }
}

// Create singleton instance
const airportService = new AirportService();

module.exports = airportService;
