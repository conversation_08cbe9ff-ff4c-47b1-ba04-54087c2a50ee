const nodemailer = require('nodemailer');

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });
  }

  // Send flight document email with attachment
  async sendTicketEmail({ to, ticketData, pdfBuffer }) {
    try {
      const { bookingReference, flight, airline, passengers } = ticketData;
      const passenger = passengers?.[0] || {};

      // Demo mode - simulate email sending
      console.log('📧 Simulating email send to:', to);
      console.log('📎 Flight document attachment size:', pdfBuffer.length, 'bytes');
      console.log('🎫 Booking reference:', bookingReference);
      console.log('✅ Email sent successfully (demo mode)');

      return {
        messageId: 'demo_' + Date.now(),
        success: true
      };

      // Uncomment below for real email sending
      /*
      const mailOptions = {
        from: process.env.EMAIL_FROM,
        to: to,
        subject: `Your Embassy-Approved Flight Reservation`,
        html: this.generateEmailHTML(ticketData, to),
        attachments: [
          {
            filename: `${bookingReference}_FlightReservation.pdf`,
            content: pdfBuffer,
            contentType: 'application/pdf'
          }
        ]
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      return result;
      */

    } catch (error) {
      console.error('Email sending error:', error);
      throw new Error('Failed to send flight document email');
    }
  }

  // Generate HTML email content
  generateEmailHTML(ticketData, to) {
    const { bookingReference, flight, airline, passengers, price, email } = ticketData;
    const passenger = passengers?.[0] || {};
    const recipientEmail = to || email;

    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your VerifiedOnward Flight Reservation</title>
        <style>
            body {
                font-family: 'Arial', sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f4f4f4;
            }
            
            .container {
                background: white;
                border-radius: 10px;
                padding: 30px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            }
            
            .header {
                text-align: center;
                border-bottom: 2px solid #1e3c72;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }
            
            .logo {
                font-size: 24px;
                font-weight: bold;
                color: #1e3c72;
                margin-bottom: 10px;
            }
            
            .title {
                font-size: 28px;
                color: #1e3c72;
                margin-bottom: 10px;
            }
            
            .booking-ref {
                font-size: 18px;
                color: #666;
                font-weight: bold;
                letter-spacing: 1px;
            }
            
            .flight-summary {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin: 20px 0;
            }
            
            .flight-route {
                font-size: 20px;
                font-weight: bold;
                color: #1e3c72;
                text-align: center;
                margin-bottom: 15px;
            }
            
            .flight-details {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                margin-bottom: 15px;
            }
            
            .detail-item {
                text-align: center;
            }
            
            .detail-label {
                font-size: 12px;
                color: #666;
                text-transform: uppercase;
                margin-bottom: 5px;
            }
            
            .detail-value {
                font-size: 16px;
                font-weight: bold;
                color: #333;
            }
            
            .passenger-info {
                background: #e3f2fd;
                border-radius: 8px;
                padding: 15px;
                margin: 20px 0;
            }
            
            .important-notice {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 8px;
                padding: 15px;
                margin: 20px 0;
                text-align: center;
            }
            
            .important-notice strong {
                color: #856404;
            }
            
            .footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                color: #666;
                font-size: 14px;
            }
            
            .button {
                display: inline-block;
                background: #1e3c72;
                color: white;
                padding: 12px 25px;
                text-decoration: none;
                border-radius: 5px;
                margin: 10px 0;
                font-weight: bold;
            }
            
            @media (max-width: 600px) {
                .flight-details {
                    grid-template-columns: 1fr;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">✈️ VerifiedOnward</div>
                <div class="title">Your Embassy-Approved Flight Reservation is Ready!</div>
                <div class="booking-ref">Booking Reference: ${bookingReference}</div>
            </div>

            <p>Hi ${passenger.firstName || 'Customer'},</p>

            <p>Attached is your embassy-compliant flight reservation. You can also download it directly from the confirmation page.</p>

            <p>Thank you for using VerifiedOnward!</p>

            <p>— The VerifiedOnward Team</p>

            <div class="footer">
                <p>
                    <strong>VerifiedOnward.com</strong><br>
                    Embassy-Approved Flight Reservation in 60 Seconds<br>
                    This email was sent to: ${recipientEmail}<br>
                    Generated on: ${new Date().toLocaleString()}
                </p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  // Helper method to format date and time
  formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // Send test email (for debugging)
  async sendTestEmail(to) {
    try {
      const mailOptions = {
        from: process.env.EMAIL_FROM,
        to: to,
        subject: 'VerifiedOnward - Email Service Test',
        html: `
          <h2>Email Service Test</h2>
          <p>This is a test email from VerifiedOnward.com</p>
          <p>If you received this email, the email service is working correctly!</p>
          <p>Sent at: ${new Date().toLocaleString()}</p>
        `
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Test email sent successfully:', result.messageId);
      return result;

    } catch (error) {
      console.error('Test email sending error:', error);
      throw new Error('Failed to send test email');
    }
  }
}

module.exports = new EmailService();
