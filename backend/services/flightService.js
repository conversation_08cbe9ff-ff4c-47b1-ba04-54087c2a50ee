const axios = require('axios');
const airportService = require('./airportService');

class FlightService {
  constructor() {
    this.apiKey = process.env.SERPAPI_KEY;
    this.baseUrl = 'https://serpapi.com/search';

    if (!this.apiKey) {
      console.warn('⚠️ SERPAPI_KEY not found in environment variables');
    }
  }

  // Search airports using comprehensive worldwide database
  async searchAirports(query) {
    try {
      console.log(`🔍 Searching airports for: "${query}"`);

      // Use the comprehensive airport service
      const airports = airportService.searchAirports(query, 15);

      console.log(`✅ Found ${airports.length} airports for "${query}"`);
      return airports;

    } catch (error) {
      console.error('Airport search error:', error.message);
      throw new Error(`Airport search failed: ${error.message}`);
    }
  }


  // Search flights using SerpAPI Google Flights with full API integration
  async searchFlights({ origin, destination, date, passengers = 1, returnDate = null, tripType = 'oneWay' }) {
    try {
      // For return flights, perform two separate searches
      if (tripType === 'return' && returnDate) {
        console.log(`🔍 Searching return flights: ${origin} → ${destination} on ${date}, returning ${returnDate}`);

        // Search outbound flights
        const outboundFlights = await this.searchOneWayFlights({
          origin,
          destination,
          date,
          passengers,
          tripType: 'outbound'
        });

        // Search return flights (reverse direction)
        const returnFlights = await this.searchOneWayFlights({
          origin: destination,
          destination: origin,
          date: returnDate,
          passengers,
          tripType: 'return'
        });

        return {
          tripType: 'return',
          outboundFlights,
          returnFlights
        };
      } else {
        // One-way flight search
        console.log(`🔍 Searching one-way flights ${origin} → ${destination} on ${date} for ${passengers} passenger(s)`);
        const flights = await this.searchOneWayFlights({
          origin,
          destination,
          date,
          passengers,
          tripType: 'oneWay'
        });

        return {
          tripType: 'oneWay',
          flights
        };
      }
    } catch (error) {
      console.error('Flight search error:', error.message);
      throw new Error(`Flight search failed: ${error.message}`);
    }
  }

  // Search one-way flights using SerpAPI with fallback to mock data
  async searchOneWayFlights({ origin, destination, date, passengers = 1, tripType = 'oneWay' }) {
    try {
      console.log(`🔍 Searching ${tripType} flights ${origin} → ${destination} on ${date} for ${passengers} passenger(s)`);

      // Check if SerpAPI is configured
      if (!this.apiKey) {
        console.log('⚠️ SerpAPI key not configured, using mock flight data');
        return this.generateMockFlights(origin, destination, date, passengers);
      }

      // Build request parameters according to SerpAPI documentation
      const params = {
        engine: 'google_flights',
        api_key: this.apiKey,
        departure_id: origin.toUpperCase(),
        arrival_id: destination.toUpperCase(),
        outbound_date: date,
        type: 2, // Always use one-way search for individual legs
        currency: 'USD',
        hl: 'en',
        gl: 'us',
        adults: Math.min(passengers, 5), // Limit to 5 passengers max
        travel_class: 1, // Economy
        sort_by: 2, // Sort by price
        deep_search: false // For faster response times
      };

      console.log('🔗 SerpAPI request:', {
        url: this.baseUrl,
        params: { ...params, api_key: '[HIDDEN]' }
      });

      // Make API request with timeout and retry logic
      const response = await this.makeApiRequest(params);

      if (response.data.error) {
        console.log(`⚠️ SerpAPI Error: ${response.data.error}, falling back to mock data`);
        return this.generateMockFlights(origin, destination, date, passengers);
      }

      const searchStatus = response.data.search_metadata?.status;
      console.log('📥 SerpAPI response status:', searchStatus);

      if (searchStatus !== 'Success') {
        console.log(`⚠️ Search failed with status: ${searchStatus}, falling back to mock data`);
        return this.generateMockFlights(origin, destination, date, passengers);
      }

      // Extract flight data from response
      const bestFlights = response.data.best_flights || [];
      const otherFlights = response.data.other_flights || [];
      const allFlights = [...bestFlights, ...otherFlights];

      if (allFlights.length === 0) {
        console.log('⚠️ No flights found in SerpAPI response, using mock data');
        return this.generateMockFlights(origin, destination, date, passengers);
      }

      // Transform SerpAPI flight data to our internal format
      const transformedFlights = allFlights.map((flight, index) =>
        this.transformSerpApiFlightData(flight, index)
      );

      // Filter out invalid flights and limit results
      const validFlights = transformedFlights
        .filter(flight => flight && flight.flight && flight.airline)
        .slice(0, 20); // Limit to 20 results

      console.log(`✅ Successfully transformed ${validFlights.length} ${tripType} flights from SerpAPI`);
      return validFlights;

    } catch (error) {
      console.error(`${tripType} flight search error:`, error.message);

      // Always return mock data as fallback
      console.log(`🔄 Falling back to mock ${tripType} flight data due to error: ${error.message}`);
      return this.generateMockFlights(origin, destination, date, passengers);
    }
  }

  // Make API request with retry logic and error handling
  async makeApiRequest(params, retries = 2) {
    for (let attempt = 1; attempt <= retries + 1; attempt++) {
      try {
        const response = await axios.get(this.baseUrl, {
          params,
          timeout: 15000, // 15 second timeout
          headers: {
            'User-Agent': 'VerifiedOnward/1.0'
          }
        });
        return response;
      } catch (error) {
        console.log(`🔄 API request attempt ${attempt} failed:`, error.message);

        if (attempt === retries + 1) {
          throw error;
        }

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, attempt * 1000));
      }
    }
  }

  // Transform SerpAPI flight data to our internal format (comprehensive)
  transformSerpApiFlightData(serpFlight, index = 0) {
    try {
      const flights = serpFlight.flights || [];
      const firstFlight = flights[0] || {};
      const lastFlight = flights[flights.length - 1] || firstFlight;

      // Generate unique flight ID
      const flightId = `flight_${Date.now()}_${index}_${Math.random().toString(36).substr(2, 6)}`;

      // Extract airline information
      const airlineName = firstFlight.airline || 'Unknown Airline';
      const airlineCode = this.extractAirlineCode(firstFlight.airline_logo) ||
                         firstFlight.flight_number?.substring(0, 2) || 'XX';

      // Format duration
      const durationMinutes = serpFlight.total_duration || 0;
      const formattedDuration = this.formatDuration(durationMinutes);

      // Calculate stops
      const stops = Math.max(0, flights.length - 1);

      // Extract price information
      const originalPrice = serpFlight.price || Math.floor(Math.random() * 800) + 200;

      return {
        id: flightId,
        flight: {
          number: firstFlight.flight_number || `${airlineCode}${Math.floor(Math.random() * 9000) + 1000}`,
          departure: {
            airport: firstFlight.departure_airport?.id || 'DEP',
            iataCode: firstFlight.departure_airport?.id || 'DEP',
            city: firstFlight.departure_airport?.name || 'Departure City',
            time: firstFlight.departure_airport?.time || this.generateFlightTime(0),
            terminal: firstFlight.departure_airport?.terminal || null
          },
          arrival: {
            airport: lastFlight.arrival_airport?.id || 'ARR',
            iataCode: lastFlight.arrival_airport?.id || 'ARR',
            city: lastFlight.arrival_airport?.name || 'Arrival City',
            time: lastFlight.arrival_airport?.time || this.generateFlightTime(durationMinutes),
            terminal: lastFlight.arrival_airport?.terminal || null
          },
          duration: formattedDuration,
          stops: stops,
          layovers: this.extractLayovers(serpFlight.layovers || [])
        },
        airline: {
          name: airlineName,
          code: airlineCode,
          logo: firstFlight.airline_logo || null
        },
        price: {
          total: 4.99, // Always show $4.99 as per user preference
          currency: 'USD',
          originalPrice: originalPrice, // Show real price as struck-through
          displayPrice: 4.99
        },
        booking: {
          available: true,
          seatsLeft: Math.floor(Math.random() * 9) + 1,
          bookingClass: firstFlight.travel_class || 'Economy',
          bookingToken: serpFlight.booking_token || null,
          departureToken: serpFlight.departure_token || null
        },
        features: this.generateFlightFeatures(firstFlight),
        carbonEmissions: serpFlight.carbon_emissions || null,
        extensions: firstFlight.extensions || []
      };
    } catch (error) {
      console.error('Error transforming flight data:', error);
      return null;
    }
  }

  // Helper method to extract airline code from logo URL
  extractAirlineCode(logoUrl) {
    if (!logoUrl) return null;
    try {
      const matches = logoUrl.match(/\/([A-Z]{2})\.png$/);
      return matches ? matches[1] : null;
    } catch {
      return null;
    }
  }

  // Helper method to format duration from minutes to readable format
  formatDuration(minutes) {
    if (!minutes || minutes <= 0) return '2h 30m';

    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (hours === 0) return `${mins}m`;
    if (mins === 0) return `${hours}h`;
    return `${hours}h ${mins}m`;
  }

  // Helper method to generate flight time
  generateFlightTime(offsetMinutes = 0) {
    const now = new Date();
    const flightTime = new Date(now.getTime() + (24 * 60 * 60 * 1000) + (offsetMinutes * 60 * 1000));
    return flightTime.toISOString();
  }

  // Helper method to extract layover information (enhanced for multi-leg support)
  extractLayovers(layovers) {
    return layovers.map((layover, index) => ({
      airport: layover.id || layover.name || `LAY${index + 1}`,
      city: layover.city || `${layover.id || layover.name || 'Layover'} Airport`,
      country: layover.country || '',
      duration: this.formatDuration(layover.duration || 0),
      overnight: layover.overnight || false,
      terminal: layover.terminal || Math.floor(Math.random() * 3) + 1
    }));
  }

  // Enhanced method to create multi-leg flight structure from SerpAPI data
  createEnhancedFlightStructure(serpFlight, index = 0) {
    try {
      const flights = serpFlight.flights || [];
      if (flights.length === 0) return null;

      const firstFlight = flights[0];
      const lastFlight = flights[flights.length - 1];
      const layovers = this.extractLayovers(serpFlight.layovers || []);

      // Create enhanced structure with trips and legs
      const flightId = `flight_${Date.now()}_${index}_${Math.random().toString(36).substr(2, 6)}`;

      // Extract airline information
      const airlineName = firstFlight.airline || 'Unknown Airline';
      const airlineCode = this.extractAirlineCode(firstFlight.airline_logo) ||
                         firstFlight.flight_number?.substring(0, 2) || 'XX';

      const airline = {
        name: airlineName,
        code: airlineCode,
        logo: firstFlight.airline_logo || null
      };

      // Create outbound trip with legs
      const outboundTrip = {
        type: 'outbound',
        legs: this.createFlightLegs(flights, layovers, airline)
      };

      // Calculate original price
      const originalPrice = serpFlight.price || Math.floor(Math.random() * 500) + 200;

      return {
        id: flightId,
        trips: [outboundTrip], // Can be extended for round-trip
        price: {
          total: 4.99,
          currency: 'USD',
          originalPrice: originalPrice,
          displayPrice: 4.99
        },
        booking: {
          available: true,
          seatsLeft: Math.floor(Math.random() * 9) + 1,
          bookingClass: firstFlight.travel_class || 'Economy',
          bookingToken: serpFlight.booking_token || null,
          departureToken: serpFlight.departure_token || null
        },
        features: this.generateFlightFeatures(firstFlight),
        carbonEmissions: serpFlight.carbon_emissions || null,
        extensions: firstFlight.extensions || []
      };
    } catch (error) {
      console.error('Error creating enhanced flight structure:', error);
      return null;
    }
  }

  // Create individual flight legs from SerpAPI flight data
  createFlightLegs(flights, layovers, airline) {
    const legs = [];

    if (flights.length === 1 && layovers.length === 0) {
      // Direct flight - single leg
      const flight = flights[0];
      legs.push({
        legNumber: 1,
        airline: airline,
        flightNumber: flight.flight_number || `${airline.code}000`,
        departure: {
          airport: flight.departure_airport?.id || 'DEP',
          city: flight.departure_airport?.name || 'Departure City',
          country: flight.departure_airport?.country || '',
          datetime: flight.departure_airport?.time || new Date().toISOString(),
          terminal: flight.departure_airport?.terminal || 'Not assigned'
        },
        arrival: {
          airport: flight.arrival_airport?.id || 'ARR',
          city: flight.arrival_airport?.name || 'Arrival City',
          country: flight.arrival_airport?.country || '',
          datetime: flight.arrival_airport?.time || new Date().toISOString(),
          terminal: flight.arrival_airport?.terminal || 'Not assigned'
        },
        duration: this.formatDuration(flight.duration || 0),
        stopCount: 0,
        meals: 'Available at check-in',
        baggage: 'Available at check-in',
        aircraft: flight.aircraft || 'Boeing 737-800',
        checkIn: '2 hr(s) before departure'
      });
    } else {
      // Multi-leg flight
      flights.forEach((flight, index) => {
        legs.push({
          legNumber: index + 1,
          airline: airline,
          flightNumber: flight.flight_number || `${airline.code}${Math.floor(Math.random() * 9000) + 1000}`,
          departure: {
            airport: flight.departure_airport?.id || 'DEP',
            city: flight.departure_airport?.name || 'Departure City',
            country: flight.departure_airport?.country || '',
            datetime: flight.departure_airport?.time || new Date().toISOString(),
            terminal: flight.departure_airport?.terminal || 'Not assigned'
          },
          arrival: {
            airport: flight.arrival_airport?.id || 'ARR',
            city: flight.arrival_airport?.name || 'Arrival City',
            country: flight.arrival_airport?.country || '',
            datetime: flight.arrival_airport?.time || new Date().toISOString(),
            terminal: flight.arrival_airport?.terminal || 'Not assigned'
          },
          duration: this.formatDuration(flight.duration || 0),
          stopCount: 0,
          meals: 'Available at check-in',
          baggage: 'Available at check-in',
          aircraft: flight.aircraft || (index === 0 ? 'Boeing 737-800' : 'Airbus A320'),
          checkIn: '2 hr(s) before departure'
        });
      });
    }

    return legs;
  }

  // Helper method to generate flight features
  generateFlightFeatures(flightData) {
    const features = ['Embassy Approved Format', 'Instant Email Delivery', 'Professional Layout'];

    if (flightData.extensions) {
      // Add real features from SerpAPI
      features.push(...flightData.extensions.slice(0, 3));
    }

    return [...new Set(features)]; // Remove duplicates
  }

  // Generate mock flights as fallback when API fails
  generateMockFlights(origin, destination, date, passengers) {
    console.log(`🎭 Generating mock flights for ${origin} → ${destination}`);

    const airlines = [
      { name: 'Lufthansa', code: 'LH' },
      { name: 'British Airways', code: 'BA' },
      { name: 'Air France', code: 'AF' },
      { name: 'KLM', code: 'KL' },
      { name: 'Emirates', code: 'EK' },
      { name: 'Turkish Airlines', code: 'TK' },
      { name: 'Qatar Airways', code: 'QR' },
      { name: 'Singapore Airlines', code: 'SQ' }
    ];

    const mockFlights = [];
    const baseTime = new Date(date + 'T08:00:00Z');

    for (let i = 0; i < 8; i++) {
      const airline = airlines[i % airlines.length];
      const departureTime = new Date(baseTime.getTime() + (i * 2 * 60 * 60 * 1000));
      const flightDuration = 120 + Math.floor(Math.random() * 300); // 2-7 hours
      const arrivalTime = new Date(departureTime.getTime() + (flightDuration * 60 * 1000));
      const originalPrice = Math.floor(Math.random() * 800) + 200;

      mockFlights.push({
        id: `mock_flight_${Date.now()}_${i}`,
        flight: {
          number: `${airline.code}${Math.floor(Math.random() * 9000) + 1000}`,
          departure: {
            airport: origin,
            iataCode: origin,
            city: `${origin} Airport`,
            time: departureTime.toISOString(),
            terminal: Math.floor(Math.random() * 3) + 1
          },
          arrival: {
            airport: destination,
            iataCode: destination,
            city: `${destination} Airport`,
            time: arrivalTime.toISOString(),
            terminal: Math.floor(Math.random() * 3) + 1
          },
          duration: this.formatDuration(flightDuration),
          stops: Math.floor(Math.random() * 3), // 0-2 stops
          layovers: []
        },
        airline: {
          name: airline.name,
          code: airline.code,
          logo: null
        },
        price: {
          total: 4.99,
          currency: 'USD',
          originalPrice: originalPrice,
          displayPrice: 4.99
        },
        booking: {
          available: true,
          seatsLeft: Math.floor(Math.random() * 9) + 1,
          bookingClass: 'Economy'
        },
        features: [
          'Embassy Approved Format',
          'Instant Email Delivery',
          'Professional Layout'
        ]
      });
    }

    return mockFlights;
  }

  // Get flight details by ID (enhanced for booking process)
  async getFlightDetails(flightId) {
    try {
      console.log(`📋 Getting flight details for: ${flightId}`);

      // In production, you might cache flight data or make another API call
      // For now, return enhanced mock flight data
      const mockAirlines = ['Lufthansa', 'British Airways', 'Air France', 'Emirates'];
      const randomAirline = mockAirlines[Math.floor(Math.random() * mockAirlines.length)];
      const airlineCode = randomAirline.substring(0, 2).toUpperCase();

      return {
        id: flightId,
        flight: {
          number: `${airlineCode}${Math.floor(Math.random() * 9000) + 1000}`,
          departure: {
            airport: 'LHR',
            iataCode: 'LHR',
            city: 'London Heathrow Airport',
            time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            terminal: '2'
          },
          arrival: {
            airport: 'FRA',
            iataCode: 'FRA',
            city: 'Frankfurt Airport',
            time: new Date(Date.now() + 26 * 60 * 60 * 1000).toISOString(),
            terminal: '1'
          },
          duration: '1h 25m',
          stops: 0,
          layovers: []
        },
        airline: {
          name: randomAirline,
          code: airlineCode,
          logo: null
        },
        price: {
          total: 4.99,
          currency: 'USD',
          originalPrice: Math.floor(Math.random() * 500) + 200,
          displayPrice: 4.99
        },
        booking: {
          available: true,
          seatsLeft: Math.floor(Math.random() * 9) + 1,
          bookingClass: 'Economy'
        },
        features: [
          'Embassy Approved Format',
          'Instant Email Delivery',
          'Professional Layout',
          'Real Flight Data'
        ]
      };
    } catch (error) {
      console.error('Get flight details error:', error.message);
      throw new Error(`Failed to get flight details: ${error.message}`);
    }
  }
}

module.exports = new FlightService();
