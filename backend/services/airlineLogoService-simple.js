class SimpleAirlineLogoService {
  constructor() {
    this.logoCache = new Map();
  }

  // Comprehensive airline logo database with multiple sources
  getLogoSources(airlineCode) {
    const logoDatabase = {
      'PC': {
        name: 'Pegasus Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/PC.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/PC.png?crop=false&width=108&height=92'
        ],
        colors: { bg: '#E30613', text: 'white' }
      },
      'TK': {
        name: 'Turkish Airlines',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/TK.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/TK.png?crop=false&width=108&height=92'
        ],
        colors: { bg: '#E30613', text: 'white' }
      },
      'LH': {
        name: '<PERSON>fthansa',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/LH.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/LH.png?crop=false&width=108&height=92'
        ],
        colors: { bg: '#FFD800', text: '#004080' }
      },
      'BA': {
        name: 'British Airways',
        sources: [
          'https://www.gstatic.com/flights/airline_logos/70px/BA.png',
          'https://content.r9cdn.net/rimg/provider-logos/airlines/v/BA.png?crop=false&width=108&height=92'
        ],
        colors: { bg: '#004775', text: 'white' }
      }
    };

    return logoDatabase[airlineCode] || null;
  }

  // Get the best available logo for an airline
  async getAirlineLogo(airlineCode, airlineName) {
    if (!airlineCode) {
      return this.generateSVGLogo('XX', airlineName || 'Airline');
    }

    // Check cache first
    const cacheKey = `${airlineCode}_${airlineName}`;
    if (this.logoCache.has(cacheKey)) {
      return this.logoCache.get(cacheKey);
    }

    const logoData = this.getLogoSources(airlineCode);
    
    if (logoData && logoData.sources.length > 0) {
      // Use the first source (Google Static is most reliable for PDF generation)
      const logoUrl = logoData.sources[0];
      this.logoCache.set(cacheKey, logoUrl);
      return logoUrl;
    }

    // Fallback to generated SVG logo
    const fallbackLogo = this.generateSVGLogo(airlineCode, airlineName, logoData?.colors);
    this.logoCache.set(cacheKey, fallbackLogo);
    return fallbackLogo;
  }

  // Generate a branded SVG logo
  generateSVGLogo(airlineCode, airlineName, colors = null) {
    const defaultColors = { bg: '#2563EB', text: 'white' };
    const logoColors = colors || defaultColors;
    const displayCode = airlineCode || 'XX';

    const svgContent = `
      <svg width="70" height="45" viewBox="0 0 70 45" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="70" height="45" fill="${logoColors.bg}" rx="4"/>
        <text x="35" y="28" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="${logoColors.text}" text-anchor="middle">${displayCode}</text>
      </svg>
    `;

    return `data:image/svg+xml;base64,${Buffer.from(svgContent.trim()).toString('base64')}`;
  }

  // Clear cache (for testing/debugging)
  clearCache() {
    this.logoCache.clear();
  }
}

module.exports = new SimpleAirlineLogoService();
