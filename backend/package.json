{"name": "verifiedonward-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "Verified<PERSON><PERSON><PERSON> - Embassy-Approved Flight Reservation in 60 Seconds", "dependencies": {"@paypal/paypal-server-sdk": "^1.1.0", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^4.21.2", "node-fetch": "^2.7.0", "nodemailer": "^7.0.4", "puppeteer": "^24.14.0", "qrcode": "^1.5.4", "stripe": "^18.3.0"}, "devDependencies": {"nodemon": "^3.1.10"}}