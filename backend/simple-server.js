const express = require('express');
const path = require('path');

const app = express();
const PORT = 5003;

// Simple test route
app.get('/test', (req, res) => {
  res.json({ message: 'Simple server is working!' });
});

// Serve static files from frontend
app.use(express.static(path.join(__dirname, '../frontend/public')));

// Serve index.html for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../frontend/index.html'));
});

app.listen(PORT, () => {
  console.log(`🚀 Simple server running on port ${PORT}`);
  console.log(`🌐 Visit: http://localhost:${PORT}`);
});
