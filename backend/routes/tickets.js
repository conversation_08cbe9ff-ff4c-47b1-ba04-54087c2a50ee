const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const pdfService = require('../services/pdfService');
const emailService = require('../services/emailService');
const { generateReservationCodes } = require('../utils/reservationCodeGenerator');

// Store for booking data (in production, use a database)
const bookingStore = new Map();

// Aircraft assignment based on airline
function getAircraftForAirline(airlineName, isReturn = false) {
  const aircraftByAirline = {
    'RYANAIR': isReturn ? 'BOEING 737-800' : 'BOEING 737-800',
    'EASYJET': isReturn ? 'AIRBUS A320' : 'AIRBUS A319',
    'BRITISH AIRWAYS': isReturn ? 'AIRBUS A320' : 'BOEING 777-200',
    'LUFTHANSA': isReturn ? 'AIRBUS A321' : 'BOEING 737-800',
    'AIR FRANCE': isReturn ? 'AIRBUS A320' : 'AIRBUS A321',
    'KLM': isReturn ? 'BOEING 737-800' : 'AIRBUS A330-200',
    'EMIRATES': isReturn ? 'BOEING 777-300ER' : 'AIRBUS A380-800',
    'QATAR AIRWAYS': isReturn ? 'AIRBUS A321' : 'BOEING 787-8',
    'TURKISH AIRLINES': isReturn ? 'AIRBUS A320' : 'BOEING 737-800',
    'IBERIA': isReturn ? 'AIRBUS A320' : 'AIRBUS A321',
    'VUELING': isReturn ? 'AIRBUS A320' : 'AIRBUS A319',
    'WIZZ AIR': isReturn ? 'AIRBUS A321' : 'AIRBUS A320'
  };

  const normalizedAirline = airlineName?.toUpperCase() || '';
  return aircraftByAirline[normalizedAirline] || (isReturn ? 'AIRBUS A320' : 'BOEING 737-800');
}

// Helper function to convert booking data to ticket format
function convertBookingToTicketFormat(bookingData) {
  const { bookingReference, passengers, outboundFlight, returnFlight } = bookingData;

  // Format trip dates
  const outboundDate = new Date(outboundFlight.departure.time).toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  }).toUpperCase().replace(/,/g, '');

  const returnDate = returnFlight ?
    new Date(returnFlight.departure.time).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    }).toUpperCase().replace(/,/g, '') : null;

  const tripDates = returnDate ? `${outboundDate} › ${returnDate}` : outboundDate;

  // Format destination
  const destination = `TRIP TO ${outboundFlight.arrival.city.toUpperCase()}`;

  // Format passengers
  const formattedPassengers = passengers.map(p => ({
    name: `${(p.lastName || 'PASSENGER').toUpperCase()}/${(p.firstName || 'NAME').toUpperCase()}`
  }));

  // Format segments
  const segments = [];

  // Outbound segment
  segments.push({
    departureDay: new Date(outboundFlight.departure.time).toLocaleDateString('en-US', {
      weekday: 'long',
      day: '2-digit',
      month: 'short'
    }).toUpperCase(),
    airline: outboundFlight.airline || 'AIRLINE',
    flightNo: outboundFlight.flightNumber || 'FL 000',
    duration: outboundFlight.duration || '00hr(s) 00min(s)',
    flightClass: 'Economy Class (M)',
    status: 'Confirmed',
    from: {
      code: outboundFlight.departure.code,
      city: outboundFlight.departure.city,
      time: new Date(outboundFlight.departure.time).toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      }),
      terminal: outboundFlight.departure.terminal || '1'
    },
    to: {
      code: outboundFlight.arrival.code,
      city: outboundFlight.arrival.city,
      time: new Date(outboundFlight.arrival.time).toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      }),
      terminal: outboundFlight.arrival.terminal || '1'
    },
    aircraft: getAircraftForAirline(outboundFlight.airline, false),
    stops: outboundFlight.stops || 0,
    layovers: outboundFlight.layovers || [],
    meals: 'Not Available',
    distance: 'Not Available'
  });

  // Return segment if exists
  if (returnFlight) {
    segments.push({
      departureDay: new Date(returnFlight.departure.time).toLocaleDateString('en-US', {
        weekday: 'long',
        day: '2-digit',
        month: 'short'
      }).toUpperCase(),
      airline: returnFlight.airline || 'AIRLINE',
      flightNo: returnFlight.flightNumber || 'FL 000',
      duration: returnFlight.duration || '00hr(s) 00min(s)',
      flightClass: 'Economy Class (M)',
      status: 'Confirmed',
      from: {
        code: returnFlight.departure.code,
        city: returnFlight.departure.city,
        time: new Date(returnFlight.departure.time).toLocaleTimeString('en-GB', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }),
        terminal: returnFlight.departure.terminal || '1'
      },
      to: {
        code: returnFlight.arrival.code,
        city: returnFlight.arrival.city,
        time: new Date(returnFlight.arrival.time).toLocaleTimeString('en-GB', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }),
        terminal: returnFlight.arrival.terminal || '1'
      },
      aircraft: getAircraftForAirline(returnFlight.airline, true),
      stops: returnFlight.stops || 0,
      layovers: returnFlight.layovers || [],
      meals: 'Not Available',
      distance: 'Not Available'
    });
  }

  // Generate proper reservation codes
  const airlineCode = outboundFlight.airline?.code || outboundFlight.airline?.substring(0, 2) || 'VO';
  const codes = generateReservationCodes(airlineCode);

  return {
    tripDates,
    destination,
    passengers: formattedPassengers,
    reservationCode: codes.reservationCode,
    airlineReservationCode: codes.airlineReservationCode,
    segments,
    showNotice: false
  };
}

// Generate PDF ticket
router.post('/generate', async (req, res) => {
  try {
    console.log('📥 PDF generation request received');
    console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

    const {
      bookingReference,
      passengers,
      outboundFlight,
      returnFlight,
      totalPrice
    } = req.body;

    // Validate required fields
    if (!bookingReference || !passengers || !outboundFlight) {
      return res.status(400).json({
        success: false,
        error: 'Missing required booking data'
      });
    }

    // Store booking data
    const bookingData = {
      bookingReference,
      passengers,
      outboundFlight,
      returnFlight,
      totalPrice,
      createdAt: new Date().toISOString()
    };

    bookingStore.set(bookingReference, bookingData);
    console.log(`💾 Stored booking data for: ${bookingReference}`);

    // Convert booking data to ticket format
    const ticketData = convertBookingToTicketFormat(bookingData);

    // Generate PDF
    const pdfDir = path.join(__dirname, '../temp');
    if (!fs.existsSync(pdfDir)) {
      fs.mkdirSync(pdfDir, { recursive: true });
    }

    const pdfPath = path.join(pdfDir, `ticket-${bookingReference}.pdf`);
    await pdfService.generatePDF(ticketData, pdfPath);

    console.log(`✅ PDF generated successfully: ${pdfPath}`);

    // Update booking data with PDF path
    bookingData.pdfPath = pdfPath;
    bookingData.reservationCode = bookingReference;
    bookingStore.set(bookingReference, bookingData);

    res.json({
      success: true,
      message: 'Flight reservation generated successfully',
      bookingReference,
      reservationCode: bookingReference,
      downloadUrl: `/api/tickets/download/${bookingReference}`
    });

  } catch (error) {
    console.error('❌ PDF generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate flight reservation',
      details: error.message
    });
  }
});

// Download PDF ticket
router.get('/download/:bookingReference', async (req, res) => {
  try {
    const { bookingReference } = req.params;
    console.log(`📥 Download request for booking reference: ${bookingReference}`);

    // Get booking data
    const bookingData = bookingStore.get(bookingReference);
    if (!bookingData) {
      return res.status(404).json({
        success: false,
        error: 'Booking not found'
      });
    }

    // Check if PDF exists
    if (!bookingData.pdfPath || !fs.existsSync(bookingData.pdfPath)) {
      return res.status(404).json({
        success: false,
        error: 'PDF file not found'
      });
    }

    // Set headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="flight-reservation-${bookingReference}.pdf"`);

    // Stream the PDF file
    const fileStream = fs.createReadStream(bookingData.pdfPath);
    fileStream.pipe(res);

    console.log(`✅ PDF download started for: ${bookingReference}`);

  } catch (error) {
    console.error('❌ Download error:', error);
    res.status(500).json({
      success: false,
      error: 'Download failed',
      details: error.message
    });
  }
});

// Generate PDF from ticket data (direct endpoint)
router.post('/generate-pdf', async (req, res) => {
  try {
    console.log('📥 Direct PDF generation request received');

    const ticketData = req.body;

    // Validate required fields
    if (!ticketData.reservationCode || !ticketData.passengers || !ticketData.segments) {
      return res.status(400).json({
        success: false,
        error: 'Missing required ticket data'
      });
    }

    // Generate PDF
    const pdfDir = path.join(__dirname, '../temp');
    if (!fs.existsSync(pdfDir)) {
      fs.mkdirSync(pdfDir, { recursive: true });
    }

    const pdfPath = path.join(pdfDir, `ticket-${ticketData.reservationCode}-${Date.now()}.pdf`);
    const pdfBuffer = await pdfService.generatePDF(ticketData, pdfPath);

    console.log(`✅ Direct PDF generated successfully: ${pdfPath}`);

    // Return PDF as response
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="flight-reservation-${ticketData.reservationCode}.pdf"`);
    res.send(pdfBuffer);

  } catch (error) {
    console.error('❌ Direct PDF generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate PDF',
      details: error.message
    });
  }
});

// Verify reservation (for QR code scanning)
router.get('/verify/:reservationCode', async (req, res) => {
  try {
    const { reservationCode } = req.params;
    console.log(`🔍 Verification request for: ${reservationCode}`);

    // Get booking data
    const bookingData = bookingStore.get(reservationCode);
    if (!bookingData) {
      return res.status(404).json({
        success: false,
        error: 'Reservation not found'
      });
    }

    // Return verification info (without sensitive data)
    res.json({
      success: true,
      verified: true,
      reservationCode,
      createdAt: bookingData.createdAt,
      passengers: bookingData.passengers?.length || 0,
      destination: bookingData.outboundFlight?.arrival?.city || 'Unknown',
      message: 'Reservation verified successfully'
    });

  } catch (error) {
    console.error('❌ Verification error:', error);
    res.status(500).json({
      success: false,
      error: 'Verification failed'
    });
  }
});

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'VerifiedOnward PDF Ticket service is running',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
