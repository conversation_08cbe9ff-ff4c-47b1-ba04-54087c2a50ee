const express = require('express');
const router = express.Router();
const stripeService = require('../services/stripeService');
const paypalService = require('../services/paypalService');

// Check if we're in demo mode (no real API keys configured)
const isDemoMode = !process.env.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY === 'sk_test_your_stripe_secret_key';

// Create Stripe payment intent
router.post('/stripe/create-intent', async (req, res) => {
  try {
    const { amount, currency = 'usd', metadata = {} } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        error: 'Invalid amount',
        message: 'Amount must be greater than 0'
      });
    }

    // Demo mode - return mock client secret
    if (isDemoMode) {
      const mockPaymentIntentId = `pi_demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const mockClientSecret = `${mockPaymentIntentId}_secret_demo`;

      console.log('🎭 Demo mode: Creating mock Stripe payment intent');

      res.json({
        success: true,
        clientSecret: mockClientSecret,
        paymentIntentId: mockPaymentIntentId
      });
      return;
    }

    const paymentIntent = await stripeService.createPaymentIntent({
      amount: Math.round(amount * 100), // Convert to cents
      currency,
      metadata
    });

    res.json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    });

  } catch (error) {
    console.error('Stripe payment intent error:', error);
    res.status(500).json({
      error: 'Payment intent creation failed',
      message: error.message
    });
  }
});

// Confirm Stripe payment
router.post('/stripe/confirm', async (req, res) => {
  try {
    const { paymentIntentId } = req.body;

    if (!paymentIntentId) {
      return res.status(400).json({
        error: 'Missing payment intent ID'
      });
    }

    // Demo mode - simulate successful payment
    if (isDemoMode) {
      console.log('🎭 Demo mode: Confirming mock Stripe payment');

      res.json({
        success: true,
        status: 'succeeded',
        paymentId: paymentIntentId
      });
      return;
    }

    const paymentIntent = await stripeService.retrievePaymentIntent(paymentIntentId);

    if (paymentIntent.status === 'succeeded') {
      // Payment successful - trigger ticket generation
      res.json({
        success: true,
        status: 'succeeded',
        paymentId: paymentIntent.id
      });
    } else {
      res.status(400).json({
        error: 'Payment not completed',
        status: paymentIntent.status
      });
    }

  } catch (error) {
    console.error('Stripe payment confirmation error:', error);
    res.status(500).json({
      error: 'Payment confirmation failed',
      message: error.message
    });
  }
});

// Create PayPal order
router.post('/paypal/create-order', async (req, res) => {
  try {
    const { amount, currency = 'USD' } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        error: 'Invalid amount',
        message: 'Amount must be greater than 0'
      });
    }

    // Check if PayPal is in demo mode
    const isPayPalDemoMode = !process.env.PAYPAL_CLIENT_ID || process.env.PAYPAL_CLIENT_ID === 'your_paypal_client_id';

    // Demo mode - return mock order ID
    if (isPayPalDemoMode) {
      const mockOrderId = `paypal_demo_order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      console.log('🎭 Demo mode: Creating mock PayPal order');

      res.json({
        success: true,
        orderId: mockOrderId,
        approvalUrl: `https://www.sandbox.paypal.com/checkoutnow?token=${mockOrderId}`
      });
      return;
    }

    const order = await paypalService.createOrder({
      amount: amount.toFixed(2),
      currency
    });

    res.json({
      success: true,
      orderId: order.id,
      approvalUrl: order.links.find(link => link.rel === 'approve')?.href
    });

  } catch (error) {
    console.error('PayPal order creation error:', error);
    res.status(500).json({
      error: 'PayPal order creation failed',
      message: error.message
    });
  }
});

// Capture PayPal payment
router.post('/paypal/capture/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;

    // Check if PayPal is in demo mode
    const isPayPalDemoMode = !process.env.PAYPAL_CLIENT_ID || process.env.PAYPAL_CLIENT_ID === 'your_paypal_client_id';

    // Demo mode - simulate successful capture
    if (isPayPalDemoMode) {
      console.log('🎭 Demo mode: Capturing mock PayPal payment');

      res.json({
        success: true,
        status: 'completed',
        paymentId: `paypal_capture_${orderId}_${Date.now()}`
      });
      return;
    }

    const capture = await paypalService.captureOrder(orderId);

    if (capture.status === 'COMPLETED') {
      res.json({
        success: true,
        status: 'completed',
        paymentId: capture.id
      });
    } else {
      res.status(400).json({
        error: 'Payment not completed',
        status: capture.status
      });
    }

  } catch (error) {
    console.error('PayPal capture error:', error);
    res.status(500).json({
      error: 'PayPal capture failed',
      message: error.message
    });
  }
});

module.exports = router;
