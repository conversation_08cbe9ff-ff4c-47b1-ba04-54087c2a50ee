#!/usr/bin/env node

/**
 * HTML Logo Debug Test
 * Generates HTML to test logo display before PDF conversion
 */

const airlineLogoService = require('./backend/services/airlineLogoService');
const pdfService = require('./backend/services/pdfService');
const fs = require('fs');

async function testHTMLLogos() {
  console.log('🌐 HTML LOGO DEBUG TEST');
  console.log('=' .repeat(50));

  // Create test ticket data
  const testTicketData = {
    reservationCode: 'HTMLTEST123',
    airlineReservationCode: 'AF123VW',
    tripDates: '21 JUL 2025 • 28 JUL 2025',
    destination: 'TRIP TO PARIS CHARLES DE GAULLE',
    passengers: [
      { name: 'JOHN/DOE' }
    ],
    segments: [
      {
        airline: 'AIR FRANCE',
        flightNo: 'AF 1669',
        from: { code: 'MAN', name: 'Manchester Airport' },
        to: { code: 'CDG', name: 'Paris Charles de Gaulle' },
        departureTime: '11:50',
        arrivalTime: '14:40',
        departureDay: 'MONDAY, JUL 21',
        duration: '2H 50M',
        flightClass: 'Economy Class (M)',
        stops: 0
      },
      {
        airline: 'ETHIOPIAN AIRLINES',
        flightNo: 'ET 900',
        from: { code: 'CDG', name: 'Paris Charles de Gaulle' },
        to: { code: 'MAN', name: 'Manchester Airport' },
        departureTime: '13:40',
        arrivalTime: '15:30',
        departureDay: 'MONDAY, JUL 28',
        duration: '2H 50M',
        flightClass: 'Economy Class (M)',
        stops: 0
      }
    ]
  };

  try {
    console.log('📄 Generating HTML content...');
    
    // Generate the HTML content
    const html = await pdfService.generateTicketHTML(testTicketData);
    
    // Save HTML file
    const htmlPath = './test-logo-debug-output.html';
    fs.writeFileSync(htmlPath, html);
    
    console.log(`✅ HTML generated successfully: ${htmlPath}`);
    console.log(`📊 File size: ${(html.length / 1024).toFixed(2)} KB`);
    
    // Extract and analyze logo data
    console.log('\n🔍 Analyzing logo data in HTML:');
    
    const logoMatches = html.match(/src="data:image\/png;base64,[^"]+"/g);
    if (logoMatches) {
      console.log(`📊 Found ${logoMatches.length} base64 PNG logos:`);
      
      logoMatches.forEach((match, index) => {
        const base64Data = match.match(/base64,([^"]+)/)[1];
        const buffer = Buffer.from(base64Data, 'base64');
        const isPNG = buffer.slice(0, 8).toString('hex') === '89504e470d0a1a0a';
        
        console.log(`  ${index + 1}. Size: ${buffer.length} bytes, Valid PNG: ${isPNG ? '✅' : '❌'}`);
      });
    } else {
      console.log('❌ No base64 PNG logos found in HTML');
    }
    
    // Check for any broken image references
    const imgMatches = html.match(/<img[^>]+src="[^"]*"[^>]*>/g);
    if (imgMatches) {
      console.log(`\n🖼️  Found ${imgMatches.length} image tags:`);
      imgMatches.forEach((match, index) => {
        const src = match.match(/src="([^"]*)"/)[1];
        const isBase64 = src.startsWith('data:image/');
        const isURL = src.startsWith('http');
        
        console.log(`  ${index + 1}. ${isBase64 ? 'Base64 Data' : isURL ? 'External URL' : 'Unknown'}: ${src.substring(0, 50)}...`);
      });
    }
    
    console.log(`\n🌐 Open in browser: file://${process.cwd()}/${htmlPath}`);
    
  } catch (error) {
    console.log(`❌ HTML generation failed: ${error.message}`);
    console.error(error);
  }

  console.log('\n🏁 HTML LOGO DEBUG TEST COMPLETE');
}

// Run the test
testHTMLLogos().catch(console.error);
