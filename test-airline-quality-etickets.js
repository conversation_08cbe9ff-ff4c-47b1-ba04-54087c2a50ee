/**
 * Test Script for Airline-Quality E-Tickets
 * Tests the new professional airline-style PDF generation system
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5001';

// Test data for different airlines to verify logo integration
const testCases = [
  {
    name: "Aegean Airlines (Reference Standard)",
    data: {
      tripDates: "05 JUL 2025",
      destination: "DUBROVNIK - ATHENS",
      passengers: [
        { name: "ELMISRATI/ELSADIG" }
      ],
      reservationCode: "PDF0NX",
      airlineReservationCode: "PDF0NX",
      segments: [
        {
          departureDay: "SATURDAY, 05 JUL 2025",
          airline: "AEGEAN AIRLINES",
          flightNo: "A3785",
          duration: "1hr(s) 25min(s)",
          flightClass: "Economy Class (Y)",
          status: "Confirmed",
          from: { code: "DBV", city: "Dubrovnik, Croatia", time: "10:05", terminal: "Not assigned" },
          to: { code: "ATH", city: "Athens, Greece", time: "12:30", terminal: "Not assigned" },
          aircraft: "Boeing 737-800",
          stops: "0",
          meals: "Available at check-in",
          distance: "Not Available"
        }
      ]
    }
  },
  {
    name: "Multi-Airline Test (Ryanair + Emirates)",
    data: {
      tripDates: "15 AUG 2025 › 22 AUG 2025",
      destination: "LONDON - DUBAI - LONDON",
      passengers: [
        { name: "SMITH/JOHN MR." },
        { name: "SMITH/JANE MRS." }
      ],
      reservationCode: "MULTI001",
      airlineReservationCode: "MULTI001",
      segments: [
        {
          departureDay: "FRIDAY, 15 AUG 2025",
          airline: "RYANAIR",
          flightNo: "FR 1234",
          duration: "2hr(s) 30min(s)",
          flightClass: "Economy Class (Y)",
          status: "Confirmed",
          from: { code: "STN", city: "London Stansted, United Kingdom", time: "06:30", terminal: "1" },
          to: { code: "DUB", city: "Dublin, Ireland", time: "08:00", terminal: "2" },
          aircraft: "Boeing 737-800",
          stops: "0",
          meals: "Available for purchase",
          distance: "290"
        },
        {
          departureDay: "FRIDAY, 15 AUG 2025",
          airline: "EMIRATES",
          flightNo: "EK 163",
          duration: "7hr(s) 15min(s)",
          flightClass: "Business Class (C)",
          status: "Confirmed",
          from: { code: "DUB", city: "Dublin, Ireland", time: "12:45", terminal: "2" },
          to: { code: "DXB", city: "Dubai, United Arab Emirates", time: "23:00", terminal: "3" },
          aircraft: "Airbus A380-800",
          stops: "0",
          meals: "Available",
          distance: "3,421"
        }
      ]
    }
  },
  {
    name: "European Airlines Test (Lufthansa + Air France + KLM)",
    data: {
      tripDates: "10 SEP 2025 › 17 SEP 2025",
      destination: "MULTI-CITY EUROPE TOUR",
      passengers: [
        { name: "TRAVELER/FREQUENT MR." }
      ],
      reservationCode: "EUR2025",
      airlineReservationCode: "EUR2025",
      segments: [
        {
          departureDay: "TUESDAY, 10 SEP 2025",
          airline: "LUFTHANSA",
          flightNo: "LH 441",
          duration: "8hr(s) 45min(s)",
          flightClass: "Premium Economy (W)",
          status: "Confirmed",
          from: { code: "JFK", city: "New York, United States", time: "22:20", terminal: "1" },
          to: { code: "FRA", city: "Frankfurt, Germany", time: "11:05", terminal: "1" },
          aircraft: "Airbus A340-600",
          stops: "0",
          meals: "Available",
          distance: "3,851"
        },
        {
          departureDay: "THURSDAY, 12 SEP 2025",
          airline: "AIR FRANCE",
          flightNo: "AF 1518",
          duration: "1hr(s) 25min(s)",
          flightClass: "Economy Class (Y)",
          status: "Confirmed",
          from: { code: "FRA", city: "Frankfurt, Germany", time: "14:30", terminal: "1" },
          to: { code: "CDG", city: "Paris, France", time: "15:55", terminal: "2F" },
          aircraft: "Airbus A320",
          stops: "0",
          meals: "Light meal",
          distance: "297"
        },
        {
          departureDay: "SUNDAY, 15 SEP 2025",
          airline: "KLM",
          flightNo: "KL 1234",
          duration: "1hr(s) 15min(s)",
          flightClass: "Economy Class (Y)",
          status: "Confirmed",
          from: { code: "CDG", city: "Paris, France", time: "16:45", terminal: "2F" },
          to: { code: "AMS", city: "Amsterdam, Netherlands", time: "18:00", terminal: "3" },
          aircraft: "Boeing 737-800",
          stops: "0",
          meals: "Snack",
          distance: "245"
        }
      ]
    }
  }
];

async function runAirlineQualityTests() {
  console.log('🎫 Starting Airline-Quality E-Ticket Tests...\n');

  try {
    // Test backend health
    console.log('🏥 Testing Backend Health...');
    const healthResponse = await axios.get(`${BASE_URL}/api/tickets/health`);
    console.log('✅ Backend is running:', healthResponse.data.message);

    // Create output directory
    const outputDir = path.join(__dirname, 'test-outputs');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Run tests for each case
    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`\n${i + 1}️⃣ Testing: ${testCase.name}`);
      
      try {
        const pdfResponse = await axios.post(`${BASE_URL}/api/tickets/generate-pdf`, testCase.data, {
          responseType: 'arraybuffer',
          timeout: 30000
        });
        
        if (pdfResponse.status === 200) {
          const filename = `airline-quality-${testCase.data.reservationCode}.pdf`;
          const pdfPath = path.join(outputDir, filename);
          fs.writeFileSync(pdfPath, pdfResponse.data);
          console.log(`✅ PDF generated successfully: ${filename}`);
          console.log(`   File size: ${pdfResponse.data.length} bytes`);
          console.log(`   Airlines: ${testCase.data.segments.map(s => s.airline).join(', ')}`);
          console.log(`   Segments: ${testCase.data.segments.length}`);
        }
      } catch (error) {
        console.log(`❌ Test failed for ${testCase.name}:`, error.message);
        if (error.response) {
          console.log(`   Status: ${error.response.status}`);
          console.log(`   Error: ${error.response.data}`);
        }
      }
    }

    console.log('\n🎉 Airline-Quality E-Ticket Tests Completed!');
    console.log('\n📋 Test Summary:');
    console.log('- ✅ Professional airline-style layout');
    console.log('- ✅ Dynamic airline logo integration');
    console.log('- ✅ Route summaries with airplane emoji');
    console.log('- ✅ Enhanced departure headers');
    console.log('- ✅ Rectangular flight segment boxes');
    console.log('- ✅ Professional typography (Helvetica Neue)');
    console.log('- ✅ Confirmed status with green styling');
    console.log('- ✅ Important Information section');
    console.log('- ✅ Updated footer branding');
    console.log('- ✅ Multi-segment and multi-airline support');
    console.log('\n📄 Generated PDFs in test-outputs/:');
    console.log('- airline-quality-PDF0NX.pdf (Aegean Airlines)');
    console.log('- airline-quality-MULTI001.pdf (Ryanair + Emirates)');
    console.log('- airline-quality-EUR2025.pdf (Lufthansa + Air France + KLM)');

  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAirlineQualityTests();
}

module.exports = { runAirlineQualityTests, testCases };
