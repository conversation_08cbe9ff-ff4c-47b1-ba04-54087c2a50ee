#!/usr/bin/env node

/**
 * Test script to verify the checkout page fix
 * This simulates the user flow and tests that real user data loads properly
 */

const puppeteer = require('puppeteer');

async function testCheckoutFix() {
  console.log('🧪 Testing checkout page fix...');
  
  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: false,
      defaultViewport: { width: 1200, height: 800 }
    });
    
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Browser Error:', msg.text());
      } else if (msg.text().includes('CheckoutPageFixed')) {
        console.log('🔍 Checkout Log:', msg.text());
      }
    });
    
    // Set up localStorage with real user data (Atedal Zemit)
    await page.evaluateOnNewDocument(() => {
      localStorage.setItem('bookingPassengers', JSON.stringify([
        { id: 1, firstName: 'Atedal', lastName: 'Zemit' }
      ]));
      localStorage.setItem('bookingEmail', '<EMAIL>');
      localStorage.setItem('bookingSelectedFlight', JSON.stringify({
        id: 'test-flight-1',
        flight: {
          number: 'BA 123',
          departure: { airport: 'LHR', city: 'London Heathrow', time: '2025-07-15 10:00' },
          arrival: { airport: 'JFK', city: 'New York JFK', time: '2025-07-15 18:00' },
          duration: '8h 00m'
        },
        airline: { name: 'British Airways', code: 'BA' },
        price: { total: 4.99, currency: 'USD', originalPrice: 650 }
      }));
      localStorage.setItem('bookingTripType', 'oneWay');
    });
    
    console.log('📝 Step 1: Loading checkout page...');
    await page.goto('http://localhost:5174/checkout', { waitUntil: 'networkidle0' });
    
    // Wait for loading to complete
    await page.waitForTimeout(2000);
    
    // Check if the page loaded successfully
    const pageContent = await page.content();
    
    if (pageContent.includes('Loading checkout...')) {
      console.log('⏳ Still loading...');
      await page.waitForTimeout(3000);
    }
    
    // Check for error states
    const hasError = await page.$('.text-red-500');
    if (hasError) {
      const errorText = await page.$eval('.text-red-500', el => el.textContent);
      console.log('❌ Error found:', errorText);
      return false;
    }
    
    // Check for successful checkout page elements
    const hasCheckoutForm = await page.$('form');
    const hasStripeElements = await page.$('.stripe-element, [data-testid="stripe"]');
    const hasPaymentButton = await page.$('button[type="submit"], .payment-button');
    
    console.log('✅ Step 2: Checking page elements...');
    console.log('- Checkout form:', hasCheckoutForm ? '✅' : '❌');
    console.log('- Payment elements:', hasStripeElements ? '✅' : '❌');
    console.log('- Payment button:', hasPaymentButton ? '✅' : '❌');
    
    // Check if user data is displayed
    const pageText = await page.evaluate(() => document.body.textContent);
    const hasUserName = pageText.includes('Atedal') || pageText.includes('Zemit');
    const hasUserEmail = pageText.includes('<EMAIL>');
    const hasFlightInfo = pageText.includes('BA 123') || pageText.includes('British Airways');
    
    console.log('✅ Step 3: Checking user data display...');
    console.log('- User name displayed:', hasUserName ? '✅' : '❌');
    console.log('- User email displayed:', hasUserEmail ? '✅' : '❌');
    console.log('- Flight info displayed:', hasFlightInfo ? '✅' : '❌');
    
    const success = hasCheckoutForm && (hasUserName || hasUserEmail || hasFlightInfo);
    
    if (success) {
      console.log('🎉 SUCCESS: Checkout page is working correctly!');
      console.log('✅ Real user data (Atedal Zemit) is loading properly');
      console.log('✅ No more blank screen issue');
    } else {
      console.log('❌ FAILED: Checkout page still has issues');
    }
    
    // Keep browser open for manual inspection
    console.log('🔍 Browser will stay open for 10 seconds for manual inspection...');
    await page.waitForTimeout(10000);
    
    return success;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
testCheckoutFix().then(success => {
  if (success) {
    console.log('🎯 Checkout fix verification: PASSED');
    process.exit(0);
  } else {
    console.log('🚨 Checkout fix verification: FAILED');
    process.exit(1);
  }
}).catch(error => {
  console.error('💥 Test runner error:', error);
  process.exit(1);
});
