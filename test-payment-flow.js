// Test script to verify payment flow
const testPaymentFlow = async () => {
  console.log('🧪 Testing payment flow...');
  
  try {
    // Test Stripe payment intent creation
    const response = await fetch('http://localhost:5001/api/payments/stripe/create-intent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: 4.99,
        currency: 'usd',
        metadata: {
          description: 'Test dummy flight ticket'
        }
      })
    });
    
    const data = await response.json();
    console.log('✅ Stripe payment intent response:', data);
    
    if (data.success && data.clientSecret) {
      console.log('✅ Payment intent created successfully');
      
      // Test ticket generation with correct data structure
      const ticketResponse = await fetch('http://localhost:5001/api/tickets/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          flightData: {
            airline: 'Ryanair',
            flightNumber: 'FR1234',
            departure: { time: '10:30', airport: 'Manchester (MAN)' },
            arrival: { time: '14:15', airport: 'Madrid (MAD)' },
            duration: '2h 45m',
            price: 89.99
          },
          passengerData: [
            { firstName: 'John', lastName: 'Doe' }
          ],
          email: '<EMAIL>',
          paymentId: data.paymentIntentId
        })
      });
      
      const ticketData = await ticketResponse.json();
      console.log('✅ Ticket generation response:', ticketData);
      
      if (ticketData.success) {
        console.log('🎉 Complete flow test PASSED!');
        return true;
      } else {
        console.error('❌ Ticket generation failed:', ticketData);
        return false;
      }
      
    } else {
      console.error('❌ Payment intent creation failed:', data);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
};

// Run the test
testPaymentFlow().then(success => {
  if (success) {
    console.log('🎉 ALL TESTS PASSED - Payment flow is working!');
  } else {
    console.log('❌ TESTS FAILED - Payment flow has issues');
  }
});
