# 🚀 Manual Startup Guide for VerifiedOnward

Since there appears to be an environment issue preventing automated script execution, here's a manual startup guide.

## 🔧 Prerequisites Check

First, verify Node.js is installed:
```bash
node --version
npm --version
```

If Node.js is not installed, install it from: https://nodejs.org/

## 📋 Step-by-Step Manual Startup

### Step 1: Open Terminal and Navigate to Project
```bash
cd /Users/<USER>/Documents/augment-projects/InstantDummyTicket
```

### Step 2: Kill Any Existing Processes
```bash
# Kill processes on port 5001 (backend)
lsof -ti:5001 | xargs kill -9

# Kill processes on port 5173 (frontend)  
lsof -ti:5173 | xargs kill -9
```

### Step 3: Install Dependencies (if needed)

**Backend Dependencies:**
```bash
cd backend
npm install
cd ..
```

**Frontend Dependencies:**
```bash
cd frontend
npm install
cd ..
```

### Step 4: Start Backend Server

Open a new terminal window/tab and run:
```bash
cd /Users/<USER>/Documents/augment-projects/InstantDummyTicket/backend
node server.js
```

You should see output like:
```
🚀 VerifiedOnward Backend Server starting...
✅ Server running on port 5001
🌐 API available at: http://localhost:5001/api/
```

### Step 5: Start Frontend Server

Open another new terminal window/tab and run:
```bash
cd /Users/<USER>/Documents/augment-projects/InstantDummyTicket/frontend
npm run dev
```

You should see output like:
```
  VITE v7.0.0  ready in 1234 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
```

### Step 6: Verify Everything is Working

1. **Check Backend Health:**
   Open browser to: http://localhost:5001/api/health
   Should show: `{"status":"OK","message":"VerifiedOnward API is running"}`

2. **Check Frontend:**
   Open browser to: http://localhost:5173
   Should show the VerifiedOnward homepage

## 🎯 Access Points

Once both servers are running:

- **🌐 Main Application:** http://localhost:5173
- **🔧 Backend API:** http://localhost:5001/api/
- **❤️ Health Check:** http://localhost:5001/api/health

## 🛠️ Troubleshooting

### Issue: "EADDRINUSE" Error
**Solution:** Port is already in use
```bash
# Find and kill the process
lsof -ti:5001 | xargs kill -9  # For backend
lsof -ti:5173 | xargs kill -9  # For frontend
```

### Issue: "Module not found" Error
**Solution:** Dependencies not installed
```bash
# In backend directory
cd backend && npm install

# In frontend directory  
cd frontend && npm install
```

### Issue: Backend starts but API calls fail
**Solution:** Check CORS settings and ensure both servers are running

### Issue: Frontend shows blank page
**Solution:** Check browser console for errors, ensure backend is running

## 📊 Monitoring

### Check if servers are running:
```bash
# Check backend (should show process)
lsof -i :5001

# Check frontend (should show process)
lsof -i :5173
```

### View server logs:
- Backend logs will appear in the terminal where you ran `node server.js`
- Frontend logs will appear in the terminal where you ran `npm run dev`

## 🔄 Stopping Servers

To stop the servers:
1. Go to each terminal window
2. Press `Ctrl+C` to stop the process
3. Or close the terminal windows

## 🚨 If Manual Startup Doesn't Work

If you're still having issues:

1. **Check Node.js Installation:**
   ```bash
   which node
   which npm
   ```

2. **Check Project Structure:**
   ```bash
   ls -la backend/
   ls -la frontend/
   ```

3. **Try Alternative Startup:**
   ```bash
   # Try using npx for frontend
   cd frontend
   npx vite
   ```

4. **Check for Permission Issues:**
   ```bash
   # Make scripts executable
   chmod +x start-dev.sh
   chmod +x stop-dev.sh
   chmod +x health-check.sh
   ```

## 📞 Getting Help

If you continue to have issues:
1. Check the error messages in the terminal
2. Verify Node.js version compatibility
3. Ensure all dependencies are installed
4. Check for port conflicts with other applications

## 🎉 Success Indicators

You'll know everything is working when:
- ✅ Backend terminal shows "Server running on port 5001"
- ✅ Frontend terminal shows "Local: http://localhost:5173/"
- ✅ http://localhost:5173 loads the VerifiedOnward homepage
- ✅ http://localhost:5001/api/health returns JSON response
- ✅ You can search for flights and see results
