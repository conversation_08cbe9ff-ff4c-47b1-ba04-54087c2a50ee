# VerifiedOnward.com PDF Generation System

## 🎯 Overview

A complete PDF generation system for VerifiedOnward.com that creates professional, embassy-ready flight reservation documents. The system generates pixel-perfect PDFs that match real airline reservations for visa applications and travel documentation.

## 🏗️ Architecture

### Frontend Components
- **FlightTicket.jsx** - React component that renders professional airline-style tickets
- **PDFDownloadButton.jsx** - Handles PDF generation and download
- **FlightTicketDemo.jsx** - Interactive demo page with multiple examples
- **SuccessPage.jsx** - Integrated PDF download in checkout success flow

### Backend Services
- **pdfService.js** - Core PDF generation using Puppeteer
- **airlineLogoService-enhanced.js** - Airline branding and logo management
- **routes/tickets.js** - API endpoints for PDF generation and verification

### Key Features
- ✅ **Server-side PDF generation** with Puppeteer (no browser dependencies)
- ✅ **Professional airline styling** matching real e-tickets
- ✅ **QR code integration** for reservation verification
- ✅ **Airline logos and branding** with fallback placeholders
- ✅ **Watermarks and security features** for authenticity
- ✅ **Multi-segment flight support** (connecting flights, round trips)
- ✅ **Multiple passenger support** with detailed passenger tables
- ✅ **Embassy-compliant format** suitable for visa applications
- ✅ **Print-optimized styling** for physical documents

## 🚀 Quick Start

### Prerequisites
```bash
Node.js 16+ 
npm or yarn
```

### Installation
```bash
# Backend dependencies
cd backend
npm install puppeteer qrcode

# Frontend dependencies  
cd frontend
npm install
```

### Start Services
```bash
# Start backend (Terminal 1)
cd backend
npm run dev

# Start frontend (Terminal 2)  
cd frontend
npm run dev
```

### Test the System
```bash
# Run comprehensive tests
node test-pdf-system.js
```

## 📡 API Endpoints

### POST /api/tickets/generate-pdf
Generate PDF directly from ticket data.

**Request Body:**
```json
{
  "tripDates": "18 JUL 2021 › 19 JUL 2021",
  "destination": "TRIP TO NEW YORK CITY", 
  "passengers": [{"name": "COOPER/JANE MR."}],
  "reservationCode": "NHG8IQ",
  "airlineReservationCode": "NHG8IQ",
  "segments": [...],
  "showNotice": true,
  "customNotice": "Custom disclaimer text"
}
```

**Response:** PDF file download

### POST /api/tickets/generate
Generate PDF from booking data (full workflow).

**Request Body:**
```json
{
  "bookingReference": "ABC123",
  "passengers": [...],
  "outboundFlight": {...},
  "returnFlight": {...},
  "totalPrice": 299
}
```

**Response:**
```json
{
  "success": true,
  "bookingReference": "ABC123", 
  "reservationCode": "ABC123",
  "downloadUrl": "/api/tickets/download/ABC123"
}
```

### GET /api/tickets/download/:bookingReference
Download generated PDF file.

### GET /api/tickets/verify/:reservationCode
Verify reservation (for QR code scanning).

**Response:**
```json
{
  "success": true,
  "verified": true,
  "reservationCode": "ABC123",
  "createdAt": "2025-01-16T...",
  "passengers": 2,
  "destination": "New York City"
}
```

## 🎨 Frontend Usage

### Basic FlightTicket Component
```jsx
import FlightTicket from '../components/FlightTicket';

<FlightTicket
  tripDates="18 JUL 2021 › 19 JUL 2021"
  destination="TRIP TO NEW YORK CITY"
  passengers={[{ name: "COOPER/JANE MR." }]}
  reservationCode="NHG8IQ"
  airlineReservationCode="NHG8IQ"
  segments={[...]}
  showNotice={true}
  customNotice="Custom disclaimer"
/>
```

### PDF Download Button
```jsx
import PDFDownloadButton from '../components/PDFDownloadButton';

<PDFDownloadButton 
  ticketData={ticketData}
  buttonText="Download PDF"
  className="custom-styles"
/>
```

### Print Button
```jsx
import { PrintButton } from '../components/PDFDownloadButton';

<PrintButton 
  buttonText="Print Ticket"
  className="custom-styles"
/>
```

## 🔧 Configuration

### PDF Settings
Located in `backend/services/pdfService.js`:
```javascript
// PDF generation settings
const pdfBuffer = await page.pdf({
  format: 'A4',
  margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' },
  printBackground: true,
  preferCSSPageSize: false
});
```

### QR Code Settings
```javascript
const qrCodeDataURL = await QRCode.toDataURL(qrData, {
  width: 100,
  margin: 1,
  color: { dark: '#000000', light: '#FFFFFF' }
});
```

### Airline Logos
Add logos to `backend/assets/airline-logos/` directory.
Supported formats: PNG, JPG, SVG

## 🧪 Testing

### Demo Pages
- **Flight Ticket Demo**: `http://localhost:5175/flight-ticket-demo`
- **Checkout Test**: `http://localhost:5175/checkout-test`

### Test Script
```bash
node test-pdf-system.js
```

Tests include:
- Backend health check
- Direct PDF generation
- Full booking workflow
- PDF download functionality
- QR code verification

## 📦 Deployment

### Environment Variables
```bash
# Backend
NODE_ENV=production
PORT=3001

# Frontend  
VITE_API_URL=https://your-backend-domain.com
```

### Production Build
```bash
# Backend
cd backend
npm start

# Frontend
cd frontend
npm run build
npm run preview
```

### Docker Deployment
```dockerfile
# Dockerfile example for backend
FROM node:18-alpine
RUN apk add --no-cache chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

## 🔒 Security Features

- **Watermarks** - Subtle branding prevents unauthorized reproduction
- **QR Code Verification** - Links to verification endpoint
- **Document IDs** - Unique identifiers for each reservation
- **Timestamp Generation** - Shows when document was created
- **Professional Disclaimers** - Clear legal notices

## 🎯 Business Benefits

1. **Professional Credibility** - Real airline-style tickets build user trust
2. **Embassy Compliance** - Format designed for visa application requirements  
3. **User Convenience** - Instant PDF generation and download
4. **Scalable Solution** - Handles any flight configuration
5. **Brand Consistency** - Integrated VerifiedOnward branding
6. **Revenue Generation** - Premium feature for paid reservations

## 🐛 Troubleshooting

### Common Issues

**PDF Generation Fails**
- Check Puppeteer installation: `npm list puppeteer`
- Verify Chrome/Chromium availability
- Check memory limits in production

**QR Codes Not Showing**
- Verify QRCode package: `npm list qrcode`
- Check network connectivity for verification URL

**Fonts Not Loading**
- Ensure system fonts are available
- Add web font fallbacks in CSS

### Debug Mode
```javascript
// Enable Puppeteer debug mode
const browser = await puppeteer.launch({
  headless: false, // Show browser
  devtools: true   // Open DevTools
});
```

## 📞 Support

For technical support or feature requests:
- Email: <EMAIL>
- Documentation: https://docs.verifiedonward.com
- GitHub Issues: https://github.com/verifiedonward/issues

---

**© 2025 VerifiedOnward.com - Professional Flight Reservations**
