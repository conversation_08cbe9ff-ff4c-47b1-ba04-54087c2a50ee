// Test flight search functionality after cleanup
const testFlightSearch = async () => {
  console.log('🧪 Testing cleaned flight search functionality...');
  
  try {
    // Test 1: Health check
    console.log('\n1. Testing backend health...');
    const healthResponse = await fetch('http://localhost:5001/api/health');
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData);
    
    // Test 2: Airport search
    console.log('\n2. Testing airport search...');
    const airportResponse = await fetch('http://localhost:5001/api/flights/airports?query=London');
    const airportData = await airportResponse.json();
    console.log('✅ Airport search:', `Found ${airportData.data?.length || 0} airports`);
    
    // Test 3: Flight search with valid data
    console.log('\n3. Testing flight search with valid data...');
    const flightResponse = await fetch('http://localhost:5001/api/flights/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        origin: 'MAN',
        destination: 'MAD',
        date: '2025-07-15',
        tripType: 'oneWay'
      })
    });
    
    const flightData = await flightResponse.json();
    console.log('✅ Flight search result:', {
      success: flightData.success,
      flightCount: flightData.data?.flights?.length || 0,
      message: flightData.message || 'No message'
    });
    
    // Test 4: Flight search with invalid data (should fail validation)
    console.log('\n4. Testing flight search with invalid data...');
    try {
      const invalidResponse = await fetch('http://localhost:5001/api/flights/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          origin: 'London', // Invalid - should be IATA code
          destination: 'Madrid', // Invalid - should be IATA code
          date: '2025-07-15',
          tripType: 'oneWay'
        })
      });
      
      const invalidData = await invalidResponse.json();
      console.log('✅ Invalid data test:', {
        success: invalidData.success,
        error: invalidData.error,
        message: invalidData.message
      });
    } catch (error) {
      console.log('✅ Invalid data correctly rejected:', error.message);
    }
    
    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

// Run the test
testFlightSearch();
