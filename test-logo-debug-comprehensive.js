#!/usr/bin/env node

/**
 * Comprehensive Logo Debug Test
 * Tests the entire logo pipeline from URL retrieval to PDF embedding
 */

const airlineLogoService = require('./backend/services/airlineLogoService');
const pdfService = require('./backend/services/pdfService');

async function testLogoSystem() {
  console.log('🔍 COMPREHENSIVE LOGO SYSTEM DEBUG TEST');
  console.log('=' .repeat(60));

  const testAirlines = [
    { name: 'AIR FRANCE', expectedCode: 'AF' },
    { name: 'ETHIOPIAN AIRLINES', expectedCode: 'ET' },
    { name: 'BRITISH AIRWAYS', expectedCode: 'BA' },
    { name: 'RY<PERSON>A<PERSON>', expectedCode: 'FR' },
    { name: 'EMIRATES', expectedCode: 'EK' },
    { name: 'LUFTHANSA', expectedCode: 'LH' }
  ];

  // pdfService is already instantiated from the require

  console.log('\n📋 PHASE 1: Airline Code Mapping Test');
  console.log('-'.repeat(40));
  
  for (const airline of testAirlines) {
    const mappedCode = pdfService.getAirlineCode(airline.name);
    const status = mappedCode === airline.expectedCode ? '✅' : '❌';
    console.log(`${status} ${airline.name} -> ${mappedCode} (expected: ${airline.expectedCode})`);
  }

  console.log('\n🌐 PHASE 2: Logo URL Sources Test');
  console.log('-'.repeat(40));
  
  for (const airline of testAirlines) {
    console.log(`\n🔍 Testing ${airline.name} (${airline.expectedCode}):`);
    
    try {
      const logoSources = airlineLogoService.getAllLogoSources(airline.expectedCode);
      console.log(`  📊 Found ${logoSources.length} logo sources:`);
      
      for (let i = 0; i < Math.min(3, logoSources.length); i++) {
        const source = logoSources[i];
        console.log(`    ${i + 1}. ${source}`);
        
        // Test URL accessibility
        try {
          const response = await fetch(source, { 
            method: 'HEAD',
            timeout: 5000,
            headers: {
              'User-Agent': 'VerifiedOnward-Logo-Test/1.0'
            }
          });
          const status = response.ok ? '✅ Accessible' : `❌ HTTP ${response.status}`;
          console.log(`       ${status}`);
        } catch (error) {
          console.log(`       ❌ Error: ${error.message}`);
        }
      }
    } catch (error) {
      console.log(`  ❌ Error getting logo sources: ${error.message}`);
    }
  }

  console.log('\n🎨 PHASE 3: Logo Retrieval Test');
  console.log('-'.repeat(40));
  
  for (const airline of testAirlines) {
    console.log(`\n🔍 Testing logo retrieval for ${airline.name}:`);
    
    try {
      const logo = await airlineLogoService.getLogoForPDF(airline.expectedCode, airline.name);
      
      const isBase64PNG = logo.startsWith('data:image/png;base64,');
      const isSVG = logo.startsWith('data:image/svg');
      const isURL = logo.startsWith('http');
      
      let logoType = 'Unknown';
      let status = '❌';
      
      if (isBase64PNG) {
        logoType = 'Base64 PNG (Authentic)';
        status = '✅';
      } else if (isSVG) {
        logoType = 'Generated SVG (Fallback)';
        status = '⚠️';
      } else if (isURL) {
        logoType = 'External URL';
        status = '⚠️';
      }
      
      console.log(`  ${status} Type: ${logoType}`);
      console.log(`  📏 Size: ${logo.length} characters`);
      
      if (isBase64PNG) {
        // Decode and check PNG header
        try {
          const base64Data = logo.split(',')[1];
          const buffer = Buffer.from(base64Data, 'base64');
          const isPNG = buffer.slice(0, 8).toString('hex') === '89504e470d0a1a0a';
          console.log(`  🖼️  Valid PNG: ${isPNG ? '✅' : '❌'}`);
          console.log(`  📊 Image size: ${buffer.length} bytes`);
        } catch (error) {
          console.log(`  ❌ PNG validation error: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.log(`  ❌ Logo retrieval failed: ${error.message}`);
    }
  }

  console.log('\n📄 PHASE 4: PDF Generation Test');
  console.log('-'.repeat(40));
  
  // Create test ticket data
  const testTicketData = {
    reservationCode: 'LOGOTEST123',
    airlineReservationCode: 'AF123VW',
    tripDates: '21 JUL 2025 • 28 JUL 2025',
    destination: 'TRIP TO PARIS CHARLES DE GAULLE',
    passengers: [
      { name: 'JOHN/DOE' }
    ],
    segments: [
      {
        airline: 'AIR FRANCE',
        flightNo: 'AF 1669',
        from: { code: 'MAN', name: 'Manchester Airport' },
        to: { code: 'CDG', name: 'Paris Charles de Gaulle' },
        departureTime: '11:50',
        arrivalTime: '14:40',
        departureDay: 'MONDAY, JUL 21',
        duration: '2H 50M',
        flightClass: 'Economy Class (M)',
        stops: 0
      },
      {
        airline: 'ETHIOPIAN AIRLINES',
        flightNo: 'ET 900',
        from: { code: 'CDG', name: 'Paris Charles de Gaulle' },
        to: { code: 'MAN', name: 'Manchester Airport' },
        departureTime: '13:40',
        arrivalTime: '15:30',
        departureDay: 'MONDAY, JUL 28',
        duration: '2H 50M',
        flightClass: 'Economy Class (M)',
        stops: 0
      }
    ]
  };

  try {
    const outputPath = './test-logo-debug-output.pdf';
    console.log(`\n📄 Generating test PDF: ${outputPath}`);
    
    await pdfService.generatePDF(testTicketData, outputPath);
    console.log('✅ PDF generated successfully!');
    console.log(`📁 Output: ${outputPath}`);
    
    // Check file size
    const fs = require('fs');
    const stats = fs.statSync(outputPath);
    console.log(`📊 File size: ${(stats.size / 1024).toFixed(2)} KB`);
    
  } catch (error) {
    console.log(`❌ PDF generation failed: ${error.message}`);
  }

  console.log('\n🏁 LOGO DEBUG TEST COMPLETE');
  console.log('=' .repeat(60));
}

// Run the test
testLogoSystem().catch(console.error);
