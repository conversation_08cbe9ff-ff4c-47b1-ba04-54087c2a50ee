<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Success Page - VerifiedOnward</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-button {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .note {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 VerifiedOnward Success Page Test</h1>
        
        <div class="description">
            This page allows you to test the success page functionality with different scenarios. 
            Click the buttons below to navigate to the success page with pre-populated test data.
        </div>

        <div class="note">
            <strong>Note:</strong> These tests simulate the post-payment experience with realistic data 
            to verify the success page displays correctly, payment IDs persist, and PDF downloads work.
        </div>

        <a href="#" class="test-button" onclick="testOneWayFlight()">
            ✈️ Test One-Way Flight Success
        </a>

        <a href="#" class="test-button" onclick="testReturnFlight()">
            🔄 Test Return Flight Success
        </a>

        <a href="#" class="test-button" onclick="testMultiplePassengers()">
            👥 Test Multiple Passengers Success
        </a>

        <a href="#" class="test-button" onclick="testPDFDownload()">
            📄 Test PDF Download Functionality
        </a>
    </div>

    <script>
        function testOneWayFlight() {
            const testData = {
                bookingReference: 'VO' + Date.now().toString().slice(-8),
                paymentData: {
                    paymentId: 'PAY_' + Date.now() + '_TEST123'
                },
                selectedFlight: {
                    airline: { name: 'Lufthansa', code: 'LH' },
                    flight: {
                        number: 'LH441',
                        departure: { 
                            airport: 'FRA', 
                            time: new Date(Date.now() + 86400000).toISOString() 
                        },
                        arrival: { 
                            airport: 'JFK', 
                            time: new Date(Date.now() + 86400000 + 28800000).toISOString() 
                        }
                    },
                    price: { originalPrice: '899.99' }
                },
                passengers: [
                    { firstName: 'John', lastName: 'Doe' }
                ],
                email: '<EMAIL>',
                tripType: 'oneway'
            };

            // Navigate to success page with test data
            window.location.href = '/success?' + new URLSearchParams({
                test: 'true',
                data: JSON.stringify(testData)
            });
        }

        function testReturnFlight() {
            const testData = {
                bookingReference: 'VO' + Date.now().toString().slice(-8),
                paymentData: {
                    paymentId: 'PAY_' + Date.now() + '_RET456'
                },
                selectedFlight: {
                    airline: { name: 'British Airways', code: 'BA' },
                    flight: {
                        number: 'BA117',
                        departure: { 
                            airport: 'LHR', 
                            time: new Date(Date.now() + 86400000).toISOString() 
                        },
                        arrival: { 
                            airport: 'JFK', 
                            time: new Date(Date.now() + 86400000 + 25200000).toISOString() 
                        }
                    },
                    price: { originalPrice: '1299.99' }
                },
                returnFlight: {
                    airline: { name: 'British Airways', code: 'BA' },
                    flight: {
                        number: 'BA112',
                        departure: { 
                            airport: 'JFK', 
                            time: new Date(Date.now() + 604800000).toISOString() 
                        },
                        arrival: { 
                            airport: 'LHR', 
                            time: new Date(Date.now() + 604800000 + 25200000).toISOString() 
                        }
                    }
                },
                passengers: [
                    { firstName: 'Jane', lastName: 'Smith' }
                ],
                email: '<EMAIL>',
                tripType: 'return'
            };

            window.location.href = '/success?' + new URLSearchParams({
                test: 'true',
                data: JSON.stringify(testData)
            });
        }

        function testMultiplePassengers() {
            const testData = {
                bookingReference: 'VO' + Date.now().toString().slice(-8),
                paymentData: {
                    paymentId: 'PAY_' + Date.now() + '_MULTI789'
                },
                selectedFlight: {
                    airline: { name: 'Emirates', code: 'EK' },
                    flight: {
                        number: 'EK205',
                        departure: { 
                            airport: 'DXB', 
                            time: new Date(Date.now() + 172800000).toISOString() 
                        },
                        arrival: { 
                            airport: 'JFK', 
                            time: new Date(Date.now() + 172800000 + 50400000).toISOString() 
                        }
                    },
                    price: { originalPrice: '2199.99' }
                },
                passengers: [
                    { firstName: 'Michael', lastName: 'Johnson' },
                    { firstName: 'Sarah', lastName: 'Johnson' }
                ],
                email: '<EMAIL>',
                tripType: 'oneway'
            };

            window.location.href = '/success?' + new URLSearchParams({
                test: 'true',
                data: JSON.stringify(testData)
            });
        }

        function testPDFDownload() {
            alert('This will test the PDF download functionality. After clicking OK, you will be taken to a success page where you can test the download button.');
            testOneWayFlight();
        }
    </script>
</body>
</html>
