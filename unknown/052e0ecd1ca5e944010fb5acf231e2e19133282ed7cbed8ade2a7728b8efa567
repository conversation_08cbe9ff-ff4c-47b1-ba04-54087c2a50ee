const express = require('express');
const router = express.Router();
const flightService = require('../services/flightService');

// Flight routes with SerpAPI Google Flights integration



// Search flights - ready for fresh API integration
router.post('/search', async (req, res) => {
  try {
    const { origin, destination, date, returnDate, tripType = 'oneWay' } = req.body;

    // Validate required fields
    if (!origin || !destination || !date) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'origin, destination, and date are required'
      });
    }

    // Validate return date if trip type is return
    if (tripType === 'return' && !returnDate) {
      return res.status(400).json({
        error: 'Missing return date',
        message: 'Return date is required for return trips'
      });
    }

    // Validate IATA codes (should be 3 letters)
    if (origin.length !== 3 || destination.length !== 3) {
      return res.status(400).json({
        error: 'Invalid airport codes',
        message: 'Airport codes must be 3-letter IATA codes'
      });
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      return res.status(400).json({
        error: 'Invalid date format',
        message: 'Date must be in YYYY-MM-DD format (e.g., 2025-07-20)'
      });
    }

    // Validate return date format if provided
    if (returnDate && !dateRegex.test(returnDate)) {
      return res.status(400).json({
        error: 'Invalid return date format',
        message: 'Return date must be in YYYY-MM-DD format (e.g., 2025-07-20)'
      });
    }

    // Validate date is not in the past
    const searchDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (searchDate < today) {
      return res.status(400).json({
        error: 'Invalid date',
        message: 'Departure date cannot be in the past'
      });
    }

    // Validate return date is after departure date
    if (returnDate) {
      const returnSearchDate = new Date(returnDate);
      if (returnSearchDate <= searchDate) {
        return res.status(400).json({
          error: 'Invalid return date',
          message: 'Return date must be after departure date'
        });
      }
    }

    const logMessage = tripType === 'return'
      ? `🔍 Searching ${tripType} flights ${origin.toUpperCase()} → ${destination.toUpperCase()} on ${date}, returning ${returnDate}`
      : `🔍 Searching ${tripType} flights ${origin.toUpperCase()} → ${destination.toUpperCase()} on ${date}`;
    console.log(logMessage);

    // Search flights using SerpAPI
    const searchResult = await flightService.searchFlights({
      origin: origin.toUpperCase(),
      destination: destination.toUpperCase(),
      date,
      returnDate,
      tripType,
      passengers: 1
    });

    // Handle different response formats based on trip type
    let responseData;
    let flightCount = 0;

    if (tripType === 'return') {
      responseData = {
        tripType: 'return',
        outboundFlights: searchResult.outboundFlights || [],
        returnFlights: searchResult.returnFlights || []
      };
      flightCount = (searchResult.outboundFlights?.length || 0) + (searchResult.returnFlights?.length || 0);
    } else {
      responseData = {
        tripType: 'oneWay',
        flights: searchResult.flights || []
      };
      flightCount = searchResult.flights?.length || 0;
    }

    res.json({
      success: true,
      data: responseData,
      meta: {
        origin: origin.toUpperCase(),
        destination: destination.toUpperCase(),
        date,
        returnDate: returnDate || null,
        tripType,
        count: flightCount
      }
    });

  } catch (error) {
    console.error('Flight search error:', error);

    // Provide more specific error messages
    let errorMessage = 'Flight search failed';
    let statusCode = 500;

    if (error.message.includes('SerpAPI key not configured')) {
      errorMessage = 'Flight search service is not properly configured. Please try again later.';
      statusCode = 503;
    } else if (error.message.includes('timeout')) {
      errorMessage = 'Flight search took too long. Please try again with different criteria.';
      statusCode = 408;
    } else if (error.message.includes('Network Error') || error.message.includes('ECONNREFUSED')) {
      errorMessage = 'Unable to connect to flight search service. Please check your internet connection and try again.';
      statusCode = 503;
    } else if (error.message.includes('API')) {
      errorMessage = 'Flight search service is temporarily unavailable. Please try again later.';
      statusCode = 503;
    } else {
      errorMessage = error.message || 'An unexpected error occurred during flight search';
    }

    res.status(statusCode).json({
      success: false,
      error: 'Flight search failed',
      message: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Get airport suggestions - ready for fresh API integration
router.get('/airports', async (req, res) => {
  try {
    const { query } = req.query;

    if (!query || query.length < 2) {
      return res.status(400).json({
        error: 'Invalid query',
        message: 'Query must be at least 2 characters long'
      });
    }

    console.log(`🔍 Searching airports for: "${query}"`);

    // Search airports using SerpAPI
    const airports = await flightService.searchAirports(query);

    res.json({
      success: true,
      data: airports,
      meta: {
        query,
        count: airports.length
      }
    });

  } catch (error) {
    console.error('Airport search error:', error);

    // Provide more specific error messages
    let errorMessage = 'Airport search failed';
    let statusCode = 500;

    if (error.message.includes('timeout')) {
      errorMessage = 'Airport search took too long. Please try again.';
      statusCode = 408;
    } else if (error.message.includes('Network Error') || error.message.includes('ECONNREFUSED')) {
      errorMessage = 'Unable to connect to airport search service. Please check your internet connection.';
      statusCode = 503;
    } else {
      errorMessage = error.message || 'An unexpected error occurred during airport search';
    }

    res.status(statusCode).json({
      success: false,
      error: 'Airport search failed',
      message: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    res.json({
      success: true,
      message: 'Flight service is running - ready for fresh API integration',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Flight service health check failed',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// Get flight details by ID
router.get('/:flightId', async (req, res) => {
  try {
    const { flightId } = req.params;

    if (!flightId) {
      return res.status(400).json({
        error: 'Missing flight ID',
        message: 'Flight ID is required'
      });
    }

    console.log(`📋 Getting flight details for: ${flightId}`);

    const flightDetails = await flightService.getFlightDetails(flightId);

    res.json({
      success: true,
      data: flightDetails
    });

  } catch (error) {
    console.error('Get flight details error:', error);
    res.status(500).json({
      error: 'Flight details retrieval failed',
      message: error.message
    });
  }
});

module.exports = router;
