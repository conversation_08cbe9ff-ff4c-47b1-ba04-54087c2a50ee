import React, { useState } from 'react';
import PassengerDetailsForm from '../components/PassengerDetailsForm';

const TestPassengerForm = () => {
  const [passengers, setPassengers] = useState([{ id: 1, firstName: '', lastName: '' }]);
  const [email, setEmail] = useState('');

  const handleSubmit = (formData) => {
    console.log('Form submitted:', formData);
    alert('Form submitted! Check console for data.');
  };

  const handlePassengersChange = (updatedPassengers) => {
    console.log('Passengers updated:', updatedPassengers);
    setPassengers(updatedPassengers);
  };

  const handleEmailChange = (updatedEmail) => {
    console.log('Email updated:', updatedEmail);
    setEmail(updatedEmail);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold mb-6 text-center">
            Test Passenger Form - Empty Fields
          </h1>
          
          <div className="mb-4 p-4 bg-blue-50 rounded-lg">
            <h2 className="font-semibold text-blue-800 mb-2">Test Instructions:</h2>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Email field should be empty and editable</li>
              <li>• First Name field should be empty and editable</li>
              <li>• Last Name field should be empty and editable</li>
              <li>• Add Passenger button should work (max 2 passengers)</li>
              <li>• Remove buttons should work (min 1 passenger)</li>
              <li>• No pre-filled test data should appear</li>
            </ul>
          </div>

          <div className="mb-4 p-4 bg-green-50 rounded-lg">
            <h2 className="font-semibold text-green-800 mb-2">Current State:</h2>
            <p className="text-sm text-green-700">
              <strong>Passengers:</strong> {passengers.length} |
              <strong> Email:</strong> "{email}" |
              <strong> Check console for updates</strong>
            </p>
          </div>

          <PassengerDetailsForm
            initialPassengers={passengers}
            initialEmail={email}
            onSubmit={handleSubmit}
            onPassengersChange={handlePassengersChange}
            onEmailChange={handleEmailChange}
            submitButtonText="Test Submit"
          />
        </div>
      </div>
    </div>
  );
};

export default TestPassengerForm;
