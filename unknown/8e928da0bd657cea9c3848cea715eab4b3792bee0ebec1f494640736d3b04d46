const fetch = require('node-fetch');

/**
 * Test if airline logo URLs are accessible and return valid images
 */
async function testLogoUrls() {
  console.log('🔗 Testing Airline Logo URL Accessibility...\n');

  const testUrls = [
    'https://www.gstatic.com/flights/airline_logos/70px/CX.png', // Cathay Pacific
    'https://www.gstatic.com/flights/airline_logos/70px/BA.png', // British Airways
    'https://www.gstatic.com/flights/airline_logos/70px/LH.png', // Lufthansa
    'https://www.gstatic.com/flights/airline_logos/70px/EK.png', // Emirates
    'https://www.gstatic.com/flights/airline_logos/70px/SQ.png', // Singapore Airlines
    'https://www.gstatic.com/flights/airline_logos/70px/AF.png', // Air France
  ];

  for (const url of testUrls) {
    const airlineCode = url.match(/\/([A-Z0-9]+)\.png$/)[1];
    console.log(`📋 Testing ${airlineCode}: ${url}`);
    
    try {
      const response = await fetch(url);
      
      if (response.ok) {
        const contentType = response.headers.get('content-type');
        const contentLength = response.headers.get('content-length');
        
        console.log(`  ✅ Status: ${response.status} ${response.statusText}`);
        console.log(`  📄 Content-Type: ${contentType}`);
        console.log(`  📏 Content-Length: ${contentLength} bytes`);
        
        // Check if it's actually an image
        if (contentType && contentType.startsWith('image/')) {
          console.log(`  🎯 Valid image format detected`);
        } else {
          console.log(`  ⚠️  Unexpected content type - may not be an image`);
        }
        
        // Test if we can convert to base64 (like the service does)
        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        const base64 = buffer.toString('base64');
        
        console.log(`  🔄 Base64 conversion: ${base64.length} characters`);
        console.log(`  📊 First 50 chars: ${base64.substring(0, 50)}...`);
        
      } else {
        console.log(`  ❌ HTTP Error: ${response.status} ${response.statusText}`);
      }
      
    } catch (error) {
      console.log(`  ❌ Network Error: ${error.message}`);
    }
    
    console.log(''); // Empty line for readability
  }

  console.log('🏁 Logo URL testing completed!');
}

// Run test if this file is executed directly
if (require.main === module) {
  testLogoUrls().catch(console.error);
}

module.exports = { testLogoUrls };
