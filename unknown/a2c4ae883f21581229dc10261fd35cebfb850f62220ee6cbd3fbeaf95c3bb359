const airlineLogoService = require('./services/airlineLogoService');
const pdfService = require('./services/pdfService');

function auditAirlineCoverage() {
  console.log('🔍 Auditing Airline Logo Coverage...\n');

  // Get all airline codes from PDF service
  const pdfServiceCodes = {
    // European Airlines
    'RYANAIR': 'FR',
    'RYANAIR UK': 'FR',
    'EASYJET': 'U2',
    'JET2': 'LS',
    'VUELING': 'VY',
    'AEGEAN AIRLINES': 'A3',
    'BRITISH AIRWAYS': 'BA',
    'LUFTHANSA': 'LH',
    'AIR FRANCE': 'AF',
    'KLM': 'KL',
    'SWISS': 'LX',
    'WIZZ AIR': 'W6',
    'NORWEGIAN': 'DY',
    'FLYBE': 'BE',
    'TWINJET': 'T7',
    'IBERIA': 'IB',
    'TAP AIR PORTUGAL': 'TP',
    'ALITALIA': 'AZ',
    'AEROFLOT': 'SU',
    'FINNAIR': 'AY',

    // Asian Airlines
    'CATHAY PACIFIC': 'CX',
    'JAPAN AIRLINES': 'JL',
    'SINGAPORE AIRLINES': 'SQ',
    'KOREAN AIR': 'KE',
    'THAI AIRWAYS': 'TG',
    'MALAYSIA AIRLINES': 'MH',
    'PHILIPPINE AIRLINES': 'PR',
    'CHINA AIRLINES': 'CI',
    'CHINA EASTERN': 'MU',
    'CHINA SOUTHERN': 'CZ',
    'ASIANA AIRLINES': 'OZ',
    'VIETNAM AIRLINES': 'VN',
    'GARUDA INDONESIA': 'GA',
    'CEBU PACIFIC': '5J',
    'JETSTAR ASIA': '3K',

    // Middle Eastern Airlines
    'TURKISH AIRLINES': 'TK',
    'EMIRATES': 'EK',
    'QATAR AIRWAYS': 'QR',
    'ETIHAD AIRWAYS': 'EY',
    'SAUDIA': 'SV',
    'GULF AIR': 'GF',
    'OMAN AIR': 'WY',
    'FLYDUBAI': 'FZ',
    'AIR ARABIA': 'G9',

    // African Airlines
    'ETHIOPIAN AIRLINES': 'ET',
    'SOUTH AFRICAN AIRWAYS': 'SA',
    'KENYA AIRWAYS': 'KQ',
    'EGYPTAIR': 'MS',
    'ROYAL AIR MAROC': 'AT',
    'TUNISAIR': 'TU',
    'AIR ALGERIE': 'AH',
    'RWANDAIR': 'WB',
    'FASTJET': 'FN',

    // South American Airlines
    'LATAM': 'LA',
    'AVIANCA': 'AV',
    'COPA AIRLINES': 'CM',
    'GOL': 'G3',
    'AZUL': 'AD',
    'SKY AIRLINE': 'H2',
    'VIVA AIR': 'VV',

    // Oceania Airlines
    'QANTAS': 'QF',
    'JETSTAR': 'JQ',
    'AIR NEW ZEALAND': 'NZ',
    'VIRGIN AUSTRALIA': 'VA',
    'TIGERAIR': 'TT',

    // North American Airlines
    'AMERICAN AIRLINES': 'AA',
    'DELTA AIR LINES': 'DL',
    'UNITED AIRLINES': 'UA'
  };

  const missingLogos = [];
  const presentLogos = [];
  const duplicateEntries = [];

  console.log('📊 Checking logo coverage for all airlines...\n');

  for (const [airlineName, airlineCode] of Object.entries(pdfServiceCodes)) {
    const logoSources = airlineLogoService.getLogoSources(airlineCode);
    
    if (logoSources) {
      presentLogos.push({ name: airlineName, code: airlineCode, sources: logoSources.sources.length });
    } else {
      missingLogos.push({ name: airlineName, code: airlineCode });
    }
  }

  // Check for duplicates in logo service
  const logoServiceCodes = new Set();
  const logoDatabase = airlineLogoService.getLogoSources('TEST') || {}; // This will return null, but we need to check the actual database

  console.log('✅ Airlines with Logo Support:');
  console.log(`   Found ${presentLogos.length} airlines with logo support:`);
  presentLogos.forEach(airline => {
    console.log(`   ✓ ${airline.name} (${airline.code}) - ${airline.sources} sources`);
  });

  console.log('\n❌ Airlines Missing Logo Support:');
  console.log(`   Found ${missingLogos.length} airlines missing logo support:`);
  missingLogos.forEach(airline => {
    console.log(`   ✗ ${airline.name} (${airline.code})`);
  });

  console.log('\n📈 Coverage Statistics:');
  console.log(`   Total airlines in PDF service: ${Object.keys(pdfServiceCodes).length}`);
  console.log(`   Airlines with logos: ${presentLogos.length}`);
  console.log(`   Airlines missing logos: ${missingLogos.length}`);
  console.log(`   Coverage percentage: ${((presentLogos.length / Object.keys(pdfServiceCodes).length) * 100).toFixed(1)}%`);

  console.log('\n🎯 Priority Airlines to Add:');
  const priorityAirlines = missingLogos.filter(airline => 
    ['BE', 'T7', 'TP', 'AZ', 'SU', 'AY', 'JL', 'CX', 'FN', 'H2', 'VV', 'NZ', 'VA', 'TT'].includes(airline.code)
  );
  
  priorityAirlines.forEach(airline => {
    console.log(`   🔥 ${airline.name} (${airline.code}) - Major international carrier`);
  });

  return { presentLogos, missingLogos, priorityAirlines };
}

// Run the audit
auditAirlineCoverage();
