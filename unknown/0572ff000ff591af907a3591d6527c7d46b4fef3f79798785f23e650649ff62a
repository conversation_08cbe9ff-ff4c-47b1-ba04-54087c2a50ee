import React from 'react';
import { useNavigate } from 'react-router-dom';
import PassengerDetails from '../components/PassengerDetails';

const PassengerDetailsTest = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-gray-50 py-8 px-4">
      <div className="max-w-5xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Fresh PassengerDetails Component
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Completely rebuilt from scratch with clean state management, proper validation, 
            and professional styling. All fields start empty and work correctly.
          </p>
        </div>

        {/* Features List */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">✨ Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">All fields empty by default</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Email validation</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Add/Remove passengers</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Maximum 2 passengers</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Real-time validation</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Mobile responsive</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Professional styling</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Context integration</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700">Checkout navigation</span>
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8">
          <h2 className="text-lg font-semibold text-blue-900 mb-3">🧪 Test Instructions</h2>
          <ol className="list-decimal list-inside space-y-2 text-blue-800">
            <li>Verify all fields are completely empty on page load</li>
            <li>Type in the email field - should be fully editable</li>
            <li>Type in First Name and Last Name - should be fully editable</li>
            <li>Click "Add Passenger" - should instantly add Passenger 2</li>
            <li>Continue adding passengers up to 5 - button should disable</li>
            <li>Click "Remove" on any passenger except Passenger 1</li>
            <li>Try submitting with empty fields - should show validation errors</li>
            <li>Fill all fields correctly and submit - should navigate to checkout</li>
            <li>Check browser console for data logging</li>
          </ol>
        </div>

        {/* State Management Info */}
        <div className="bg-green-50 border border-green-200 rounded-xl p-6 mb-8">
          <h2 className="text-lg font-semibold text-green-900 mb-3">🔧 Implementation Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-green-800 mb-2">State Management:</h3>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• useState for email input</li>
                <li>• useState for passenger array</li>
                <li>• useState for validation errors</li>
                <li>• useBooking context integration</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-green-800 mb-2">Data Flow:</h3>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Real-time input updates</li>
                <li>• Instant validation feedback</li>
                <li>• Context state updates</li>
                <li>• Navigation to /checkout</li>
              </ul>
            </div>
          </div>
        </div>

        {/* The Fresh Component */}
        <PassengerDetails />

        {/* Navigation */}
        <div className="mt-8 text-center space-x-4">
          <button
            onClick={() => navigate('/passenger-form-comparison')}
            className="text-blue-600 hover:text-blue-800 underline"
          >
            Compare with Old Component
          </button>
          <button
            onClick={() => navigate('/')}
            className="text-gray-600 hover:text-gray-800 underline"
          >
            Back to Homepage
          </button>
        </div>
      </div>
    </div>
  );
};

export default PassengerDetailsTest;
