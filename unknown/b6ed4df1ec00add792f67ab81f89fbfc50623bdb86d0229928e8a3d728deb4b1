const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;
const airlineLogoService = require('./airlineLogoService');

/**
 * PDF Service for VerifiedOnward.com
 * Generates professional airline-style flight reservation PDFs using Puppeteer
 */
class PDFService {
  constructor() {
    this.browser = null;
    this.isInitialized = false;
  }

  /**
   * Initialize Puppeteer browser instance
   */
  async initialize() {
    if (this.isInitialized && this.browser) {
      return;
    }

    try {
      console.log('🚀 Initializing PDF service with Puppeteer...');

      this.browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      this.isInitialized = true;
      console.log('✅ PDF service initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize PDF service:', error);
      throw error;
    }
  }



  /**
   * Get airline logo path
   */
  getAirlineLogo(airlineName) {
    const logoMap = {
      'CATHAY PACIFIC': '/assets/airline-logos/cathay-pacific.png',
      'BRITISH AIRWAYS': '/assets/airline-logos/british-airways.png',
      'JAPAN AIRLINES': '/assets/airline-logos/japan-airlines.png',
      'EMIRATES': '/assets/airline-logos/emirates.png',
      'LUFTHANSA': '/assets/airline-logos/lufthansa.png',
      'SINGAPORE AIRLINES': '/assets/airline-logos/singapore-airlines.png'
    };

    return logoMap[airlineName?.toUpperCase()] || null;
  }

  /**
   * Get airline IATA code from airline name
   */
  getAirlineCode(airlineName) {
    const codeMap = {
      // European Airlines
      'RYANAIR': 'FR',
      'RYANAIR UK': 'FR',
      'EASYJET': 'U2',
      'JET2': 'LS',
      'VUELING': 'VY',
      'AEGEAN AIRLINES': 'A3',
      'BRITISH AIRWAYS': 'BA',
      'LUFTHANSA': 'LH',
      'AIR FRANCE': 'AF',
      'KLM': 'KL',
      'SWISS': 'LX',
      'WIZZ AIR': 'W6',
      'NORWEGIAN': 'DY',
      'FLYBE': 'BE',
      'TWINJET': 'T7',
      'IBERIA': 'IB',
      'TAP AIR PORTUGAL': 'TP',
      'ALITALIA': 'AZ',
      'AEROFLOT': 'SU',
      'FINNAIR': 'AY',

      // Asian Airlines
      'SINGAPORE AIRLINES': 'SQ',
      'CATHAY PACIFIC': 'CX',
      'JAPAN AIRLINES': 'JL',
      'KOREAN AIR': 'KE',
      'THAI': 'TG',
      'THAI AIRWAYS': 'TG',
      'MALAYSIA AIRLINES': 'MH',
      'PHILIPPINE AIRLINES': 'PR',
      'CHINA AIRLINES': 'CI',
      'CHINA EASTERN': 'MU',
      'CHINA SOUTHERN': 'CZ',
      'ASIANA AIRLINES': 'OZ',
      'VIETNAM AIRLINES': 'VN',
      'GARUDA INDONESIA': 'GA',
      'CEBU PACIFIC': '5J',
      'JETSTAR ASIA': '3K',

      // Middle Eastern Airlines
      'TURKISH AIRLINES': 'TK',
      'EMIRATES': 'EK',
      'QATAR AIRWAYS': 'QR',
      'ETIHAD AIRWAYS': 'EY',
      'SAUDIA': 'SV',
      'GULF AIR': 'GF',
      'OMAN AIR': 'WY',
      'FLYDUBAI': 'FZ',
      'AIR ARABIA': 'G9',

      // African Airlines
      'ETHIOPIAN AIRLINES': 'ET',
      'SOUTH AFRICAN AIRWAYS': 'SA',
      'KENYA AIRWAYS': 'KQ',
      'EGYPTAIR': 'MS',
      'ROYAL AIR MAROC': 'AT',
      'TUNISAIR': 'TU',
      'AIR ALGERIE': 'AH',
      'RWANDAIR': 'WB',
      'FASTJET': 'FN',

      // South American Airlines
      'LATAM': 'LA',
      'AVIANCA': 'AV',
      'COPA AIRLINES': 'CM',
      'GOL': 'G3',
      'AZUL': 'AD',
      'SKY AIRLINE': 'H2',
      'VIVA AIR': 'VV',

      // Oceania Airlines
      'QANTAS': 'QF',
      'JETSTAR': 'JQ',
      'AIR NEW ZEALAND': 'NZ',
      'VIRGIN AUSTRALIA': 'VA',
      'TIGERAIR': 'TT',

      // North American Airlines
      'AMERICAN AIRLINES': 'AA',
      'DELTA AIR LINES': 'DL',
      'UNITED AIRLINES': 'UA'
    };
    return codeMap[airlineName?.toUpperCase()] || 'XX';
  }

  /**
   * Generate HTML template for flight ticket
   */
  async generateTicketHTML(ticketData) {
    const {
      tripDates,
      destination,
      passengers = [],
      reservationCode,
      airlineReservationCode,
      segments = [],
      showNotice = false,
      customNotice = "This is not a valid boarding pass. Please check with the airline before departure."
    } = ticketData;

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight Reservation - ${reservationCode}</title>
    <style>
        /* Authentic airline reservation system typography */
        body {
            font-family: Arial, Helvetica, sans-serif;
            margin: 0;
            padding: 12px;
            background: white;
            color: #000;
            line-height: 1.1;
            font-size: 11pt;
        }

        /* Authentic airline font hierarchy - ALL CAPS and bold */
        .header-large {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 14pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .header-medium {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 12pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
        }
        .subheader {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 11pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
        }
        .body-text {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 10pt;
            color: #000;
            font-weight: normal;
        }
        .small-text {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 9pt;
            color: #000;
        }
        .tiny-text {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 8pt;
            color: #000;
        }

        /* Layout structure - tighter spacing */
        .ticket-container {
            max-width: 750px;
            margin: 0 auto;
            background: white;
        }

        /* Sharp rectangular flight segment boxes - authentic airline style */
        .flight-segment {
            border: 1px solid #cccccc;
            margin: 5px 0;
            background: white;
            display: table;
            width: 100%;
            border-collapse: separate;
            border-bottom: 1px solid #cccccc;
            margin-bottom: 5px;
        }

        /* Left-side airline branding panel - more prominent */
        .airline-branding-panel {
            display: table-cell;
            width: 160px;
            background: #f8f8f8;
            border-right: 1px solid #cccccc;
            padding: 2px 6px;
            vertical-align: middle;
            text-align: left;
        }

        .airline-branding-panel .airline-name-block {
            text-align: center;
            margin-bottom: 8px;
        }

        .airline-branding-panel .airline-name {
            font-size: 12pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
            margin-bottom: 2px;
            line-height: 1.0;
        }

        .airline-branding-panel .flight-number {
            font-size: 12pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
            line-height: 1.0;
        }

        .airline-branding-panel .flight-details {
            margin-top: 8px;
            font-size: 9pt;
            color: #000;
        }

        /* Flight details grid - authentic airline system layout */
        .flight-details-panel {
            display: table-cell;
            padding: 0;
            vertical-align: top;
        }

        .flight-info-grid {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .flight-info-grid, .flight-info-grid th, .flight-info-grid td {
            border: 1px solid #cccccc;
            border-collapse: collapse;
        }

        .flight-info-grid td {
            font-family: Arial, Helvetica, sans-serif;
            padding: 2px 6px;
            vertical-align: middle;
            font-size: 13px;
            font-weight: 400;
            color: #000;
        }

        .flight-info-grid .info-label {
            font-family: Arial, Helvetica, sans-serif;
            background: #f8f8f8;
            font-weight: 500;
            color: #000;
            text-transform: uppercase;
            font-size: 13px;
            width: 25%;
        }

        .flight-info-grid .info-value {
            background: white;
            color: #000;
            font-weight: 400;
        }

        .flight-info-grid .time-large {
            font-size: 14pt;
            font-weight: bold;
            color: #000;
        }

        /* Additional spacing and density adjustments */
        .flight-info-grid th {
            font-family: Arial, Helvetica, sans-serif;
            padding: 2px 6px;
            background: #f8f8f8;
            font-weight: 500;
            color: #000;
            text-transform: uppercase;
            font-size: 13px;
            vertical-align: middle;
        }

        /* Ensure consistent table cell heights */
        .flight-info-grid td,
        .flight-info-grid th {
            height: 24px;
            line-height: 1.2;
        }

        /* Remove any rounded corners or modern styling */
        * {
            border-radius: 0 !important;
        }

        /* Monochrome status styling - no colors */
        .status-confirmed {
            color: #000;
            font-weight: normal;
            background: white;
            padding: 0;
            text-transform: uppercase;
            font-size: 13px;
        }

        /* Tiny disclaimer text at bottom */
        .disclaimer-text {
            font-size: 7pt;
            color: #666;
            text-align: center;
            margin-top: 20px;
            line-height: 1.2;
        }

        /* Route summary - authentic airline style */
        .route-summary {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 11pt;
            font-weight: 500;
            color: #000;
            margin: 6px 0 5px 0;
            padding: 2px 6px;
            background: #f8f8f8;
            border: 1px solid #cccccc;
            border-left: 1px solid #cccccc;
            text-transform: uppercase;
        }

        /* Departure header - stark official style */
        .departure-header {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 12pt;
            font-weight: 500;
            color: #000;
            margin: 6px 0 5px 0;
            padding: 2px 6px;
            background: #f8f8f8;
            border-bottom: 1px solid #cccccc;
            text-transform: uppercase;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .departure-header .left-content {
            display: flex;
            align-items: center;
        }

        .departure-header .airplane {
            font-size: 12pt;
            font-weight: normal;
            color: #000;
            margin-right: 8px;
        }

        .departure-header .verify-text {
            font-size: 9pt;
            font-weight: normal;
            color: #888888;
            text-transform: none;
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
        }



        @media print {
            body { margin: 0; padding: 15px; }
            .ticket-container { max-width: none; }
        }
    </style>
</head>
<body>
    <div class="ticket-container">
        <!-- Header - Authentic airline style -->
        <div class="header-large" style="text-align: left; margin-bottom: 12px; padding-bottom: 6px; border-bottom: 1px solid #cccccc;">
            ${tripDates} &nbsp;&nbsp;&nbsp; ${destination}
        </div>

        <!-- Prepared For Section -->
        <div style="margin: 10px 0;">
            <div class="subheader" style="margin-bottom: 6px;">PREPARED FOR</div>
            ${passengers.map(passenger => `
                <div class="subheader" style="margin: 2px 0;">${passenger.name}</div>
            `).join('')}
        </div>

        <!-- Reservation Codes -->
        <div style="margin: 10px 0;">
            <div class="body-text" style="margin: 2px 0;"><strong>STATUS:</strong> <span style="font-weight: normal; text-transform: uppercase;">CONFIRMED</span></div>
            <div class="body-text" style="margin: 2px 0;"><strong>RESERVATION CODE:</strong> ${reservationCode}</div>
            <div class="body-text" style="margin: 2px 0;"><strong>AIRLINE RESERVATION CODE:</strong> ${airlineReservationCode}</div>
        </div>

        <!-- Flight Segments -->
        ${(await Promise.all(segments.map(async (segment, index) => {
            // Get airline code from airline name
            const airlineCode = this.getAirlineCode(segment.airline);
            const airlineLogo = await airlineLogoService.getLogoForPDF(airlineCode, segment.airline);

            // Format stops information for route summary
            const formatStopsForSummary = (stops, layovers) => {
                const stopCount = stops || 0;
                let stopsText = `Stop(s): ${stopCount}`;

                // Add layover details if available
                if (layovers && layovers.length > 0) {
                    const layoverCodes = layovers.map(l => l.airport).join(', ');
                    stopsText += ` (via ${layoverCodes})`;
                }

                return stopsText;
            };

            return `
            <!-- Route Summary -->
            <div class="route-summary">
                ROUTE ${segment.from?.code || 'XXX'} – ${segment.to?.code || 'XXX'} &nbsp;&nbsp;&nbsp; Duration: ${segment.duration || 'N/A'} &nbsp;&nbsp;&nbsp; ${formatStopsForSummary(segment.stops, segment.layovers)}
            </div>

            <!-- Departure Header -->
            <div class="departure-header">
                <div class="left-content">
                    <span class="airplane">✈</span> ${index === 0 ? 'DEPARTURE' : 'RETURN'}: ${segment.departureDay || 'DATE NOT SPECIFIED'}
                </div>
                <div class="verify-text">
                    Please verify flight times prior to departure
                </div>
            </div>

            <!-- Flight Segment Box with Left-Side Airline Branding -->
            <div class="flight-segment">
                <!-- Left-Side Airline Branding Panel -->
                <div class="airline-branding-panel">
                    <div style="text-align: center; margin-bottom: 6px;">
                        <img src="${airlineLogo}" alt="${segment.airline}" style="width: 70px; height: 45px; display: block; margin: 0 auto 2px auto;" />
                    </div>
                    <div class="airline-name-block">
                        <div class="airline-name">${segment.airline || 'AIRLINE'}</div>
                        <div class="flight-number">${segment.flightNo || 'FLIGHT'}</div>
                    </div>
                    <div class="flight-details">
                        <div>DURATION: ${segment.duration || 'N/A'}</div>
                        <div>CLASS: ${segment.flightClass || 'Economy Class (M)'}</div>
                        <div>STATUS: <span style="font-weight: normal; text-transform: uppercase;">CONFIRMED</span></div>
                    </div>
                </div>

                <!-- Flight Details Panel -->
                <div class="flight-details-panel">
                    <!-- Airport and Time Information Grid -->
                    <table class="flight-info-grid">
                        <tr>
                            <td class="info-label">${segment.from?.code || 'XXX'}</td>
                            <td class="info-value"><strong>${segment.from?.city || 'Departure City'}</strong></td>
                            <td class="info-label">${segment.to?.code || 'XXX'}</td>
                            <td class="info-value"><strong>${segment.to?.city || 'Arrival City'}</strong></td>
                        </tr>
                        <tr>
                            <td class="info-label">DEPARTING AT:</td>
                            <td class="info-value">
                                <div class="time-large">${segment.from?.time || 'TIME'}</div>
                                <div class="small-text">TERMINAL: ${segment.from?.terminal || '1'}</div>
                            </td>
                            <td class="info-label">ARRIVING AT:</td>
                            <td class="info-value">
                                <div class="time-large">${segment.to?.time || 'TIME'}</div>
                                <div class="small-text">TERMINAL: ${segment.to?.terminal || '1'}</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="info-label">AIRCRAFT:</td>
                            <td class="info-value">${segment.aircraft || 'BOEING 737-800'}</td>
                            <td class="info-label">DISTANCE (IN MILES):</td>
                            <td class="info-value">${segment.distance || 'Not Available'}</td>
                        </tr>
                        <tr>
                            <td class="info-label">STOP(S):</td>
                            <td class="info-value">${this.formatStopsForTable(segment.stops, segment.layovers)}</td>
                            <td class="info-label">MEALS:</td>
                            <td class="info-value">${segment.meals || 'Not Available'}</td>
                        </tr>
                    </table>

                    <!-- Passenger Information -->
                    <div style="margin-top: 6px; border-top: 1px solid #cccccc; padding-top: 6px;">
                        <table class="flight-info-grid">
                            <thead>
                                <tr style="background: #f8f8f8;">
                                    <th class="info-label" style="font-weight: 500;">PASSENGER NAME:</th>
                                    <th class="info-label" style="font-weight: 500;">SEATS:</th>
                                    <th class="info-label" style="font-weight: 500;">BOOKING:</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${passengers.map(passenger => `
                                    <tr>
                                        <td class="info-value"><strong>${passenger.name}</strong></td>
                                        <td class="info-value">Check-in required</td>
                                        <td class="info-value"><span class="status-confirmed">CONFIRMED</span></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            `;
        }))).join('')}

        <!-- Tiny disclaimer text at bottom -->
        ${showNotice ? `
        <div class="disclaimer-text">
            ${customNotice}
        </div>
        ` : ''}


    </div>
</body>
</html>`;
  }

  /**
   * Generate PDF from ticket data
   */
  async generatePDF(ticketData, outputPath) {
    try {
      await this.initialize();

      console.log('📄 Generating PDF for reservation:', ticketData.reservationCode);

      const page = await this.browser.newPage();

      // Set page size and margins for professional look
      await page.setViewport({ width: 1200, height: 1600 });

      const html = await this.generateTicketHTML(ticketData);
      await page.setContent(html, { waitUntil: 'networkidle0' });

      // Generate PDF with professional settings
      const pdfBuffer = await page.pdf({
        path: outputPath,
        format: 'A4',
        margin: {
          top: '20px',
          right: '20px',
          bottom: '20px',
          left: '20px'
        },
        printBackground: true,
        preferCSSPageSize: false
      });

      await page.close();
      
      console.log('✅ PDF generated successfully:', outputPath);
      return pdfBuffer;

    } catch (error) {
      console.error('❌ PDF generation failed:', error);
      throw error;
    }
  }

  /**
   * Format stops information for flight details table
   */
  formatStopsForTable(stops, layovers) {
    const stopCount = stops || 0;

    if (stopCount === 0) {
      return '0';
    }

    // If we have layover details, show them
    if (layovers && layovers.length > 0) {
      const layoverInfo = layovers.map(layover => {
        const airport = layover.airport || 'LAY';
        const duration = layover.duration ? ` (${layover.duration})` : '';
        return `${airport}${duration}`;
      }).join(', ');

      return `${stopCount} (via ${layoverInfo})`;
    }

    // Otherwise just show the count
    return stopCount.toString();
  }

  /**
   * Clean up resources
   */
  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.isInitialized = false;
      console.log('🧹 PDF service cleaned up');
    }
  }
}

module.exports = new PDFService();
