// Performance optimization utilities for VerifiedOnward

/**
 * Debounce function to limit the rate of function calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @param {boolean} immediate - Execute immediately on first call
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait, immediate = false) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
};

/**
 * Throttle function to limit function execution frequency
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} Throttled function
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Lazy load images with intersection observer
 * @param {string} selector - CSS selector for images to lazy load
 */
export const lazyLoadImages = (selector = 'img[data-src]') => {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    });

    document.querySelectorAll(selector).forEach(img => {
      imageObserver.observe(img);
    });
  }
};

/**
 * Preload critical resources
 * @param {Array} resources - Array of resource URLs to preload
 */
export const preloadResources = (resources) => {
  resources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource.url;
    link.as = resource.type || 'fetch';
    if (resource.crossorigin) link.crossOrigin = resource.crossorigin;
    document.head.appendChild(link);
  });
};

/**
 * Check if user prefers reduced motion
 * @returns {boolean} True if user prefers reduced motion
 */
export const prefersReducedMotion = () => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

/**
 * Get device performance tier based on hardware capabilities
 * @returns {string} 'high', 'medium', or 'low'
 */
export const getPerformanceTier = () => {
  // Check for hardware concurrency (CPU cores)
  const cores = navigator.hardwareConcurrency || 1;
  
  // Check for device memory (if available)
  const memory = navigator.deviceMemory || 1;
  
  // Check for connection speed
  const connection = navigator.connection;
  const effectiveType = connection?.effectiveType || '4g';
  
  if (cores >= 8 && memory >= 4 && effectiveType === '4g') {
    return 'high';
  } else if (cores >= 4 && memory >= 2) {
    return 'medium';
  } else {
    return 'low';
  }
};

/**
 * Optimize animations based on device performance
 * @param {string} tier - Performance tier ('high', 'medium', 'low')
 */
export const optimizeAnimations = (tier) => {
  const root = document.documentElement;
  
  switch (tier) {
    case 'low':
      root.style.setProperty('--animation-duration', '0s');
      root.style.setProperty('--transition-duration', '0s');
      break;
    case 'medium':
      root.style.setProperty('--animation-duration', '0.3s');
      root.style.setProperty('--transition-duration', '0.2s');
      break;
    case 'high':
    default:
      root.style.setProperty('--animation-duration', '0.6s');
      root.style.setProperty('--transition-duration', '0.3s');
      break;
  }
};

/**
 * Initialize performance optimizations
 */
export const initPerformanceOptimizations = () => {
  // Get performance tier and optimize accordingly
  const tier = getPerformanceTier();
  optimizeAnimations(tier);
  
  // Lazy load images
  lazyLoadImages();
  
  // Preload critical resources
  preloadResources([
    { url: '/api/airports', type: 'fetch' },
    { url: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap', type: 'style' }
  ]);
  
  // Add performance observer for monitoring
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'largest-contentful-paint') {
          console.log('LCP:', entry.startTime);
        }
        if (entry.entryType === 'first-input') {
          console.log('FID:', entry.processingStart - entry.startTime);
        }
      });
    });
    
    observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input'] });
  }
};

/**
 * Web Vitals monitoring with enterprise-level tracking
 */
export const measureWebVitals = () => {
  // Measure Cumulative Layout Shift (CLS)
  let clsValue = 0;
  let clsEntries = [];

  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (!entry.hadRecentInput) {
        clsValue += entry.value;
        clsEntries.push(entry);
      }
    }
  });

  observer.observe({ type: 'layout-shift', buffered: true });

  // Report CLS when page visibility changes
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      console.log('CLS:', clsValue);
      // Send to analytics in production
      if (process.env.NODE_ENV === 'production') {
        // Analytics integration would go here
      }
    }
  });
};

/**
 * Enterprise-level performance monitoring
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = {
      pageLoadTime: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      firstInputDelay: 0,
      cumulativeLayoutShift: 0,
      timeToInteractive: 0
    };

    this.thresholds = {
      pageLoadTime: 2000, // 2 seconds
      firstContentfulPaint: 1800,
      largestContentfulPaint: 2500,
      firstInputDelay: 100,
      cumulativeLayoutShift: 0.1,
      timeToInteractive: 3800
    };

    this.init();
  }

  init() {
    this.measurePageLoad();
    this.measureCoreWebVitals();
    this.measureResourceTiming();
    this.setupPerformanceObserver();
  }

  measurePageLoad() {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0];
      this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
      this.reportMetric('pageLoadTime', this.metrics.pageLoadTime);
    });
  }

  measureCoreWebVitals() {
    // First Contentful Paint
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          this.metrics.firstContentfulPaint = entry.startTime;
          this.reportMetric('firstContentfulPaint', entry.startTime);
        }
      }
    }).observe({ type: 'paint', buffered: true });

    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.metrics.largestContentfulPaint = lastEntry.startTime;
      this.reportMetric('largestContentfulPaint', lastEntry.startTime);
    }).observe({ type: 'largest-contentful-paint', buffered: true });

    // First Input Delay
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
        this.reportMetric('firstInputDelay', this.metrics.firstInputDelay);
      }
    }).observe({ type: 'first-input', buffered: true });
  }

  measureResourceTiming() {
    const resources = performance.getEntriesByType('resource');
    const slowResources = resources.filter(resource =>
      resource.duration > 1000 // Resources taking more than 1 second
    );

    if (slowResources.length > 0) {
      console.warn('Slow resources detected:', slowResources);
    }
  }

  setupPerformanceObserver() {
    // Monitor long tasks (blocking main thread)
    if ('PerformanceObserver' in window) {
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) { // Tasks longer than 50ms
            console.warn('Long task detected:', entry);
          }
        }
      }).observe({ type: 'longtask', buffered: true });
    }
  }

  reportMetric(name, value) {
    const threshold = this.thresholds[name];
    const status = value <= threshold ? 'good' : 'needs-improvement';

    console.log(`Performance Metric - ${name}: ${value}ms (${status})`);

    // Send to analytics in production
    if (process.env.NODE_ENV === 'production') {
      // Analytics integration would go here
      this.sendToAnalytics(name, value, status);
    }
  }

  sendToAnalytics(metric, value, status) {
    // Placeholder for analytics integration
    // Could integrate with Google Analytics, DataDog, etc.
  }

  getReport() {
    return {
      metrics: this.metrics,
      thresholds: this.thresholds,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Advanced caching strategies
 */
export class CacheManager {
  constructor() {
    this.memoryCache = new Map();
    this.cacheSize = 0;
    this.maxCacheSize = 50 * 1024 * 1024; // 50MB
  }

  set(key, value, ttl = 300000) { // 5 minutes default TTL
    const item = {
      value,
      timestamp: Date.now(),
      ttl,
      size: this.calculateSize(value)
    };

    // Check if adding this item would exceed cache size
    if (this.cacheSize + item.size > this.maxCacheSize) {
      this.evictLRU();
    }

    this.memoryCache.set(key, item);
    this.cacheSize += item.size;
  }

  get(key) {
    const item = this.memoryCache.get(key);

    if (!item) return null;

    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return null;
    }

    // Update access time for LRU
    item.lastAccessed = Date.now();
    return item.value;
  }

  delete(key) {
    const item = this.memoryCache.get(key);
    if (item) {
      this.cacheSize -= item.size;
      this.memoryCache.delete(key);
    }
  }

  evictLRU() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, item] of this.memoryCache) {
      if (item.lastAccessed < oldestTime) {
        oldestTime = item.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.delete(oldestKey);
    }
  }

  calculateSize(obj) {
    return JSON.stringify(obj).length * 2; // Rough estimate
  }

  clear() {
    this.memoryCache.clear();
    this.cacheSize = 0;
  }
}

// Global instances
export const performanceMonitor = new PerformanceMonitor();
export const cacheManager = new CacheManager();
