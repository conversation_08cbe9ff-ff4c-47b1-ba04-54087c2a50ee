import React, { useState } from 'react';

/**
 * DocumentDownloadButton Component
 *
 * Handles document generation and download for flight tickets
 * Integrates with the backend document generation service
 *
 * @param {Object} props - Component props
 * @param {Object} props.ticketData - Flight ticket data for document generation
 * @param {string} props.buttonText - Text to display on the button
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.disabled - Whether the button is disabled
 */
const DocumentDownloadButton = ({
  ticketData,
  buttonText = "Instant Download",
  className = "",
  disabled = false
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState(null);

  const handleDownloadDocument = async () => {
    if (!ticketData || disabled) return;

    setIsGenerating(true);
    setError(null);

    try {
      console.log('🔄 Generating PDF...', ticketData);

      const response = await fetch('/api/tickets/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(ticketData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate PDF');
      }

      // Get the document blob
      const documentBlob = await response.blob();

      // Create download link
      const url = window.URL.createObjectURL(documentBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `flight-reservation-${ticketData.reservationCode || 'ticket'}.pdf`;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      window.URL.revokeObjectURL(url);

      console.log('✅ Document downloaded successfully');

    } catch (error) {
      console.error('❌ Document generation failed:', error);
      setError(error.message);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex flex-col items-center gap-2">
      <button
        onClick={handleDownloadDocument}
        disabled={disabled || isGenerating || !ticketData}
        className={`
          px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg
          hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed
          transition-colors duration-200 flex items-center gap-2
          ${className}
        `}
      >
        {isGenerating ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
            Generating Document...
          </>
        ) : (
          <>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            {buttonText}
          </>
        )}
      </button>
      
      {error && (
        <div className="text-red-600 text-sm text-center max-w-md">
          Error: {error}
        </div>
      )}
    </div>
  );
};

/**
 * PrintButton Component
 * 
 * Handles printing of the flight ticket using browser print functionality
 * 
 * @param {Object} props - Component props
 * @param {string} props.buttonText - Text to display on the button
 * @param {string} props.className - Additional CSS classes
 */
export const PrintButton = ({ 
  buttonText = "Print Ticket", 
  className = "" 
}) => {
  const handlePrint = () => {
    window.print();
  };

  return (
    <button
      onClick={handlePrint}
      className={`
        px-6 py-3 bg-green-600 text-white font-semibold rounded-lg
        hover:bg-green-700 transition-colors duration-200 flex items-center gap-2
        ${className}
      `}
    >
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
      </svg>
      {buttonText}
    </button>
  );
};

export default DocumentDownloadButton;
