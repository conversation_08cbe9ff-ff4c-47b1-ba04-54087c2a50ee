const axios = require('axios');

async function quickDebugTest() {
  console.log('🔍 Quick debug test...');
  
  const testData = {
    bookingReference: 'QUICKTEST',
    passengers: [{ firstName: '<PERSON>', lastName: '<PERSON>' }],
    outboundFlight: {
      airline: 'RYANAIR',
      flightNumber: 'FR 1234',
      departure: { code: 'LHR', city: 'London', time: '2025-07-20T10:30:00Z', terminal: '2' },
      arrival: { code: 'BCN', city: 'Barcelona', time: '2025-07-20T13:45:00Z', terminal: '1' },
      duration: '2h 15m'
    },
    totalPrice: 9.98
  };

  try {
    const response = await axios.post('http://localhost:5001/api/tickets/generate', testData);
    console.log('✅ Request completed, check backend logs');
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

quickDebugTest().catch(console.error);
