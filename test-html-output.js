const ticketService = require('./backend/services/ticketService');
const fs = require('fs');

async function testHTMLOutput() {
  console.log('🧪 Testing HTML output to verify format...');
  
  // Sample ticket data matching the exact format we want
  const ticketData = {
    flightData: {
      trips: [{
        legs: [{
          airline: {
            code: 'CX',
            name: 'CATHAY PACIFIC'
          },
          flightNumber: 'CX 784',
          departure: {
            airport: 'DPS',
            city: 'Denpasar-Bali',
            country: 'Indonesia',
            datetime: '2021-07-18T16:05:00Z',
            terminal: '1'
          },
          arrival: {
            airport: 'HKG',
            city: 'Hong Kong',
            country: 'Hong Kong',
            datetime: '2021-07-18T21:05:00Z',
            terminal: '1'
          },
          duration: '05hr(s) 00min(s)',
          aircraft: 'AIRBUS INDUSTRIE A330-300'
        }]
      }, {
        legs: [{
          airline: {
            code: 'CX',
            name: 'CATHAY PACIFIC'
          },
          flightNumber: 'CX 844',
          departure: {
            airport: 'HKG',
            city: 'Hong Kong',
            country: 'Hong Kong',
            datetime: '2021-07-19T02:05:00Z',
            terminal: '1'
          },
          arrival: {
            airport: 'JFK',
            city: 'New York',
            country: 'United States Of America',
            datetime: '2021-07-19T06:00:00Z',
            terminal: '8'
          },
          duration: '15hr(s) 55min(s)',
          aircraft: 'BOEING 777-300ER'
        }]
      }]
    },
    passengerData: [
      { firstName: 'JANE', lastName: 'COOPER' },
      { firstName: 'JENNY', lastName: 'WILSON' }
    ],
    bookingReference: 'NHG8IQ',
    email: '<EMAIL>'
  };

  try {
    const htmlContent = await ticketService.generateTicketHTML(ticketData);
    
    // Save HTML for inspection
    fs.writeFileSync('test-output.html', htmlContent);
    console.log('✅ HTML generated and saved to test-output.html');
    
    // Check for key elements that should match the target format
    const checks = [
      { name: 'Trip Header Format', regex: /18 JUL 2021 • 19 JUL 2021 TRIP TO/ },
      { name: 'Prepared For Section', regex: /PREPARED FOR/ },
      { name: 'Passenger Names', regex: /COOPER\/JANE MR\./ },
      { name: 'Reservation Codes', regex: /RESERVATION CODE: NHG8IQ/ },
      { name: 'Airline Reservation Code', regex: /AIRLINE RESERVATION CODE: NHG8IQ/ },
      { name: 'Departure Header', regex: /DEPARTURE: SUNDAY 18 JUL/ },
      { name: 'Cathay Pacific', regex: /CATHAY PACIFIC/ },
      { name: 'Flight Numbers', regex: /CX 784/ },
      { name: 'Aircraft Info', regex: /AIRBUS INDUSTRIE A330-300/ },
      { name: 'Passenger Table', regex: /<table class="passenger-table">/ },
      { name: 'Check-in Required', regex: /Check-in required/ }
    ];
    
    console.log('\n🔍 Format Verification:');
    checks.forEach(check => {
      const found = check.regex.test(htmlContent);
      console.log(`${found ? '✅' : '❌'} ${check.name}: ${found ? 'Found' : 'Missing'}`);
    });
    
    // Open HTML file for visual inspection
    const { exec } = require('child_process');
    exec('open test-output.html', (error) => {
      if (error) {
        console.log('Could not open HTML file automatically');
      } else {
        console.log('📖 HTML file opened for visual inspection');
      }
    });
    
  } catch (error) {
    console.error('❌ HTML generation failed:', error);
  }
}

testHTMLOutput();
