/**
 * Test script to verify the payment flow fix
 * This script will test the complete flow from flight search to payment
 */

const puppeteer = require('puppeteer');

async function testPaymentFlow() {
  console.log('🚀 Starting payment flow test...');
  
  const browser = await puppeteer.launch({ 
    headless: false, // Set to true for headless testing
    defaultViewport: null,
    args: ['--start-maximized']
  });
  
  const page = await browser.newPage();
  
  try {
    // Navigate to the application
    console.log('📍 Navigating to application...');
    await page.goto('http://localhost:5173', { waitUntil: 'networkidle2' });
    
    // Fill in search form
    console.log('🔍 Filling search form...');
    
    // Fill origin
    await page.click('input[placeholder*="From"]');
    await page.type('input[placeholder*="From"]', 'London');
    await page.waitForTimeout(1000);
    await page.keyboard.press('ArrowDown');
    await page.keyboard.press('Enter');
    
    // Fill destination
    await page.click('input[placeholder*="To"]');
    await page.type('input[placeholder*="To"]', 'Paris');
    await page.waitForTimeout(1000);
    await page.keyboard.press('ArrowDown');
    await page.keyboard.press('Enter');
    
    // Fill departure date (tomorrow)
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dateString = tomorrow.toISOString().split('T')[0];
    
    await page.click('input[type="date"]');
    await page.evaluate((date) => {
      document.querySelector('input[type="date"]').value = date;
    }, dateString);
    
    // Submit search
    console.log('🔍 Submitting search...');
    await page.click('button[type="submit"]');
    
    // Wait for results
    console.log('⏳ Waiting for flight results...');
    await page.waitForSelector('.flight-card, [data-testid="flight-card"]', { timeout: 10000 });
    
    // Select first flight
    console.log('✈️ Selecting first flight...');
    await page.click('button:contains("Select Flight"), .flight-card button');
    
    // Wait for passenger form
    console.log('👤 Waiting for passenger form...');
    await page.waitForSelector('input[placeholder*="First"], input[name*="firstName"]', { timeout: 5000 });
    
    // Fill passenger details
    console.log('👤 Filling passenger details...');
    await page.type('input[placeholder*="First"], input[name*="firstName"]', 'John');
    await page.type('input[placeholder*="Last"], input[name*="lastName"]', 'Doe');
    await page.type('input[type="email"]', '<EMAIL>');
    
    // Click Continue to Payment
    console.log('💳 Clicking Continue to Payment...');
    await page.click('button:contains("Continue to Payment")');
    
    // Wait for payment section
    console.log('⏳ Waiting for payment section...');
    await page.waitForTimeout(2000);
    
    // Check if payment section is visible
    const paymentSectionVisible = await page.evaluate(() => {
      // Look for payment method buttons or payment forms
      const stripeButton = document.querySelector('button:contains("stripe"), .stripe-payment, [data-testid="stripe-payment"]');
      const paypalButton = document.querySelector('button:contains("paypal"), .paypal-payment, [data-testid="paypal-payment"]');
      const paymentSection = document.querySelector('.payment-section, [data-testid="payment-section"]');
      
      return !!(stripeButton || paypalButton || paymentSection);
    });
    
    if (paymentSectionVisible) {
      console.log('✅ SUCCESS: Payment section is visible!');
      console.log('✅ Payment flow fix is working correctly');
      
      // Try to click on Stripe payment (demo mode)
      try {
        await page.click('button:contains("Pay"), .stripe-payment button, [data-testid="stripe-pay"]');
        console.log('💳 Clicked payment button - demo payment should process');
        
        // Wait for success page or completion
        await page.waitForTimeout(3000);
        
        const currentUrl = page.url();
        if (currentUrl.includes('/success')) {
          console.log('🎉 SUCCESS: Redirected to success page!');
        } else {
          console.log('ℹ️  Payment processing (may be in demo mode)');
        }
        
      } catch (paymentError) {
        console.log('ℹ️  Payment button interaction failed (may be expected in demo mode)');
      }
      
    } else {
      console.log('❌ FAILURE: Payment section is NOT visible');
      console.log('❌ The white screen issue may still exist');
      
      // Take screenshot for debugging
      await page.screenshot({ path: 'payment-flow-error.png', fullPage: true });
      console.log('📸 Screenshot saved as payment-flow-error.png');
    }
    
    // Check for debug controls (should not be visible in production)
    const debugControlsVisible = await page.evaluate(() => {
      const debugControls = document.querySelector('.debug-controls, [data-testid="debug-controls"]');
      const debugInfo = document.querySelector('.debug-info, [data-testid="debug-info"]');
      return !!(debugControls || debugInfo);
    });
    
    if (debugControlsVisible) {
      console.log('⚠️  WARNING: Debug controls are still visible');
    } else {
      console.log('✅ SUCCESS: Debug controls are properly hidden');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    await page.screenshot({ path: 'test-error.png', fullPage: true });
    console.log('📸 Error screenshot saved as test-error.png');
  } finally {
    await browser.close();
  }
}

// Run the test
if (require.main === module) {
  testPaymentFlow().catch(console.error);
}

module.exports = { testPaymentFlow };
