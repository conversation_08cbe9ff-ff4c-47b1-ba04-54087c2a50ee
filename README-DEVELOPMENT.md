# VerifiedOnward Development Environment

## 🚀 Quick Start

### Method 1: Automated Startup (Recommended)
```bash
cd /Users/<USER>/Documents/augment-projects/InstantDummyTicket
./start-dev.sh
```

### Method 2: Manual Startup
Open two terminal windows:

**Terminal 1 - Backend Server:**
```bash
cd /Users/<USER>/Documents/augment-projects/InstantDummyTicket/backend
node server.js
```

**Terminal 2 - Frontend Server:**
```bash
cd /Users/<USER>/Documents/augment-projects/InstantDummyTicket/frontend
npm run dev
```

## 🌐 Access Points

- **Full Homepage**: http://localhost:5173
- **Backend API**: http://localhost:5001/api/
- **Health Check**: http://localhost:5001/api/health

## 🛠️ Available Scripts

```bash
npm run dev        # Start both servers
npm run stop       # Stop both servers
npm run health     # Check server health
npm run logs       # View server logs
npm run clean      # Stop servers and clean logs
npm run install-all # Install all dependencies
```

## 🔧 Troubleshooting

### If servers won't start:
```bash
# Kill any existing processes
sudo lsof -ti:5001 | xargs kill -9
sudo lsof -ti:5173 | xargs kill -9

# Reinstall dependencies
npm run install-all

# Try starting again
./start-dev.sh
```

### Check server status:
```bash
./health-check.sh
```

### View logs:
```bash
npm run logs
# Or directly:
tail -f backend.log
tail -f frontend.log
```

## 📁 Project Structure

```
InstantDummyTicket/
├── backend/           # Express.js API server (port 5001)
├── frontend/          # React + Vite app (port 5173)
├── frontend-simple/   # Simple test interface
├── start-dev.sh      # Automated startup script
├── stop-dev.sh       # Stop script
├── health-check.sh   # Health check script
└── README-DEVELOPMENT.md
```

## 🎯 Features Available

### Full Homepage (http://localhost:5173)
- Professional VerifiedOnward branding
- Flight search with autocomplete
- Real-time flight selection
- Passenger information forms
- Stripe/PayPal checkout
- Embassy-ready PDF generation
- Complete responsive design

### Backend API (http://localhost:5001/api/)
- Flight search endpoints
- PDF generation service
- Payment processing
- Email notifications
- Airport data service

## 🔍 Health Monitoring

The startup script includes:
- Automatic dependency installation
- Port conflict resolution
- Health checks for both servers
- Process monitoring
- Graceful shutdown handling
- Comprehensive logging

## 🚨 Common Issues & Solutions

### Issue: "This site can't be reached" on localhost:5173
**Solution**: Frontend Vite server is not running
```bash
cd frontend
npm run dev
```

### Issue: API calls failing
**Solution**: Backend server is not running
```bash
cd backend
node server.js
```

### Issue: Port already in use
**Solution**: Kill existing processes
```bash
./stop-dev.sh
# Or manually:
lsof -ti:5001 | xargs kill -9
lsof -ti:5173 | xargs kill -9
```

### Issue: Dependencies missing
**Solution**: Reinstall dependencies
```bash
npm run install-all
```

## 📊 Server Monitoring

Both servers include:
- Graceful shutdown handling
- Error logging
- Health check endpoints
- Process monitoring
- Automatic restart capabilities (via startup script)

## 🔄 Development Workflow

1. Start servers: `./start-dev.sh`
2. Access homepage: http://localhost:5173
3. Make changes to code
4. Servers auto-reload (Vite HMR for frontend)
5. Check health: `./health-check.sh`
6. View logs: `npm run logs`
7. Stop servers: `./stop-dev.sh`
