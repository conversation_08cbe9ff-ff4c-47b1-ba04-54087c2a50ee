const axios = require('axios');

async function testHTMLDebug() {
  console.log('🔍 Testing HTML debug output...');
  
  const testData = {
    bookingReference: 'HTMLTEST123',
    passengers: [
      { firstName: '<PERSON>', lastName: '<PERSON>' },
      { firstName: '<PERSON>', lastName: 'Do<PERSON>' }
    ],
    outboundFlight: {
      airline: 'RYANAIR',
      flightNumber: 'FR 1234',
      departure: {
        code: 'LHR',
        city: 'London, United Kingdom',
        time: '2025-07-20T10:30:00Z',
        terminal: '2'
      },
      arrival: {
        code: 'BCN',
        city: 'Barcelona, Spain',
        time: '2025-07-20T13:45:00Z',
        terminal: '1'
      },
      duration: '2h 15m'
    },
    returnFlight: {
      airline: 'EASYJET',
      flightNumber: 'U2 5678',
      departure: {
        code: 'BCN',
        city: 'Barcelona, Spain',
        time: '2025-07-27T15:20:00Z',
        terminal: '1'
      },
      arrival: {
        code: 'LHR',
        city: 'London, United Kingdom',
        time: '2025-07-27T16:35:00Z',
        terminal: '2'
      },
      duration: '2h 15m'
    },
    totalPrice: 9.98
  };

  try {
    console.log('📤 Sending request to generate HTML debug output...');
    const response = await axios.post('http://localhost:5001/api/tickets/generate', testData);
    
    if (response.data.success) {
      console.log('✅ Request successful');
      console.log('📋 Check for debug HTML file in the project directory');
      console.log('📋 Look for aircraft assignments and Important Information in the HTML');
    } else {
      console.log('❌ Request failed:', response.data.error);
    }
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

testHTMLDebug().catch(console.error);
