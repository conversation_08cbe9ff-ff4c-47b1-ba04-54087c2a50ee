#!/usr/bin/env node

/**
 * Quick Checkout Diagnostic Script
 * Checks if the checkout page loads properly with test data
 */

const puppeteer = require('puppeteer');

async function diagnoseCheckout() {
  console.log('🔍 Diagnosing checkout page...');
  
  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: false,
      defaultViewport: { width: 1200, height: 800 }
    });
    
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      const text = msg.text();
      if (msg.type() === 'error') {
        console.log('❌ Browser Error:', text);
      } else if (text.includes('CheckoutPageFixed') || text.includes('BookingContext')) {
        console.log('🔍 Debug:', text);
      }
    });
    
    // Set up localStorage with valid test data
    await page.evaluateOnNewDocument(() => {
      // Clear any existing data first
      localStorage.clear();
      
      // Set up valid booking data
      localStorage.setItem('bookingSelectedFlight', JSON.stringify({
        id: 'test-flight-1',
        flight: {
          number: 'BA 123',
          departure: { 
            airport: 'LHR', 
            iataCode: 'LHR',
            city: 'London Heathrow', 
            time: '2025-08-15 10:00',
            terminal: '5'
          },
          arrival: { 
            airport: 'JFK', 
            iataCode: 'JFK',
            city: 'New York JFK', 
            time: '2025-08-15 18:00',
            terminal: '4'
          },
          duration: '8h 00m',
          stops: 0
        },
        airline: { 
          name: 'British Airways', 
          code: 'BA',
          logo: 'https://www.gstatic.com/flights/airline_logos/70px/BA.png'
        },
        price: { 
          total: 4.99, 
          currency: 'USD', 
          originalPrice: 650,
          displayPrice: 4.99
        }
      }));
      
      localStorage.setItem('bookingPassengers', JSON.stringify([
        { id: 1, firstName: 'John', lastName: 'Smith' }
      ]));
      
      localStorage.setItem('bookingEmail', '<EMAIL>');
      localStorage.setItem('bookingTripType', 'oneWay');
      
      console.log('✅ Test data set in localStorage');
    });
    
    console.log('📝 Step 1: Loading checkout page directly...');
    await page.goto('http://localhost:5174/checkout', { waitUntil: 'networkidle0' });
    
    // Wait for initial loading
    await page.waitForTimeout(2000);
    
    console.log('📝 Step 2: Checking page state...');
    
    // Check if still loading
    const loadingElement = await page.$('text=Loading checkout');
    if (loadingElement) {
      console.log('⏳ Still showing loading state, waiting longer...');
      await page.waitForTimeout(5000);
    }
    
    // Get page content
    const bodyText = await page.evaluate(() => document.body.textContent);
    const pageHTML = await page.content();
    
    console.log('📊 Page Analysis:');
    console.log('- Content length:', bodyText.length);
    console.log('- Is blank page:', bodyText.trim().length < 50 ? '❌ YES' : '✅ NO');
    
    // Check for specific elements
    const hasError = await page.$('.text-red-500, .error-message');
    const hasLoading = await page.$('text=Loading checkout');
    const hasPaymentForm = await page.$('form, .payment-form');
    const hasBookingSummary = await page.$('.booking-summary, .flight-summary');
    const hasStripeElements = await page.$('.stripe-element, [data-testid="stripe"]');
    
    console.log('- Error message:', hasError ? '⚠️ YES' : '✅ NO');
    console.log('- Loading state:', hasLoading ? '⏳ YES' : '✅ NO');
    console.log('- Payment form:', hasPaymentForm ? '✅ YES' : '❌ NO');
    console.log('- Booking summary:', hasBookingSummary ? '✅ YES' : '❌ NO');
    console.log('- Stripe elements:', hasStripeElements ? '✅ YES' : '❌ NO');
    
    // If there's an error, get the error text
    if (hasError) {
      const errorText = await hasError.evaluate(el => el.textContent);
      console.log('🔍 Error details:', errorText);
    }
    
    // Check for validation errors specifically
    const validationErrors = await page.$$eval('li', elements => 
      elements.map(el => el.textContent).filter(text => 
        text.includes('flight') || text.includes('passenger') || text.includes('email')
      )
    ).catch(() => []);
    
    if (validationErrors.length > 0) {
      console.log('🔍 Validation errors found:');
      validationErrors.forEach(error => console.log('  -', error));
    }
    
    // Test the bypass functionality
    console.log('📝 Step 3: Testing bypass functionality...');
    await page.goto('http://localhost:5174/checkout?bypass=true', { waitUntil: 'networkidle0' });
    await page.waitForTimeout(2000);
    
    const bypassBodyText = await page.evaluate(() => document.body.textContent);
    const bypassIsBlank = bypassBodyText.trim().length < 50;
    
    console.log('🔧 Bypass mode analysis:');
    console.log('- Content length:', bypassBodyText.length);
    console.log('- Is blank page:', bypassIsBlank ? '❌ YES' : '✅ NO');
    
    // Final assessment
    const normalModeWorking = !bodyText.trim().length < 50 || hasError;
    const bypassModeWorking = !bypassIsBlank;
    
    console.log('');
    console.log('🎯 Final Assessment:');
    console.log('- Normal mode working:', normalModeWorking ? '✅ YES' : '❌ NO');
    console.log('- Bypass mode working:', bypassModeWorking ? '✅ YES' : '❌ NO');
    
    if (normalModeWorking && bypassModeWorking) {
      console.log('🎉 SUCCESS: Checkout page is functioning correctly!');
      console.log('✅ The blank screen issue has been resolved');
    } else if (bypassModeWorking && !normalModeWorking) {
      console.log('⚠️ PARTIAL: Checkout works in bypass mode but has validation issues');
      console.log('💡 This suggests the data filtering fix is working');
    } else {
      console.log('❌ FAILED: Checkout page still has issues');
    }
    
    // Keep browser open for manual inspection
    console.log('');
    console.log('🔍 Browser will stay open for 10 seconds for manual inspection...');
    await page.waitForTimeout(10000);
    
    return normalModeWorking || bypassModeWorking;
    
  } catch (error) {
    console.error('❌ Diagnostic failed:', error.message);
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the diagnostic
if (require.main === module) {
  diagnoseCheckout().then(success => {
    if (success) {
      console.log('🎯 Checkout diagnostic: PASSED');
      process.exit(0);
    } else {
      console.log('🚨 Checkout diagnostic: FAILED');
      process.exit(1);
    }
  }).catch(error => {
    console.error('💥 Diagnostic error:', error);
    process.exit(1);
  });
}

module.exports = { diagnoseCheckout };
