// Test frontend search API call
const testFrontendSearch = async () => {
  console.log('🔍 Testing frontend search API call...');
  
  try {
    const response = await fetch('http://localhost:5001/api/flights/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        origin: 'MAN',
        destination: 'MAD',
        date: '2025-07-14',
        tripType: 'oneWay'
      })
    });
    
    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    if (data.success && data.data && data.data.flights) {
      console.log('✅ Search working! Found', data.data.flights.length, 'flights');
      console.log('First flight:', data.data.flights[0].airline.name, data.data.flights[0].flight.number);
    } else {
      console.log('❌ Search not working properly');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
};

testFrontendSearch();
