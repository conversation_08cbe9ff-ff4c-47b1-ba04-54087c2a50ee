const axios = require('axios');

async function testFixesVerification() {
  console.log('🧪 Testing all three critical fixes...');
  
  const testCases = [
    {
      name: 'Ryanair vs EasyJet Round Trip',
      outboundAirline: 'RYANAIR',
      returnAirline: 'EASYJET'
    },
    {
      name: 'British Airways vs Lufthansa Round Trip',
      outboundAirline: 'BRITISH AIRWAYS',
      returnAirline: 'LUFTHANSA'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n✈️  Testing ${testCase.name}...`);
    
    const testData = {
      bookingReference: 'TEST123', // This should be replaced with short codes
      passengers: [
        { firstName: 'John', lastName: '<PERSON>' },
        { firstName: 'Jane', lastName: 'Doe' }
      ],
      outboundFlight: {
        airline: testCase.outboundAirline,
        flightNumber: 'FR 1234',
        departure: {
          code: 'LHR',
          city: 'London, United Kingdom',
          time: '2025-07-20T10:30:00Z',
          terminal: '2'
        },
        arrival: {
          code: 'BCN',
          city: 'Barcelona, Spain',
          time: '2025-07-20T13:45:00Z',
          terminal: '1'
        },
        duration: '2h 15m'
      },
      returnFlight: {
        airline: testCase.returnAirline,
        flightNumber: 'U2 5678',
        departure: {
          code: 'BCN',
          city: 'Barcelona, Spain',
          time: '2025-07-27T15:20:00Z',
          terminal: '1'
        },
        arrival: {
          code: 'LHR',
          city: 'London, United Kingdom',
          time: '2025-07-27T16:35:00Z',
          terminal: '2'
        },
        duration: '2h 15m'
      },
      totalPrice: 9.98
    };

    try {
      const response = await axios.post('http://localhost:5001/api/tickets/generate', testData);
      
      if (response.data.success) {
        console.log(`✅ ${testCase.name} - PDF generated successfully`);
        console.log(`📋 Booking Reference: ${response.data.bookingReference}`);
        console.log(`📋 Reservation Code: ${response.data.reservationCode}`);
        console.log(`📥 Download URL: ${response.data.downloadUrl}`);
        
        // Verify reservation code length
        const codeLength = response.data.reservationCode.length;
        if (codeLength >= 6 && codeLength <= 7) {
          console.log(`✅ Reservation code length correct: ${codeLength} characters`);
        } else {
          console.log(`❌ Reservation code length incorrect: ${codeLength} characters`);
        }
      } else {
        console.log(`❌ ${testCase.name} - Failed: ${response.data.error}`);
      }
    } catch (error) {
      console.log(`❌ ${testCase.name} - Error: ${error.message}`);
    }
  }
  
  console.log('\n🎉 Fix verification testing completed!');
}

// Run the test
testFixesVerification().catch(console.error);
