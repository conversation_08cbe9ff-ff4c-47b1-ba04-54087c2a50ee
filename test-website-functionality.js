#!/usr/bin/env node

/**
 * Comprehensive VerifiedOnward Website Functionality Test
 * Tests all core functionality including flight search, API connectivity, and user workflow
 */

const axios = require('axios');

const BACKEND_URL = 'http://localhost:5001';
const FRONTEND_URL = 'http://localhost:5173';

// Test results tracking
const testResults = [];
let allTestsPassed = true;

function logTest(testName, status, details = '') {
  const result = { test: testName, status, details };
  testResults.push(result);
  
  const statusIcon = status === 'PASS' ? '✅' : '❌';
  console.log(`${statusIcon} ${testName}: ${status}`);
  if (details) console.log(`   ${details}`);
  
  if (status === 'FAIL') allTestsPassed = false;
}

async function testBackendHealth() {
  console.log('\n🔍 Testing Backend Health...');
  try {
    const response = await axios.get(`${BACKEND_URL}/api/health`, { timeout: 5000 });
    if (response.data.status === 'OK') {
      logTest('Backend Health Check', 'PASS', 'API server is running');
      return true;
    } else {
      logTest('Backend Health Check', 'FAIL', 'Unexpected response format');
      return false;
    }
  } catch (error) {
    logTest('Backend Health Check', 'FAIL', error.message);
    return false;
  }
}

async function testAirportSearch() {
  console.log('\n🛫 Testing Airport Search API...');
  try {
    const response = await axios.get(`${BACKEND_URL}/api/flights/airports`, {
      params: { query: 'London' },
      timeout: 10000
    });
    
    if (response.data.success && response.data.data.length > 0) {
      const airportCount = response.data.data.length;
      logTest('Airport Search API', 'PASS', `Found ${airportCount} airports for "London"`);
      
      // Verify expected airports are present
      const airports = response.data.data;
      const hasLHR = airports.some(airport => airport.iataCode === 'LHR');
      const hasLGW = airports.some(airport => airport.iataCode === 'LGW');
      
      if (hasLHR && hasLGW) {
        logTest('Airport Data Quality', 'PASS', 'Major London airports (LHR, LGW) found');
      } else {
        logTest('Airport Data Quality', 'FAIL', 'Missing expected major airports');
      }
      
      return true;
    } else {
      logTest('Airport Search API', 'FAIL', 'No airports returned or invalid response');
      return false;
    }
  } catch (error) {
    logTest('Airport Search API', 'FAIL', error.message);
    return false;
  }
}

async function testFlightSearch() {
  console.log('\n✈️ Testing Flight Search API...');
  try {
    const searchData = {
      origin: 'LHR',
      destination: 'JFK',
      date: '2025-08-15',
      tripType: 'oneWay'
    };
    
    const response = await axios.post(`${BACKEND_URL}/api/flights/search`, searchData, {
      timeout: 20000
    });
    
    if (response.data.success && response.data.data.flights.length > 0) {
      const flightCount = response.data.data.flights.length;
      logTest('Flight Search API', 'PASS', `Found ${flightCount} flights for LHR → JFK`);
      
      // Verify flight data structure
      const firstFlight = response.data.data.flights[0];
      const hasRequiredFields = firstFlight.id && firstFlight.flight && firstFlight.airline && firstFlight.price;
      
      if (hasRequiredFields) {
        logTest('Flight Data Structure', 'PASS', 'All required flight fields present');
      } else {
        logTest('Flight Data Structure', 'FAIL', 'Missing required flight fields');
      }
      
      // Verify pricing
      if (firstFlight.price.displayPrice === 4.99) {
        logTest('Flight Pricing', 'PASS', 'Correct $4.99 display price');
      } else {
        logTest('Flight Pricing', 'FAIL', `Incorrect price: $${firstFlight.price.displayPrice}`);
      }
      
      return true;
    } else {
      logTest('Flight Search API', 'FAIL', 'No flights returned or invalid response');
      return false;
    }
  } catch (error) {
    logTest('Flight Search API', 'FAIL', error.message);
    return false;
  }
}

async function testReturnFlightSearch() {
  console.log('\n🔄 Testing Return Flight Search...');
  try {
    const searchData = {
      origin: 'LHR',
      destination: 'JFK',
      date: '2025-08-15',
      returnDate: '2025-08-22',
      tripType: 'return'
    };
    
    const response = await axios.post(`${BACKEND_URL}/api/flights/search`, searchData, {
      timeout: 20000
    });
    
    if (response.data.success && response.data.data.outboundFlights && response.data.data.returnFlights) {
      const outboundCount = response.data.data.outboundFlights.length;
      const returnCount = response.data.data.returnFlights.length;
      logTest('Return Flight Search', 'PASS', `Found ${outboundCount} outbound, ${returnCount} return flights`);
      return true;
    } else {
      logTest('Return Flight Search', 'FAIL', 'Invalid return flight response structure');
      return false;
    }
  } catch (error) {
    logTest('Return Flight Search', 'FAIL', error.message);
    return false;
  }
}

async function testFrontendConnectivity() {
  console.log('\n🌐 Testing Frontend Connectivity...');
  try {
    const response = await axios.get(FRONTEND_URL, { timeout: 5000 });
    if (response.status === 200) {
      logTest('Frontend Server', 'PASS', 'Website is accessible');
      return true;
    } else {
      logTest('Frontend Server', 'FAIL', `Unexpected status: ${response.status}`);
      return false;
    }
  } catch (error) {
    logTest('Frontend Server', 'FAIL', error.message);
    return false;
  }
}

async function testAPIProxy() {
  console.log('\n🔗 Testing Frontend-Backend Proxy...');
  try {
    // Test proxy by making request through frontend
    const response = await axios.get(`${FRONTEND_URL}/api/health`, { timeout: 5000 });
    if (response.data.status === 'OK') {
      logTest('API Proxy', 'PASS', 'Frontend can communicate with backend');
      return true;
    } else {
      logTest('API Proxy', 'FAIL', 'Proxy working but unexpected response');
      return false;
    }
  } catch (error) {
    logTest('API Proxy', 'FAIL', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting VerifiedOnward Website Functionality Tests\n');
  console.log('=' .repeat(60));
  
  // Core backend tests
  const backendHealthy = await testBackendHealth();
  if (!backendHealthy) {
    console.log('\n❌ Backend is not healthy. Skipping dependent tests.');
    return;
  }
  
  await testAirportSearch();
  await testFlightSearch();
  await testReturnFlightSearch();
  
  // Frontend tests
  await testFrontendConnectivity();
  await testAPIProxy();
  
  // Summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(60));
  
  const passCount = testResults.filter(r => r.status === 'PASS').length;
  const failCount = testResults.filter(r => r.status === 'FAIL').length;
  
  console.log(`✅ Passed: ${passCount}`);
  console.log(`❌ Failed: ${failCount}`);
  console.log(`📈 Success Rate: ${Math.round((passCount / testResults.length) * 100)}%`);
  
  if (allTestsPassed) {
    console.log('\n🎉 ALL TESTS PASSED! VerifiedOnward website is fully functional.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the issues above.');
  }
  
  console.log('\n📋 Detailed Results:');
  testResults.forEach(result => {
    const icon = result.status === 'PASS' ? '✅' : '❌';
    console.log(`${icon} ${result.test}: ${result.status}`);
    if (result.details) console.log(`   └─ ${result.details}`);
  });
}

// Run tests
runAllTests().catch(error => {
  console.error('❌ Test runner error:', error.message);
  process.exit(1);
});
