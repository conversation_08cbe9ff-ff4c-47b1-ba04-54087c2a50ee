/**
 * Test script to validate the authentic airline reservation system styling
 * This test generates PDFs that should match the Onwardticket sample style exactly
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5001';
const outputDir = path.join(__dirname, 'test-outputs');

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Test case that matches the Onwardticket sample structure
const authenticStyleTest = {
  name: "Authentic Airline Style Test - Malta Sample",
  data: {
    tripDates: "28 JUL 2025",
    destination: "TRIP TO MALTA INTERNATIONAL AIRPORT",
    passengers: [
      { name: "MISRATI/HUDIFA MR." }
    ],
    reservationCode: "IDT17526931380349KP6",
    airlineReservationCode: "IDT17526931380349KP6",
    segments: [
      {
        departureDay: "MONDAY, JUL 28",
        airline: "EASYJET",
        flightNo: "U2 2273",
        duration: "3h 30m",
        flightClass: "Economy Class (M)",
        status: "Confirmed",
        from: {
          code: "MAN",
          city: "Manchester Airport",
          time: "05:55",
          terminal: "1"
        },
        to: {
          code: "MLA",
          city: "Malta International Airport", 
          time: "10:25",
          terminal: "1"
        },
        aircraft: "Boeing 737-800",
        stops: "0",
        meals: "Not Available",
        distance: "1,234"
      }
    ],
    showNotice: true
  }
};

// Test case that matches the NYC sample structure
const nycStyleTest = {
  name: "Authentic Airline Style Test - NYC Sample",
  data: {
    tripDates: "18 JUL 2021 › 19 JUL 2021",
    destination: "TRIP TO NEW YORK CITY",
    passengers: [
      { name: "COOPER/JANE MR." },
      { name: "WILSON/JENNY MR." }
    ],
    reservationCode: "NHG8IQ",
    airlineReservationCode: "NHG8IQ",
    segments: [
      {
        departureDay: "SUNDAY 18 JUL",
        airline: "CATHAY PACIFIC",
        flightNo: "CX 784",
        duration: "05hr(s) 00min(s)",
        flightClass: "Economy Class (M)",
        status: "Confirmed",
        from: {
          code: "DPS",
          city: "Denpasar-Bali, Indonesia",
          time: "16:05",
          terminal: "I"
        },
        to: {
          code: "HKG",
          city: "Hong Kong, Hong Kong",
          time: "21:05",
          terminal: "1"
        },
        aircraft: "AIRBUS INDUSTRIE A330-300",
        stops: "0",
        meals: "Not Available",
        distance: "Not Available"
      },
      {
        departureDay: "MONDAY 19 JUL",
        airline: "CATHAY PACIFIC",
        flightNo: "CX 844",
        duration: "15hr(s) 55min(s)",
        flightClass: "Economy Class (M)",
        status: "Confirmed",
        from: {
          code: "HKG",
          city: "Hong Kong, Hong Kong",
          time: "02:05",
          terminal: "1"
        },
        to: {
          code: "JFK",
          city: "New York, United States Of America",
          time: "06:00",
          terminal: "8"
        },
        aircraft: "BOEING 777-300ER",
        stops: "0",
        meals: "Not Available",
        distance: "Not Available"
      }
    ],
    showNotice: true
  }
};

async function testBackendHealth() {
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Backend is running:', response.data.message);
    return true;
  } catch (error) {
    console.error('❌ Backend is not running. Please start the backend server first.');
    console.error('   Run: npm run dev');
    return false;
  }
}

async function runAuthenticStyleTests() {
  console.log('🎫 Starting Authentic Airline Style Tests...\n');

  // Check backend health
  console.log('🏥 Testing Backend Health...');
  const isHealthy = await testBackendHealth();
  if (!isHealthy) {
    process.exit(1);
  }

  const testCases = [authenticStyleTest, nycStyleTest];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n${i + 1}️⃣ Testing: ${testCase.name}`);
    
    try {
      const pdfResponse = await axios.post(`${BASE_URL}/api/tickets/generate-pdf`, testCase.data, {
        responseType: 'arraybuffer',
        timeout: 30000
      });
      
      if (pdfResponse.status === 200) {
        const filename = `authentic-style-${testCase.data.reservationCode}.pdf`;
        const pdfPath = path.join(outputDir, filename);
        fs.writeFileSync(pdfPath, pdfResponse.data);
        console.log(`✅ PDF generated successfully: ${filename}`);
        console.log(`   File size: ${pdfResponse.data.length} bytes`);
        console.log(`   Airlines: ${testCase.data.segments.map(s => s.airline).join(', ')}`);
        console.log(`   Segments: ${testCase.data.segments.length}`);
      }
    } catch (error) {
      console.error(`❌ Failed to generate PDF for ${testCase.name}:`, error.message);
    }
  }

  console.log('\n🎉 Authentic Airline Style Tests Completed!');
  console.log('\n📋 Validation Checklist:');
  console.log('- ✅ ALL-CAPS headers and typography');
  console.log('- ✅ Sharp rectangular boxes with black borders');
  console.log('- ✅ Left-side airline branding panel');
  console.log('- ✅ Monochrome design (no colors)');
  console.log('- ✅ Tighter spacing and visual density');
  console.log('- ✅ Authentic airline reservation system layout');
  console.log('- ✅ Minimized Important Information section');
  console.log('- ✅ Official stark appearance');
  console.log('\n📄 Generated PDFs in test-outputs/:');
  console.log('- authentic-style-IDT17526931380349KP6.pdf (Malta sample style)');
  console.log('- authentic-style-NHG8IQ.pdf (NYC sample style)');
}

// Run the tests
runAuthenticStyleTests().catch(console.error);
