const axios = require('axios');
const fs = require('fs');

async function testVerifyTextStyling() {
  console.log('🎨 TESTING "PLEASE VERIFY FLIGHT TIMES" STYLING');
  console.log('===============================================');
  console.log('Testing the positioning and color of verification text\n');
  
  const testData = {
    bookingReference: 'STYLETEST',
    passengers: [
      { firstName: 'John', lastName: 'Smith' },
      { firstName: 'Jane', lastName: 'Doe' }
    ],
    outboundFlight: {
      airline: 'RYANAIR',
      flightNumber: 'FR 1234',
      departure: {
        code: 'LHR',
        city: 'London, United Kingdom',
        time: '2025-07-21T10:30:00Z',
        terminal: '2'
      },
      arrival: {
        code: 'BCN',
        city: 'Barcelona, Spain',
        time: '2025-07-21T13:45:00Z',
        terminal: '1'
      },
      duration: '2h 15m'
    },
    returnFlight: {
      airline: 'EASYJET',
      flightNumber: 'U2 5678',
      departure: {
        code: 'BCN',
        city: 'Barcelona, Spain',
        time: '2025-07-28T15:20:00Z',
        terminal: '1'
      },
      arrival: {
        code: 'LHR',
        city: 'London, United Kingdom',
        time: '2025-07-28T16:35:00Z',
        terminal: '2'
      },
      duration: '2h 15m'
    },
    totalPrice: 9.98
  };

  try {
    console.log('📤 Generating test booking with styled verification text...');
    const response = await axios.post('http://localhost:5001/api/tickets/generate', testData);
    
    if (response.data.success) {
      console.log('✅ Booking generated successfully');
      console.log(`📋 Booking Reference: ${response.data.bookingReference}`);
      
      // Download the PDF
      const downloadResponse = await axios.get(`http://localhost:5001${response.data.downloadUrl}`, {
        responseType: 'arraybuffer'
      });
      
      const filename = 'VERIFY-TEXT-STYLING-TEST.pdf';
      fs.writeFileSync(filename, downloadResponse.data);
      console.log(`📄 PDF saved: ${filename}`);
      
      console.log(`\n🎯 STYLING VERIFICATION CHECKLIST:`);
      console.log(`==================================`);
      console.log(`📋 Open the PDF: ${filename}`);
      console.log(`📋 Check the following styling elements:`);
      console.log(`   1. ✅ "Please verify flight times prior to departure" text is positioned on the RIGHT side`);
      console.log(`   2. ✅ Text is aligned with the "DEPARTURE: MONDAY, JUL 21" header`);
      console.log(`   3. ✅ Text is aligned with the "RETURN: MONDAY, JUL 28" header`);
      console.log(`   4. ✅ Text color is LIGHT GREY (#888888) - not black`);
      console.log(`   5. ✅ Text size is smaller than the main header text`);
      console.log(`   6. ✅ Text appears for BOTH outbound and return flight segments`);
      
      console.log(`\n🎨 EXPECTED LAYOUT:`);
      console.log(`==================`);
      console.log(`✈ DEPARTURE: MONDAY, JUL 21                    Please verify flight times prior to departure`);
      console.log(`✈ RETURN: MONDAY, JUL 28                       Please verify flight times prior to departure`);
      
      console.log(`\n📋 This should match the authentic airline reservation aesthetic from your sample images.`);
      
    } else {
      console.log(`❌ Booking generation failed: ${response.data.error}`);
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

testVerifyTextStyling().catch(console.error);
