const fs = require('fs');
const path = require('path');

// Import the PDF service directly
const pdfService = require('./backend/services/pdfService');

async function debugHtmlOutput() {
  console.log('🔍 DEBUGGING HTML OUTPUT DIRECTLY');
  console.log('==================================');
  console.log('Generating HTML directly from pdfService to inspect styling\n');
  
  const testData = {
    origin: 'MAN',
    destination: 'MLA',
    passengers: [
      { firstName: 'HUDIFA', lastName: 'MISRATI' },
      { firstName: 'ATEDAL', lastName: 'ZEMIT' }
    ],
    reservationCode: 'HTMLDEBUG',
    airlineReservationCode: 'RYFLRF',
    segments: [
      {
        type: 'outbound',
        airline: 'RYANAIR',
        flightNumber: 'FR 5209',
        from: { code: 'MAN', city: 'Manchester Airport' },
        to: { code: 'MLA', city: 'Malta International Airport' },
        departureTime: '17:10',
        arrivalTime: '21:35',
        departureTerminal: '1',
        arrivalTerminal: '1',
        departureDay: 'MONDAY, JUL 21',
        duration: '3h 25m',
        aircraft: 'BOEING 737-800',
        stops: '0'
      },
      {
        type: 'return',
        airline: 'EASYJET',
        flightNumber: 'U2 2274',
        from: { code: 'MLA', city: 'Malta International Airport' },
        to: { code: 'MAN', city: 'Manchester Airport' },
        departureTime: '11:15',
        arrivalTime: '13:50',
        departureTerminal: '1',
        arrivalTerminal: '1',
        departureDay: 'MONDAY, JUL 28',
        duration: '3h 35m',
        aircraft: 'AIRBUS A320',
        stops: '0'
      }
    ],
    showNotice: false  // This should remove the Important Information section
  };

  try {
    console.log('📝 Generating HTML template...');
    const htmlContent = await pdfService.generateTicketHTML(testData);
    
    const filename = 'debug-html-output.html';
    fs.writeFileSync(filename, htmlContent);
    console.log(`📄 HTML saved: ${filename}`);
    
    console.log(`\n🔍 DEBUGGING CHECKLIST:`);
    console.log(`======================`);
    
    console.log(`\n✅ FIX 1 - CSS Verification Text Styling:`);
    console.log(`   📍 Search for: ".departure-header .verify-text"`);
    console.log(`   📍 Should have: position: absolute; right: 8px;`);
    console.log(`   📍 Should have: transform: translateY(-50%);`);
    console.log(`   📍 Should have: color: #888888;`);
    
    console.log(`\n✅ FIX 2 - Important Information Section:`);
    console.log(`   📍 Search for: "Important Information"`);
    console.log(`   📍 Should NOT appear anywhere in the HTML`);
    console.log(`   📍 showNotice is set to: ${testData.showNotice}`);
    
    console.log(`\n🔍 HTML STRUCTURE TO VERIFY:`);
    console.log(`============================`);
    console.log(`   📍 Look for: <div class="departure-header">`);
    console.log(`   📍 Inside should be: <div class="left-content">`);
    console.log(`   📍 And: <div class="verify-text">Please verify flight times prior to departure</div>`);
    
    console.log(`\n📊 MANUAL INSPECTION STEPS:`);
    console.log(`===========================`);
    console.log(`1. Open ${filename} in a web browser`);
    console.log(`2. Use browser dev tools to inspect the departure headers`);
    console.log(`3. Verify the verification text is positioned on the right`);
    console.log(`4. Confirm no "Important Information" section exists`);
    console.log(`5. Check that the CSS styles are applied correctly`);
    
    // Also extract key parts for inspection
    console.log(`\n🔍 KEY HTML SECTIONS:`);
    console.log(`====================`);
    
    // Check for departure header styling
    if (htmlContent.includes('.departure-header .verify-text')) {
      console.log(`✅ Found verify-text CSS styling in HTML`);
    } else {
      console.log(`❌ verify-text CSS styling NOT found in HTML`);
    }
    
    // Check for Important Information
    if (htmlContent.includes('Important Information')) {
      console.log(`❌ "Important Information" section STILL EXISTS in HTML`);
    } else {
      console.log(`✅ "Important Information" section successfully REMOVED from HTML`);
    }
    
    // Check for showNotice conditional
    if (htmlContent.includes('showNotice')) {
      console.log(`⚠️  showNotice conditional logic found in HTML template`);
    } else {
      console.log(`✅ No showNotice references in final HTML (as expected)`);
    }
    
    console.log(`\n📄 Open ${filename} to visually inspect the fixes!`);
    
  } catch (error) {
    console.log(`❌ Error generating HTML: ${error.message}`);
    console.log(error.stack);
  }
}

debugHtmlOutput().catch(console.error);
