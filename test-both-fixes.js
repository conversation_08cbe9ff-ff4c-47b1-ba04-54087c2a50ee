const axios = require('axios');
const fs = require('fs');

async function testBothFixes() {
  console.log('🔧 TESTING BOTH FIXES');
  console.log('=====================');
  console.log('1. Right-aligned verification text positioning');
  console.log('2. Complete removal of "Important Information" section\n');
  
  const testData = {
    bookingReference: 'BOTHFIXES',
    passengers: [
      { firstName: 'HUDIFA', lastName: 'MISRATI' },
      { firstName: 'ATEDAL', lastName: 'ZEMIT' }
    ],
    outboundFlight: {
      airline: 'RYANAIR',
      flightNumber: 'FR 5209',
      departure: {
        code: 'MAN',
        city: 'Manchester Airport',
        time: '2025-07-21T17:10:00Z',
        terminal: '1'
      },
      arrival: {
        code: 'MLA',
        city: 'Malta International Airport',
        time: '2025-07-21T21:35:00Z',
        terminal: '1'
      },
      duration: '3h 25m'
    },
    returnFlight: {
      airline: 'EASYJET',
      flightNumber: 'U2 2274',
      departure: {
        code: 'MLA',
        city: 'Malta International Airport',
        time: '2025-07-28T11:15:00Z',
        terminal: '1'
      },
      arrival: {
        code: 'MAN',
        city: 'Manchester Airport',
        time: '2025-07-28T13:50:00Z',
        terminal: '1'
      },
      duration: '3h 35m'
    },
    totalPrice: 4.99
  };

  try {
    console.log('📤 Generating test PDF with both fixes applied...');
    const response = await axios.post('http://localhost:5001/api/tickets/generate', testData);
    
    if (response.data.success) {
      console.log('✅ Booking generated successfully');
      console.log(`📋 Reservation Code: ${response.data.reservationCode}`);
      
      // Download the PDF
      const downloadResponse = await axios.get(`http://localhost:5001${response.data.downloadUrl}`, {
        responseType: 'arraybuffer'
      });
      
      const filename = 'BOTH-FIXES-TEST.pdf';
      fs.writeFileSync(filename, downloadResponse.data);
      console.log(`📄 PDF saved: ${filename}`);
      
      console.log(`\n🎯 VERIFICATION CHECKLIST`);
      console.log(`=========================`);
      console.log(`📋 Open the PDF: ${filename}`);
      console.log(`📋 Verify the following fixes:`);
      
      console.log(`\n✅ FIX 1: "Please verify flight times prior to departure" positioning:`);
      console.log(`   • Text should be positioned on the FAR RIGHT side of departure headers`);
      console.log(`   • Text should be horizontally aligned with "DEPARTURE:" and "RETURN:" headers`);
      console.log(`   • Text should appear in light grey color (#888888)`);
      console.log(`   • Text should be properly right-aligned within the header section`);
      
      console.log(`\n✅ FIX 2: "Important Information" section removal:`);
      console.log(`   • NO "Important Information" section should appear at the bottom`);
      console.log(`   • PDF should end cleanly after the flight details`);
      console.log(`   • No bullet points about non-refundable itinerary, extras, etc.`);
      console.log(`   • Clean, professional ending without disclaimer text`);
      
      console.log(`\n🎨 EXPECTED LAYOUT:`);
      console.log(`==================`);
      console.log(`✈ DEPARTURE: MONDAY, JUL 21                                    Please verify flight times prior to departure`);
      console.log(`                                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^`);
      console.log(`                                                                (Light grey, far right aligned)`);
      console.log(``);
      console.log(`✈ RETURN: MONDAY, JUL 28                                       Please verify flight times prior to departure`);
      console.log(`                                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^`);
      console.log(`                                                                (Light grey, far right aligned)`);
      
      console.log(`\n📋 BOTTOM OF PDF SHOULD END WITH:`);
      console.log(`=================================`);
      console.log(`[Flight details table]`);
      console.log(`[End of PDF - NO Important Information section]`);
      
      console.log(`\n🚀 Both fixes should now match the authentic airline reservation aesthetic!`);
      
    } else {
      console.log(`❌ Booking generation failed: ${response.data.error}`);
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

testBothFixes().catch(console.error);
