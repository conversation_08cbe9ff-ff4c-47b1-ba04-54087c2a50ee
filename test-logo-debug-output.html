
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight Reservation - HTMLTEST123</title>
    <style>
        /* Authentic airline reservation system typography */
        body {
            font-family: Arial, Helvetica, sans-serif;
            margin: 0;
            padding: 12px;
            background: white;
            color: #000;
            line-height: 1.1;
            font-size: 11pt;
        }

        /* Authentic airline font hierarchy - ALL CAPS and bold */
        .header-large {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 14pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .header-medium {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 12pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
        }
        .subheader {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 11pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
        }
        .body-text {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 10pt;
            color: #000;
            font-weight: normal;
        }
        .small-text {
            font-family: <PERSON><PERSON>, Helvetica, sans-serif;
            font-size: 9pt;
            color: #000;
        }
        .tiny-text {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 8pt;
            color: #000;
        }

        /* Layout structure - tighter spacing */
        .ticket-container {
            max-width: 750px;
            margin: 0 auto;
            background: white;
        }

        /* Sharp rectangular flight segment boxes - authentic airline style */
        .flight-segment {
            border: 1px solid #cccccc;
            margin: 5px 0;
            background: white;
            display: table;
            width: 100%;
            border-collapse: separate;
            border-bottom: 1px solid #cccccc;
            margin-bottom: 5px;
        }

        /* Left-side airline branding panel - more prominent */
        .airline-branding-panel {
            display: table-cell;
            width: 160px;
            background: #f8f8f8;
            border-right: 1px solid #cccccc;
            padding: 2px 6px;
            vertical-align: middle;
            text-align: left;
        }

        .airline-branding-panel .airline-name-block {
            text-align: center;
            margin-bottom: 8px;
        }

        .airline-branding-panel .airline-name {
            font-size: 12pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
            margin-bottom: 2px;
            line-height: 1.0;
        }

        .airline-branding-panel .flight-number {
            font-size: 12pt;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
            line-height: 1.0;
        }

        .airline-branding-panel .flight-details {
            margin-top: 8px;
            font-size: 9pt;
            color: #000;
        }

        /* Flight details grid - authentic airline system layout */
        .flight-details-panel {
            display: table-cell;
            padding: 0;
            vertical-align: top;
        }

        .flight-info-grid {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .flight-info-grid, .flight-info-grid th, .flight-info-grid td {
            border: 1px solid #cccccc;
            border-collapse: collapse;
        }

        .flight-info-grid td {
            font-family: Arial, Helvetica, sans-serif;
            padding: 2px 6px;
            vertical-align: middle;
            font-size: 13px;
            font-weight: 400;
            color: #000;
        }

        .flight-info-grid .info-label {
            font-family: Arial, Helvetica, sans-serif;
            background: #f8f8f8;
            font-weight: 500;
            color: #000;
            text-transform: uppercase;
            font-size: 13px;
            width: 25%;
        }

        .flight-info-grid .info-value {
            background: white;
            color: #000;
            font-weight: 400;
        }

        .flight-info-grid .time-large {
            font-size: 14pt;
            font-weight: bold;
            color: #000;
        }

        /* Additional spacing and density adjustments */
        .flight-info-grid th {
            font-family: Arial, Helvetica, sans-serif;
            padding: 2px 6px;
            background: #f8f8f8;
            font-weight: 500;
            color: #000;
            text-transform: uppercase;
            font-size: 13px;
            vertical-align: middle;
        }

        /* Ensure consistent table cell heights */
        .flight-info-grid td,
        .flight-info-grid th {
            height: 24px;
            line-height: 1.2;
        }

        /* Remove any rounded corners or modern styling */
        * {
            border-radius: 0 !important;
        }

        /* Monochrome status styling - no colors */
        .status-confirmed {
            color: #000;
            font-weight: normal;
            background: white;
            padding: 0;
            text-transform: uppercase;
            font-size: 13px;
        }

        /* Tiny disclaimer text at bottom */
        .disclaimer-text {
            font-size: 7pt;
            color: #666;
            text-align: center;
            margin-top: 20px;
            line-height: 1.2;
        }

        /* Route summary - authentic airline style */
        .route-summary {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 11pt;
            font-weight: 500;
            color: #000;
            margin: 6px 0 5px 0;
            padding: 2px 6px;
            background: #f8f8f8;
            border: 1px solid #cccccc;
            border-left: 1px solid #cccccc;
            text-transform: uppercase;
        }

        /* Departure header - stark official style */
        .departure-header {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 12pt;
            font-weight: 500;
            color: #000;
            margin: 6px 0 5px 0;
            padding: 2px 6px;
            background: #f8f8f8;
            border-bottom: 1px solid #cccccc;
            text-transform: uppercase;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .departure-header .left-content {
            display: flex;
            align-items: center;
        }

        .departure-header .airplane {
            font-size: 12pt;
            font-weight: normal;
            color: #000;
            margin-right: 8px;
        }

        .departure-header .verify-text {
            font-size: 9pt;
            font-weight: normal;
            color: #888888;
            text-transform: none;
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
        }



        @media print {
            body { margin: 0; padding: 15px; }
            .ticket-container { max-width: none; }
        }
    </style>
</head>
<body>
    <div class="ticket-container">
        <!-- Header - Authentic airline style -->
        <div class="header-large" style="text-align: left; margin-bottom: 12px; padding-bottom: 6px; border-bottom: 1px solid #cccccc;">
            21 JUL 2025 • 28 JUL 2025 &nbsp;&nbsp;&nbsp; TRIP TO PARIS CHARLES DE GAULLE
        </div>

        <!-- Prepared For Section -->
        <div style="margin: 10px 0;">
            <div class="subheader" style="margin-bottom: 6px;">PREPARED FOR</div>
            
                <div class="subheader" style="margin: 2px 0;">JOHN/DOE</div>
            
        </div>

        <!-- Reservation Codes -->
        <div style="margin: 10px 0;">
            <div class="body-text" style="margin: 2px 0;"><strong>STATUS:</strong> <span style="font-weight: normal; text-transform: uppercase;">CONFIRMED</span></div>
            <div class="body-text" style="margin: 2px 0;"><strong>RESERVATION CODE:</strong> HTMLTEST123</div>
            <div class="body-text" style="margin: 2px 0;"><strong>AIRLINE RESERVATION CODE:</strong> AF123VW</div>
        </div>

        <!-- Flight Segments -->
        
            <!-- Route Summary -->
            <div class="route-summary">
                ROUTE MAN – CDG &nbsp;&nbsp;&nbsp; Duration: 2H 50M &nbsp;&nbsp;&nbsp; Stop(s): 0
            </div>

            <!-- Departure Header -->
            <div class="departure-header">
                <div class="left-content">
                    <span class="airplane">✈</span> DEPARTURE: MONDAY, JUL 21
                </div>
                <div class="verify-text">
                    Please verify flight times prior to departure
                </div>
            </div>

            <!-- Flight Segment Box with Left-Side Airline Branding -->
            <div class="flight-segment">
                <!-- Left-Side Airline Branding Panel -->
                <div class="airline-branding-panel">
                    <div style="text-align: center; margin-bottom: 6px;">
                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGCAYAAABxLuKEAAADjUlEQVR4AezQMQEAAAzDoPk3nRmoBA4McNWABDFixIgRI0aMGDFixCBGjBgxYsSIESNGjBjEiBHztNgDrO1YFIDh0bNtu+7Ytm3bE4xt+9m2bdu2bZvr/Un6kqbp7d37IMkXFOu2/z3Yp8l2mt4b+BHfKfgZTysPL+8/nh9FUSRQUER0zr8ARdJUFBdpRnkWoukW1ZuqjBlYhZVYhUWwNcI0wNLQjFSswgMaUapjM0TDV8K5qjf1PyTGKFyoOMOHZMALilHOxyCIhnG4SCkMF+LgECQHrymG8XACkqbnFcO8B9GwH0ZwvlKY3pAE21AljTDbsUHRFjysEMXCfoiGN8IzcruZWyAKOqcR5g6UUFQKeXOJkgcTIRr6Reck3UgeTIGELEQHSIx7UwxzeYa/mr+BaNiCijphnoVEvIni2AmJWIFiKYS5KmNRDO9KHIOoIsoTygu8YM2wAhKyGkWD/Z9BYvyRQphLMhFlu+UV3m548yEa2gTnK4f5HBLxbmh/YSyFRBzHlZphhqMDOuWiM9qictz8bZb7B0TDyu22V0o5TLCY2wUJmYnzI8c9jVOQiCnIox5Gmx2dvcm379zi2acgik5vcZzbg/OVwzSGRKxET/QP9MNgHIfE+CBLYY7DDM9de7lZZv2l1gqIqg2XWPFv+ZC41ekRSJp2o3Y2wpSNhFl9hdEOomHmusvNgrph+kEypI9imPdwk4KbcUMB/6lC52Yuusp4csmVhmg4vOxK4zKtxw780dshEcewA7sS7MQZSIynFML4wn5dM3y72uzLrc0QVXOvMD9VnX/uoi+KWcydxuOohhoJquN7SIx1KJNLmKt1o4yt55ScYDoTJliuqHFkou0MmnGxnUc3zCtJbwWF5X4hLIbEaJTJMH0tq/Lg+s64wfUcUVbXmTGqnlta6wkeF1Yaa2NeLVdoPoh6EJKDq1E3h7fcNSrzW/r1ynT0G7zdxTW2Q1R0RlfX6NrXsEsGc7TCfISj2Bc4hGa6g0K/xA9hX8hBDMGl2Ib9kf2XJ838365TCF83duoOx6ZGTp31WJlgFZY2cev2bebVCT/U0g7zCjqjTaAT7k8xzHPohDYh7fAnLDRDW7RB20DtpJn/EgYf/GvV/gO/4Vt8k+A7fN7QOSs1t+kGqhyAduyQAAAAAEDQ/9eeMBI8QNQMXwIDBgwYMGDAgAEDBozAgAEDBgwYMGDAgAGjAIcOkVB0M3tvAAAAAElFTkSuQmCC" alt="AIR FRANCE" style="width: 70px; height: 45px; display: block; margin: 0 auto 2px auto;" />
                    </div>
                    <div class="airline-name-block">
                        <div class="airline-name">AIR FRANCE</div>
                        <div class="flight-number">AF 1669</div>
                    </div>
                    <div class="flight-details">
                        <div>DURATION: 2H 50M</div>
                        <div>CLASS: Economy Class (M)</div>
                        <div>STATUS: <span style="font-weight: normal; text-transform: uppercase;">CONFIRMED</span></div>
                    </div>
                </div>

                <!-- Flight Details Panel -->
                <div class="flight-details-panel">
                    <!-- Airport and Time Information Grid -->
                    <table class="flight-info-grid">
                        <tr>
                            <td class="info-label">MAN</td>
                            <td class="info-value"><strong>Departure City</strong></td>
                            <td class="info-label">CDG</td>
                            <td class="info-value"><strong>Arrival City</strong></td>
                        </tr>
                        <tr>
                            <td class="info-label">DEPARTING AT:</td>
                            <td class="info-value">
                                <div class="time-large">TIME</div>
                                <div class="small-text">TERMINAL: 1</div>
                            </td>
                            <td class="info-label">ARRIVING AT:</td>
                            <td class="info-value">
                                <div class="time-large">TIME</div>
                                <div class="small-text">TERMINAL: 1</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="info-label">AIRCRAFT:</td>
                            <td class="info-value">BOEING 737-800</td>
                            <td class="info-label">DISTANCE (IN MILES):</td>
                            <td class="info-value">Not Available</td>
                        </tr>
                        <tr>
                            <td class="info-label">STOP(S):</td>
                            <td class="info-value">0</td>
                            <td class="info-label">MEALS:</td>
                            <td class="info-value">Not Available</td>
                        </tr>
                    </table>

                    <!-- Passenger Information -->
                    <div style="margin-top: 6px; border-top: 1px solid #cccccc; padding-top: 6px;">
                        <table class="flight-info-grid">
                            <thead>
                                <tr style="background: #f8f8f8;">
                                    <th class="info-label" style="font-weight: 500;">PASSENGER NAME:</th>
                                    <th class="info-label" style="font-weight: 500;">SEATS:</th>
                                    <th class="info-label" style="font-weight: 500;">BOOKING:</th>
                                </tr>
                            </thead>
                            <tbody>
                                
                                    <tr>
                                        <td class="info-value"><strong>JOHN/DOE</strong></td>
                                        <td class="info-value">Check-in required</td>
                                        <td class="info-value"><span class="status-confirmed">CONFIRMED</span></td>
                                    </tr>
                                
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Route Summary -->
            <div class="route-summary">
                ROUTE CDG – MAN &nbsp;&nbsp;&nbsp; Duration: 2H 50M &nbsp;&nbsp;&nbsp; Stop(s): 0
            </div>

            <!-- Departure Header -->
            <div class="departure-header">
                <div class="left-content">
                    <span class="airplane">✈</span> RETURN: MONDAY, JUL 28
                </div>
                <div class="verify-text">
                    Please verify flight times prior to departure
                </div>
            </div>

            <!-- Flight Segment Box with Left-Side Airline Branding -->
            <div class="flight-segment">
                <!-- Left-Side Airline Branding Panel -->
                <div class="airline-branding-panel">
                    <div style="text-align: center; margin-bottom: 6px;">
                        <img src="data:image/png;base64,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" alt="ETHIOPIAN AIRLINES" style="width: 70px; height: 45px; display: block; margin: 0 auto 2px auto;" />
                    </div>
                    <div class="airline-name-block">
                        <div class="airline-name">ETHIOPIAN AIRLINES</div>
                        <div class="flight-number">ET 900</div>
                    </div>
                    <div class="flight-details">
                        <div>DURATION: 2H 50M</div>
                        <div>CLASS: Economy Class (M)</div>
                        <div>STATUS: <span style="font-weight: normal; text-transform: uppercase;">CONFIRMED</span></div>
                    </div>
                </div>

                <!-- Flight Details Panel -->
                <div class="flight-details-panel">
                    <!-- Airport and Time Information Grid -->
                    <table class="flight-info-grid">
                        <tr>
                            <td class="info-label">CDG</td>
                            <td class="info-value"><strong>Departure City</strong></td>
                            <td class="info-label">MAN</td>
                            <td class="info-value"><strong>Arrival City</strong></td>
                        </tr>
                        <tr>
                            <td class="info-label">DEPARTING AT:</td>
                            <td class="info-value">
                                <div class="time-large">TIME</div>
                                <div class="small-text">TERMINAL: 1</div>
                            </td>
                            <td class="info-label">ARRIVING AT:</td>
                            <td class="info-value">
                                <div class="time-large">TIME</div>
                                <div class="small-text">TERMINAL: 1</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="info-label">AIRCRAFT:</td>
                            <td class="info-value">BOEING 737-800</td>
                            <td class="info-label">DISTANCE (IN MILES):</td>
                            <td class="info-value">Not Available</td>
                        </tr>
                        <tr>
                            <td class="info-label">STOP(S):</td>
                            <td class="info-value">0</td>
                            <td class="info-label">MEALS:</td>
                            <td class="info-value">Not Available</td>
                        </tr>
                    </table>

                    <!-- Passenger Information -->
                    <div style="margin-top: 6px; border-top: 1px solid #cccccc; padding-top: 6px;">
                        <table class="flight-info-grid">
                            <thead>
                                <tr style="background: #f8f8f8;">
                                    <th class="info-label" style="font-weight: 500;">PASSENGER NAME:</th>
                                    <th class="info-label" style="font-weight: 500;">SEATS:</th>
                                    <th class="info-label" style="font-weight: 500;">BOOKING:</th>
                                </tr>
                            </thead>
                            <tbody>
                                
                                    <tr>
                                        <td class="info-value"><strong>JOHN/DOE</strong></td>
                                        <td class="info-value">Check-in required</td>
                                        <td class="info-value"><span class="status-confirmed">CONFIRMED</span></td>
                                    </tr>
                                
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            

        <!-- Tiny disclaimer text at bottom -->
        


    </div>
</body>
</html>