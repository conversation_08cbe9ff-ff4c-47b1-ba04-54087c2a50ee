#!/usr/bin/env node

/**
 * Comprehensive Checkout Test Script
 * Tests all aspects of the checkout functionality
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5173';
const API_URL = 'http://localhost:5174/api';

async function testCheckoutComprehensive() {
  console.log('🧪 Running comprehensive checkout tests...\n');
  
  let allTestsPassed = true;
  const results = [];

  // Test 1: Frontend server availability
  console.log('📡 Test 1: Frontend server availability...');
  try {
    const response = await axios.get(BASE_URL, { timeout: 5000 });
    if (response.status === 200) {
      console.log('✅ Frontend server is running');
      results.push({ test: 'Frontend Server', status: 'PASS' });
    } else {
      throw new Error(`Unexpected status: ${response.status}`);
    }
  } catch (error) {
    console.log('❌ Frontend server test failed:', error.message);
    results.push({ test: 'Frontend Server', status: 'FAIL', error: error.message });
    allTestsPassed = false;
  }

  // Test 2: API server availability
  console.log('\n📡 Test 2: API server availability...');
  try {
    const response = await axios.get(`${API_URL}/health`, { timeout: 5000 });
    if (response.data.status === 'OK') {
      console.log('✅ API server is running');
      results.push({ test: 'API Server', status: 'PASS' });
    } else {
      throw new Error('API health check failed');
    }
  } catch (error) {
    console.log('❌ API server test failed:', error.message);
    results.push({ test: 'API Server', status: 'FAIL', error: error.message });
    allTestsPassed = false;
  }

  // Test 3: Checkout page accessibility
  console.log('\n🌐 Test 3: Checkout page accessibility...');
  try {
    const response = await axios.get(`${BASE_URL}/checkout`, { timeout: 10000 });
    if (response.status === 200 && response.data.includes('Complete Your Booking')) {
      console.log('✅ Checkout page loads successfully');
      results.push({ test: 'Checkout Page Load', status: 'PASS' });
    } else {
      throw new Error('Checkout page content not found');
    }
  } catch (error) {
    console.log('❌ Checkout page test failed:', error.message);
    results.push({ test: 'Checkout Page Load', status: 'FAIL', error: error.message });
    allTestsPassed = false;
  }

  // Test 4: Checkout debug page
  console.log('\n🔧 Test 4: Checkout debug page...');
  try {
    const response = await axios.get(`${BASE_URL}/checkout-debug`, { timeout: 10000 });
    if (response.status === 200 && response.data.includes('Checkout Debug Console')) {
      console.log('✅ Checkout debug page loads successfully');
      results.push({ test: 'Checkout Debug Page', status: 'PASS' });
    } else {
      throw new Error('Checkout debug page content not found');
    }
  } catch (error) {
    console.log('❌ Checkout debug page test failed:', error.message);
    results.push({ test: 'Checkout Debug Page', status: 'FAIL', error: error.message });
    allTestsPassed = false;
  }

  // Test 5: Flight search API
  console.log('\n✈️ Test 5: Flight search API...');
  try {
    const response = await axios.post(`${API_URL}/flights/search`, {
      origin: 'LHR',
      destination: 'JFK',
      date: '2025-08-15',
      tripType: 'oneWay'
    }, { timeout: 15000 });
    
    if (response.data.success && response.data.data.flights.length > 0) {
      console.log(`✅ Flight search API working (found ${response.data.data.flights.length} flights)`);
      results.push({ test: 'Flight Search API', status: 'PASS' });
    } else {
      throw new Error('No flights found or API returned error');
    }
  } catch (error) {
    console.log('❌ Flight search API test failed:', error.message);
    results.push({ test: 'Flight Search API', status: 'FAIL', error: error.message });
    allTestsPassed = false;
  }

  // Test 6: Payment intent creation
  console.log('\n💳 Test 6: Payment intent creation...');
  try {
    const response = await axios.post(`${API_URL}/payments/stripe/create-intent`, {
      amount: 4.99,
      currency: 'usd',
      metadata: { description: 'Test payment' }
    }, { timeout: 10000 });
    
    if (response.data.clientSecret) {
      console.log('✅ Payment intent creation working');
      results.push({ test: 'Payment Intent', status: 'PASS' });
    } else {
      throw new Error('No client secret returned');
    }
  } catch (error) {
    console.log('❌ Payment intent test failed:', error.message);
    results.push({ test: 'Payment Intent', status: 'FAIL', error: error.message });
    allTestsPassed = false;
  }

  // Test 7: Ticket generation
  console.log('\n🎫 Test 7: Ticket generation...');
  try {
    const ticketData = {
      bookingReference: 'TEST123',
      passengers: [{ firstName: 'Test', lastName: 'User' }],
      email: '<EMAIL>',
      flight: {
        number: 'BA 123',
        departure: { airport: 'LHR', city: 'London', time: '2025-08-15 10:00' },
        arrival: { airport: 'JFK', city: 'New York', time: '2025-08-15 18:00' }
      },
      airline: { name: 'British Airways', code: 'BA' }
    };

    const response = await axios.post(`${API_URL}/tickets/generate`, ticketData, { 
      timeout: 20000,
      responseType: 'json'
    });
    
    if (response.data.success || response.data.bookingReference) {
      console.log('✅ Ticket generation working');
      results.push({ test: 'Ticket Generation', status: 'PASS' });
    } else {
      throw new Error('Ticket generation failed');
    }
  } catch (error) {
    console.log('❌ Ticket generation test failed:', error.message);
    results.push({ test: 'Ticket Generation', status: 'FAIL', error: error.message });
    allTestsPassed = false;
  }

  // Test 8: Checkout bypass mode
  console.log('\n🎭 Test 8: Checkout bypass mode...');
  try {
    const response = await axios.get(`${BASE_URL}/checkout?bypass=true`, { timeout: 10000 });
    if (response.status === 200) {
      console.log('✅ Checkout bypass mode accessible');
      results.push({ test: 'Checkout Bypass Mode', status: 'PASS' });
    } else {
      throw new Error('Bypass mode not accessible');
    }
  } catch (error) {
    console.log('❌ Checkout bypass mode test failed:', error.message);
    results.push({ test: 'Checkout Bypass Mode', status: 'FAIL', error: error.message });
    allTestsPassed = false;
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(60));
  
  const passedTests = results.filter(r => r.status === 'PASS').length;
  const failedTests = results.filter(r => r.status === 'FAIL').length;
  
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📈 Success Rate: ${Math.round((passedTests / results.length) * 100)}%`);
  
  console.log('\n📋 Detailed Results:');
  results.forEach(result => {
    const status = result.status === 'PASS' ? '✅' : '❌';
    console.log(`  ${status} ${result.test}`);
    if (result.error) {
      console.log(`     Error: ${result.error}`);
    }
  });

  if (allTestsPassed) {
    console.log('\n🎉 ALL TESTS PASSED! Checkout system is fully operational.');
    console.log('\n🚀 Ready for production use:');
    console.log(`   • Checkout page: ${BASE_URL}/checkout`);
    console.log(`   • Debug console: ${BASE_URL}/checkout-debug`);
    console.log(`   • Bypass mode: ${BASE_URL}/checkout?bypass=true`);
  } else {
    console.log('\n⚠️  Some tests failed. Please review the errors above.');
    console.log('\n🔧 Troubleshooting tips:');
    console.log('   • Ensure both frontend (5173) and backend (5174) servers are running');
    console.log('   • Check API connectivity and CORS settings');
    console.log('   • Verify environment variables are set correctly');
  }

  return allTestsPassed;
}

// Run the tests
if (require.main === module) {
  testCheckoutComprehensive()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('🚨 Test runner error:', error);
      process.exit(1);
    });
}

module.exports = { testCheckoutComprehensive };
