#!/usr/bin/env node

/**
 * Comprehensive test script to verify flight search functionality
 * This script tests both backend API and frontend-backend integration
 */

const axios = require('axios');

const BACKEND_URL = 'http://localhost:5001';
const FRONTEND_URL = 'http://localhost:5173';

async function testBackendHealth() {
  console.log('🔍 Testing backend health...');
  try {
    const response = await axios.get(`${BACKEND_URL}/api/health`);
    console.log('✅ Backend health check passed:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Backend health check failed:', error.message);
    return false;
  }
}

async function testAirportSearch() {
  console.log('🔍 Testing airport search...');
  try {
    const response = await axios.get(`${BACKEND_URL}/api/flights/airports`, {
      params: { query: 'London' }
    });
    console.log('✅ Airport search passed. Found airports:', response.data.data?.length || 0);
    return true;
  } catch (error) {
    console.error('❌ Airport search failed:', error.message);
    return false;
  }
}

async function testFlightSearch() {
  console.log('🔍 Testing flight search...');
  try {
    const response = await axios.post(`${BACKEND_URL}/api/flights/search`, {
      origin: 'LHR',
      destination: 'JFK',
      date: '2025-07-15',
      tripType: 'oneWay'
    });
    
    if (response.data.success) {
      console.log('✅ Flight search passed. Found flights:', response.data.data?.flights?.length || 0);
      return true;
    } else {
      console.error('❌ Flight search failed: No success flag in response');
      return false;
    }
  } catch (error) {
    console.error('❌ Flight search failed:', error.message);
    console.error('Error details:', {
      status: error.response?.status,
      data: error.response?.data
    });
    return false;
  }
}

async function testFrontendConnection() {
  console.log('🔍 Testing frontend connection...');
  try {
    const response = await axios.get(FRONTEND_URL);
    console.log('✅ Frontend is accessible');
    return true;
  } catch (error) {
    console.error('❌ Frontend connection failed:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting comprehensive flight search tests...\n');
  
  const tests = [
    { name: 'Backend Health', test: testBackendHealth },
    { name: 'Airport Search', test: testAirportSearch },
    { name: 'Flight Search', test: testFlightSearch },
    { name: 'Frontend Connection', test: testFrontendConnection }
  ];
  
  let passedTests = 0;
  
  for (const { name, test } of tests) {
    console.log(`\n--- ${name} ---`);
    const passed = await test();
    if (passed) {
      passedTests++;
    }
    console.log('');
  }
  
  console.log('📊 Test Results:');
  console.log(`✅ Passed: ${passedTests}/${tests.length}`);
  console.log(`❌ Failed: ${tests.length - passedTests}/${tests.length}`);
  
  if (passedTests === tests.length) {
    console.log('\n🎉 All tests passed! Flight search should be working correctly.');
    console.log('\n💡 If you\'re still seeing errors in the frontend:');
    console.log('   1. Clear browser cache and refresh');
    console.log('   2. Check browser console for JavaScript errors');
    console.log('   3. Ensure you\'re selecting airports from the dropdown (not just typing)');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the errors above.');
    console.log('\n🔧 Troubleshooting steps:');
    console.log('   1. Make sure backend is running: cd backend && npm start');
    console.log('   2. Make sure frontend is running: cd frontend && npm run dev');
    console.log('   3. Check that ports 5001 (backend) and 5173 (frontend) are available');
  }
}

// Run the tests
runAllTests().catch(console.error);
