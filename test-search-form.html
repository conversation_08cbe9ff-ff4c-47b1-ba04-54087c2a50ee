<!DOCTYPE html>
<html>
<head>
    <title>Test Search Form</title>
</head>
<body>
    <h1>Test Flight Search</h1>
    
    <script>
        // Test the search functionality
        async function testSearch() {
            console.log('🧪 Testing search form submission...');
            
            try {
                const response = await fetch('http://localhost:5001/api/flights/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        origin: 'MAN',
                        destination: 'MAD',
                        date: '2025-07-14',
                        tripType: 'oneWay'
                    })
                });
                
                const data = await response.json();
                console.log('✅ Search response:', data);
                
                if (data.success && data.data && data.data.flights) {
                    console.log('✅ Found', data.data.flights.length, 'flights');
                    console.log('First flight:', data.data.flights[0]);
                } else {
                    console.log('❌ No flights found or error');
                }
                
            } catch (error) {
                console.error('❌ Search test failed:', error);
            }
        }
        
        // Run test on page load
        testSearch();
    </script>
</body>
</html>
